/** @type {import('tailwindcss').Config} */
export default {
  content: ['./app/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        // RTG Brand Colors
        'rtg-primary': '#F5D6A1',
        'rtg-primary-400': '#E9C584',
        'rtg-accent': '#C49A6C',
        'rtg-secondary': '#8C6239',
        'rtg-secondary-dark': '#5C4024',
        'rtg-neutral-dark': '#3B2A1A',
        'rtg-neutral-light': '#F9F5F0',
        'rtg-black': '#1C1C1C',
        'rtg-white': '#FFFFFF',

        // Sidebar Colors
        'rtg-sidebar-bg': '#3B2A1A',
        'rtg-sidebar-bg-2': '#2F2416',
        'rtg-sidebar-text': '#FFFFFF',
        'rtg-sidebar-muted': '#BFA88A',
        'rtg-sidebar-active': '#F5D6A1',

        // Surface Colors
        'rtg-page-bg': '#F9F5F0',
        'rtg-card-bg': '#FFFFFF',
        'rtg-card-border': '#EAD9BF',
        'rtg-muted-surface': '#FCF8F2',

        // Text Colors
        'rtg-text-primary': '#1C1C1C',
        'rtg-text-secondary': '#5C4024',
        'rtg-text-muted': '#8A6A52',
        'rtg-text-inverted': '#FFFFFF',

        // Status Colors
        'rtg-success': '#10B981',
        'rtg-danger': '#EF4444',
        'rtg-warning': '#F59E0B',
        'rtg-info': '#0EA5E9',

        // Subtle Colors
        'rtg-light-1': '#FEFBF7',
        'rtg-light-2': '#FFF7ED',
        'rtg-border': '#F0E1CB',
        'rtg-divider': '#EDE0CF',
      },
      fontFamily: {
        sans: ['Poppins', 'system-ui', '-apple-system', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
      },
      spacing: {
        // RTG Spacing Scale
        'rtg-xs': '4px',
        'rtg-sm': '8px',
        'rtg-md': '16px',
        'rtg-lg': '24px',
        'rtg-xl': '32px',
        'rtg-xxl': '48px',
        'rtg-xxx': '64px',

        // Component Specific
        'rtg-gutter': '24px',
        'rtg-card-padding': '20px',
        'rtg-form-gap': '12px',
      },
      borderRadius: {
        'rtg-card': '12px',
        'rtg-button': '8px',
        'rtg-input': '6px',
      },
      boxShadow: {
        // RTG Shadow System
        'rtg-elevation-1': '0px 2px 8px rgba(60, 42, 26, 0.04)',
        'rtg-elevation-2': '0px 4px 16px rgba(60, 42, 26, 0.08)',
        'rtg-elevation-3': '0px 8px 24px rgba(60, 42, 26, 0.12)',
        'rtg-kpi': '0px 10px 30px rgba(196, 154, 108, 0.08)',
        'rtg-focus': '0 0 0 6px rgba(196, 154, 108, 0.06)',
      },
      backgroundImage: {
        // RTG Gradients
        'rtg-primary': 'linear-gradient(135deg, #F5D6A1 0%, #C49A6C 100%)',
        'rtg-secondary': 'linear-gradient(135deg, #8C6239 0%, #3B2A1A 100%)',
        'rtg-accent': 'linear-gradient(135deg, #C49A6C 0%, #F5D6A1 100%)',
        'rtg-hero': 'linear-gradient(120deg, #F5D6A1 0%, #8C6239 50%, #3B2A1A 100%)',
        'rtg-soft-beige': 'linear-gradient(180deg, rgba(245,214,161,0.08), rgba(245,214,161,0.02))',
        'rtg-sidebar-active': 'linear-gradient(90deg, rgba(245,214,161,0.08), rgba(196,154,108,0.03))',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in-right': 'slideInRight 0.3s ease-out',
        'slide-in-left': 'slideInLeft 0.3s ease-out',
        'bounce-in': 'bounceIn 0.6s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        bounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
