<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { notificationStore, type EnhancedNotification } from '$lib/stores/notificationStore';
	import { CheckCircle, AlertCircle, XCircle, Info, Clock, X, Bell, BellOff } from '@lucide/svelte';
	import { onMount } from 'svelte';
	import { fly } from 'svelte/transition';

	interface Props {
		isOpen?: boolean;
		onClose?: () => void;
		className?: string;
	}

	let {
		isOpen = false,
		onClose,
		className = ''
	}: Props = $props();

	let notifications = $state<EnhancedNotification[]>([]);
	let readNotifications = $state<Set<string>>(new Set());

	// Subscribe to notifications
	$effect(() => {
		console.log('Setting up notification subscription');
		const unsubscribe = notificationStore.subscribe(newNotifications => {
			console.log('Notification store updated:', newNotifications.length, newNotifications);
			notifications = newNotifications;
		});
		return unsubscribe;
	});

	// Load read notifications from localStorage
	onMount(() => {
		try {
			const stored = localStorage.getItem('hrims-read-notifications');
			if (stored) {
				readNotifications = new Set(JSON.parse(stored));
			}
		} catch (error) {
			console.warn('Failed to load read notifications:', error);
		}
	});

	// Save read notifications to localStorage
	const saveReadNotifications = () => {
		try {
			localStorage.setItem('hrims-read-notifications', JSON.stringify([...readNotifications]));
		} catch (error) {
			console.warn('Failed to save read notifications:', error);
		}
	};

	// Mark notification as read
	const markAsRead = (id: string) => {
		readNotifications.add(id);
		readNotifications = readNotifications; // Trigger reactivity
		saveReadNotifications();
	};

	// Mark all notifications as read
	const markAllAsRead = () => {
		notifications.forEach(n => readNotifications.add(n.id));
		readNotifications = readNotifications; // Trigger reactivity
		saveReadNotifications();
	};

	// Get icon component for notification type
	const getIcon = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return CheckCircle;
			case 'error': return XCircle;
			case 'warning': return AlertCircle;
			case 'info': return Info;
		}
	};

	// Get notification type color classes
	const getTypeClasses = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
			case 'error': return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
			case 'warning': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
			case 'info': return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
		}
	};

	// Group notifications by type
	const groupedNotifications = $derived(() => {
		console.log('Grouping notifications:', notifications.length, notifications);
		const groups: Record<string, EnhancedNotification[]> = {
			critical: [],
			error: [],
			warning: [],
			success: [],
			info: []
		};

		notifications.forEach(notification => {
			if (notification.priority === 'critical') {
				groups.critical.push(notification);
			} else {
				groups[notification.type].push(notification);
			}
		});

		// Sort each group by timestamp (newest first)
		Object.keys(groups).forEach(key => {
			groups[key].sort((a, b) => b.timestamp - a.timestamp);
		});

		console.log('Grouped notifications:', groups);
		return groups;
	});

	// Count unread notifications
	const unreadCount = $derived(() => {
		console.log('Calculating unread count for notifications:', notifications.length);
		return notifications.filter(n => !readNotifications.has(n.id)).length;
	});

	// Handle notification click
	const handleNotificationClick = (notification: EnhancedNotification) => {
		markAsRead(notification.id);
		if (notification.action?.onClick) {
			notification.action.onClick();
		}
	};

	// Handle remove notification
	const handleRemoveNotification = (id: string, event: Event) => {
		event.stopPropagation();
		notificationStore.remove(id);
	};

	// Handle keyboard navigation
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && isOpen) {
			onClose?.();
		}
	};

	// Format timestamp
	const formatTimestamp = (timestamp: number) => {
		const now = Date.now();
		const diff = now - timestamp;
		const minutes = Math.floor(diff / 60000);
		const hours = Math.floor(diff / 3600000);
		const days = Math.floor(diff / 86400000);

		if (minutes < 1) return 'Just now';
		if (minutes < 60) return `${minutes}m ago`;
		if (hours < 24) return `${hours}h ago`;
		return `${days}d ago`;
	};
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- Overlay -->
{#if isOpen}
	<div 
		class="fixed inset-0 bg-black/20 z-40 lg:hidden"
		onclick={onClose}
		transition:fly={{ x: 300, duration: 200 }}
	></div>
{/if}

<!-- Sidebar -->
<aside
	class={cn(
		'fixed top-0 right-0 h-full bg-background border-l border-border z-50 flex flex-col',
		'transform transition-transform duration-300 ease-in-out',
		isOpen ? 'translate-x-0' : 'translate-x-full',
		'w-80 lg:w-96',
		className
	)}
	role="complementary"
	aria-label="Notifications"
	aria-hidden={!isOpen}
>
	<!-- Header -->
	<div class="flex items-center justify-between p-4 border-b border-border">
		<div class="flex items-center gap-2">
			<Bell class="w-5 h-5 text-muted-foreground" />
			<h2 class="text-lg font-semibold">Notifications</h2>
			{#if unreadCount > 0}
				<span class="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
					{unreadCount}
				</span>
			{/if}
		</div>
		<div class="flex items-center gap-2">
			{#if notifications.length > 0}
				<button
					class="text-sm text-muted-foreground hover:text-foreground transition-colors"
					onclick={markAllAsRead}
					title="Mark all as read"
				>
					Mark all read
				</button>
			{/if}
			<button
				class="p-1 hover:bg-muted rounded-md transition-colors"
				onclick={onClose}
				title="Close notifications"
			>
				<X class="w-4 h-4" />
			</button>
		</div>
	</div>

	<!-- Content -->
	<div class="flex-1 overflow-y-auto">
		{#if notifications.length === 0}
			<div class="flex flex-col items-center justify-center h-full text-center p-8">
				<BellOff class="w-12 h-12 text-muted-foreground mb-4" />
				<h3 class="text-lg font-medium text-muted-foreground mb-2">No notifications</h3>
				<p class="text-sm text-muted-foreground">You're all caught up!</p>
			</div>
		{:else}
			<div class="p-4 space-y-6">
				{#each Object.entries(groupedNotifications) as [type, typeNotifications]}
					{#if typeNotifications.length > 0}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-muted-foreground uppercase tracking-wide">
								{type === 'critical' ? 'Critical' : type.charAt(0).toUpperCase() + type.slice(1)}
								<span class="ml-1 text-xs">({typeNotifications.length})</span>
							</h3>
							
							{#each typeNotifications as notification (notification.id)}
								{@const IconComponent = getIcon(notification.type)}
								{@const isRead = readNotifications.has(notification.id)}
								
								<div
									class={cn(
										'relative p-3 rounded-lg border cursor-pointer transition-all duration-200',
										'hover:shadow-md hover:border-primary/20',
										isRead 
											? 'bg-muted/30 border-muted' 
											: 'bg-background border-border shadow-sm',
										notification.priority === 'critical' && 'border-destructive/50 bg-destructive/5'
									)}
									onclick={() => handleNotificationClick(notification)}
									role="button"
									tabindex="0"
									aria-label={`${notification.type} notification: ${notification.title}`}
								>
									<!-- Unread indicator -->
									{#if !isRead}
										<div class="absolute top-2 right-2 w-2 h-2 bg-primary rounded-full"></div>
									{/if}

									<div class="flex items-start gap-3">
										<!-- Icon -->
										<div class={cn(
											'flex-shrink-0 p-2 rounded-full',
											getTypeClasses(notification.type)
										)}>
											<IconComponent class="w-4 h-4" />
										</div>

										<!-- Content -->
										<div class="flex-1 min-w-0">
											<div class="flex items-start justify-between gap-2">
												<h4 class={cn(
													'font-medium text-sm',
													isRead ? 'text-muted-foreground' : 'text-foreground'
												)}>
													{notification.title}
												</h4>
												<button
													class="opacity-0 group-hover:opacity-100 p-1 hover:bg-muted rounded transition-all"
													onclick={(e) => handleRemoveNotification(notification.id, e)}
													title="Remove notification"
												>
													<X class="w-3 h-3" />
												</button>
											</div>
											
											{#if notification.message}
												<p class={cn(
													'text-xs mt-1 line-clamp-2',
													isRead ? 'text-muted-foreground/70' : 'text-muted-foreground'
												)}>
													{notification.message}
												</p>
											{/if}

											<div class="flex items-center justify-between mt-2">
												<span class="text-xs text-muted-foreground">
													{formatTimestamp(notification.timestamp)}
												</span>
												
												{#if notification.priority === 'critical'}
													<div class="flex items-center gap-1 text-xs text-destructive">
														<Clock class="w-3 h-3" />
														<span>Critical</span>
													</div>
												{/if}
											</div>

											{#if notification.action}
												<button
													class="mt-2 text-xs bg-primary text-primary-foreground px-2 py-1 rounded hover:bg-primary/90 transition-colors"
													onclick={(e) => {
														e.stopPropagation();
														handleNotificationClick(notification);
													}}
												>
													{notification.action.label}
												</button>
											{/if}
										</div>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				{/each}
			</div>
		{/if}
	</div>

	<!-- Footer -->
	{#if notifications.length > 0}
		<div class="p-4 border-t border-border">
			<button
				class="w-full text-sm text-center text-muted-foreground hover:text-foreground transition-colors"
				onclick={() => notificationStore.clear()}
			>
				Clear all notifications
			</button>
		</div>
	{/if}
</aside>

<style>
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
