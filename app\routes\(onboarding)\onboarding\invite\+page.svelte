<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Eye, EyeOff, CheckCircle } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);
	const current = $derived(store.steps.find(step => step.id === store.currentStepId));

	// Form state
	let inviteToken = '';
	let password = '';
	let confirmPassword = '';
	let showPassword = false;
	let showConfirmPassword = false;
	let isValidToken = false;
	let isLoading = false;

	// Validation
	let errors: Record<string, string> = {};

	// Check for invite token in URL
	onMount(() => {
		const token = $page.url.searchParams.get('token');
		if (token) {
			inviteToken = token;
			validateToken(token);
		}
	});

	const validateToken = async (token: string) => {
		isLoading = true;
		try {
			// Simulate token validation
			await new Promise(resolve => setTimeout(resolve, 1000));
			isValidToken = token.length > 10; // Simple validation
			if (isValidToken) {
				uiStore.showSuccess('Valid Invite', 'Your invitation token has been verified.');
			} else {
				uiStore.showError('Invalid Token', 'The invitation token is not valid.');
			}
		} catch (error) {
			uiStore.showError('Validation Error', 'Failed to validate invitation token.');
		} finally {
			isLoading = false;
		}
	};

	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		if (!inviteToken.trim()) newErrors.inviteToken = 'Invitation token is required';
		if (!isValidToken) newErrors.inviteToken = 'Please enter a valid invitation token';
		if (!password) newErrors.password = 'Password is required';
		if (password.length < 8) newErrors.password = 'Password must be at least 8 characters';
		if (password !== confirmPassword) newErrors.confirmPassword = 'Passwords do not match';

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	const handleNext = async () => {
		if (!validateForm()) {
			uiStore.showError('Validation Error', 'Please fix the errors below before continuing.');
			return;
		}

		isLoading = true;
		try {
			// Simulate account creation
			await new Promise(resolve => setTimeout(resolve, 1500));
			
			// Save account data
			onboardingStore.updateData('account', {
				inviteToken,
				passwordSet: true,
				accountCreated: new Date().toISOString()
			});

			// Mark step as completed and move to next
			onboardingStore.completeStep('invite-account');
			onboardingStore.setCurrentStep('company-intro');
			
			uiStore.showSuccess('Account Created', 'Your account has been successfully created!');
			goto('/onboarding/welcome');
		} catch (error) {
			uiStore.showError('Account Creation Failed', 'Failed to create your account. Please try again.');
		} finally {
			isLoading = false;
		}
	};

	const handleBack = () => {
		goto('/onboarding');
	};
</script>

<svelte:head>
	<title>Account Setup - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(step) => {
			if (step.status === 'completed') {
				onboardingStore.setCurrentStep(step.id);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Account Setup"
		subtitle="Welcome to Rainbow Tourism Group! Please set up your account using the invitation token sent to your email."
		showBackButton={true}
		showNextButton={true}
		nextButtonText={isLoading ? 'Creating Account...' : 'Create Account'}
		nextButtonDisabled={isLoading || !isValidToken}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-6">
			<!-- Invitation Token -->
			<div class="space-y-2">
				<Label for="inviteToken">Invitation Token *</Label>
				<div class="relative">
					<Input
						id="inviteToken"
						type="text"
						placeholder="Enter your invitation token"
						bind:value={inviteToken}
						on:blur={() => inviteToken && validateToken(inviteToken)}
						class="onboarding-input {errors.inviteToken ? 'border-red-500' : ''}"
						disabled={isLoading}
					/>
					{#if isValidToken}
						<CheckCircle class="absolute right-3 top-3 w-5 h-5 text-green-500" />
					{/if}
				</div>
				{#if errors.inviteToken}
					<p class="text-sm text-red-600">{errors.inviteToken}</p>
				{/if}
			</div>

			<!-- Password -->
			<div class="space-y-2">
				<Label for="password">Create Password *</Label>
				<div class="relative">
					<Input
						id="password"
						type={showPassword ? 'text' : 'password'}
						placeholder="Create a secure password"
						bind:value={password}
						class="onboarding-input pr-12 {errors.password ? 'border-red-500' : ''}"
						disabled={isLoading}
					/>
					<button
						type="button"
						class="absolute right-3 top-3 text-gray-500 hover:text-gray-700"
						onclick={() => showPassword = !showPassword}
					>
						{#if showPassword}
							<EyeOff class="w-5 h-5" />
						{:else}
							<Eye class="w-5 h-5" />
						{/if}
					</button>
				</div>
				{#if errors.password}
					<p class="text-sm text-red-600">{errors.password}</p>
				{/if}
				<p class="text-xs text-gray-600">Password must be at least 8 characters long</p>
			</div>

			<!-- Confirm Password -->
			<div class="space-y-2">
				<Label for="confirmPassword">Confirm Password *</Label>
				<div class="relative">
					<Input
						id="confirmPassword"
						type={showConfirmPassword ? 'text' : 'password'}
						placeholder="Confirm your password"
						bind:value={confirmPassword}
						class="onboarding-input pr-12 {errors.confirmPassword ? 'border-red-500' : ''}"
						disabled={isLoading}
					/>
					<button
						type="button"
						class="absolute right-3 top-3 text-gray-500 hover:text-gray-700"
						onclick={() => showConfirmPassword = !showConfirmPassword}
					>
						{#if showConfirmPassword}
							<EyeOff class="w-5 h-5" />
						{:else}
							<Eye class="w-5 h-5" />
						{/if}
					</button>
				</div>
				{#if errors.confirmPassword}
					<p class="text-sm text-red-600">{errors.confirmPassword}</p>
				{/if}
			</div>

			<!-- SSO Option -->
			<div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
				<h3 class="font-medium text-blue-900 mb-2">Alternative: Single Sign-On</h3>
				<p class="text-sm text-blue-700 mb-3">
					You can also sign in using your Microsoft/Azure account if your organization has SSO enabled.
				</p>
				<Button variant="outline" class="w-full" disabled={isLoading}>
					Continue with Microsoft SSO
				</Button>
			</div>
		</div>
	</OnboardingCard>
</div>

<!-- Auto-save indicator -->
{#if store.lastSaved}
	<div class="fixed bottom-4 right-4 text-xs text-gray-500 bg-white px-3 py-1 rounded-full shadow-sm">
		Last saved: {store.lastSaved.toLocaleTimeString()}
	</div>
{/if}
