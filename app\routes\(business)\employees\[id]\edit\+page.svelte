<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import { ArrowLeft, Save, X } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const employeeId = page.params.id;

	// Mock employee data - in real app, this would be fetched from API
	let formData = $state({
		employee_number: 'RTG001',
		first_name: '<PERSON>',
		last_name: '<PERSON><PERSON>',
		email: '<EMAIL>',
		phone: '+263 77 123 4567',
		position: 'Senior Software Developer',
		department: 'Information Technology',
		manager: '<PERSON>',
		hire_date: '2022-01-15',
		status: 'Active',
		salary: '75000',
		address: '123 Main Street, Harare, Zimbabwe',
		emergency_contact_name: 'Jane Doe',
		emergency_contact_phone: '+263 77 987 6543',
		skills: 'JavaScript, TypeScript, Svelte, Node.js, Python'
	});

	// Form validation errors
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Department options
	const departmentOptions = [
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Human Resources', label: 'Human Resources' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Operations', label: 'Operations' },
		{ value: 'Sales', label: 'Sales' }
	];

	// Status options
	const statusOptions = [
		{ value: 'Active', label: 'Active' },
		{ value: 'Inactive', label: 'Inactive' },
		{ value: 'On Leave', label: 'On Leave' },
		{ value: 'Terminated', label: 'Terminated' }
	];

	// Manager options (mock data)
	const managerOptions = [
		{ value: 'Sarah Wilson', label: 'Sarah Wilson' },
		{ value: 'Mike Johnson', label: 'Mike Johnson' },
		{ value: 'Emily Brown', label: 'Emily Brown' },
		{ value: 'David Smith', label: 'David Smith' }
	];

	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		// Required field validation
		if (!formData.first_name.trim()) {
			newErrors.first_name = 'First name is required';
		}
		if (!formData.last_name.trim()) {
			newErrors.last_name = 'Last name is required';
		}
		if (!formData.email.trim()) {
			newErrors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}
		if (!formData.employee_number.trim()) {
			newErrors.employee_number = 'Employee number is required';
		}
		if (!formData.position.trim()) {
			newErrors.position = 'Position is required';
		}
		if (!formData.department) {
			newErrors.department = 'Department is required';
		}
		if (!formData.hire_date) {
			newErrors.hire_date = 'Hire date is required';
		}

		// Phone validation
		if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
			newErrors.phone = 'Please enter a valid phone number';
		}

		// Salary validation
		if (formData.salary && !/^\d+(\.\d{2})?$/.test(formData.salary)) {
			newErrors.salary = 'Please enter a valid salary amount';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) {
			notificationStore.error('Validation Error', 'Please fix the errors below');
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			console.log('Updating employee:', employeeId, formData);
			
			notificationStore.success('Employee Updated', 'Employee information has been successfully updated');
			
			// Redirect back to employee detail page
			goto(`/employees/${employeeId}`);
		} catch (error) {
			console.error('Error updating employee:', error);
			notificationStore.error('Update Failed', 'Failed to update employee information');
		} finally {
			isSubmitting = false;
		}
	};

	const handleCancel = () => {
		goto(`/employees/${employeeId}`);
	};

	const canEdit = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin';

	// Redirect if user doesn't have permission
	if (!canEdit) {
		goto(`/employees/${employeeId}`);
	}
</script>

<svelte:head>
	<title>Edit Employee - {formData.first_name} {formData.last_name} - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleCancel}>
				<ArrowLeft class="w-4 h-4" />
				Back to Employee
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">Edit Employee</h1>
				<p class="text-muted-foreground">{formData.first_name} {formData.last_name} • {formData.employee_number}</p>
			</div>
		</div>
		
		<div class="flex gap-2">
			<Button variant="outline" onclick={handleCancel} disabled={isSubmitting}>
				<X class="w-4 h-4" />
				Cancel
			</Button>
			<Button variant="default" onclick={handleSubmit} disabled={isSubmitting}>
				<Save class="w-4 h-4" />
				{isSubmitting ? 'Saving...' : 'Save Changes'}
			</Button>
		</div>
	</div>

	<!-- Edit Form -->
	<Card class="p-6">
		<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
			<!-- Personal Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Personal Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="employee_number">Employee Number *</Label>
						<Input
							id="employee_number"
							bind:value={formData.employee_number}
							error={errors.employee_number}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="status">Status *</Label>
						<Select
							options={statusOptions}
							bind:value={formData.status}
							error={errors.status}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="first_name">First Name *</Label>
						<Input
							id="first_name"
							bind:value={formData.first_name}
							error={errors.first_name}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="last_name">Last Name *</Label>
						<Input
							id="last_name"
							bind:value={formData.last_name}
							error={errors.last_name}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="email">Email Address *</Label>
						<Input
							id="email"
							type="email"
							bind:value={formData.email}
							error={errors.email}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="phone">Phone Number</Label>
						<Input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							error={errors.phone}
							disabled={isSubmitting}
						/>
					</div>
				</div>
			</div>

			<!-- Job Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Job Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="position">Position *</Label>
						<Input
							id="position"
							bind:value={formData.position}
							error={errors.position}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="department">Department *</Label>
						<Select
							options={departmentOptions}
							bind:value={formData.department}
							error={errors.department}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="manager">Manager</Label>
						<Select
							options={managerOptions}
							bind:value={formData.manager}
							error={errors.manager}
							disabled={isSubmitting}
						/>
					</div>
					<div>
						<Label for="hire_date">Hire Date *</Label>
						<Input
							id="hire_date"
							type="date"
							bind:value={formData.hire_date}
							error={errors.hire_date}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="salary">Annual Salary</Label>
						<Input
							id="salary"
							type="number"
							bind:value={formData.salary}
							error={errors.salary}
							disabled={isSubmitting}
							placeholder="75000"
						/>
					</div>
				</div>
			</div>

			<!-- Additional Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Additional Information</h3>
				<div class="grid grid-cols-1 gap-4">
					<div>
						<Label for="address">Address</Label>
						<Input
							id="address"
							bind:value={formData.address}
							error={errors.address}
							disabled={isSubmitting}
						/>
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<Label for="emergency_contact_name">Emergency Contact Name</Label>
							<Input
								id="emergency_contact_name"
								bind:value={formData.emergency_contact_name}
								error={errors.emergency_contact_name}
								disabled={isSubmitting}
							/>
						</div>
						<div>
							<Label for="emergency_contact_phone">Emergency Contact Phone</Label>
							<Input
								id="emergency_contact_phone"
								type="tel"
								bind:value={formData.emergency_contact_phone}
								error={errors.emergency_contact_phone}
								disabled={isSubmitting}
							/>
						</div>
					</div>
					<div>
						<Label for="skills">Skills (comma-separated)</Label>
						<Input
							id="skills"
							bind:value={formData.skills}
							error={errors.skills}
							disabled={isSubmitting}
							placeholder="JavaScript, TypeScript, Svelte"
						/>
					</div>
				</div>
			</div>
		</form>
	</Card>
</div>
