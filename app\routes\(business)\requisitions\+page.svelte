<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import Modal from '$lib/components/ui/Modal.svelte';
	import { 
		Search, 
		Filter, 
		Plus, 
		Eye, 
		Edit, 
		Trash2,
		Briefcase,
		Calendar,
		MapPin,
		DollarSign,
		Users,
		Clock,
		CheckCircle,
		XCircle,
		AlertCircle
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock requisitions data
	let requisitions = $state([
		{
			id: 1,
			title: 'Senior Software Developer',
			department: 'Information Technology',
			location: 'Harare, Zimbabwe',
			employment_type: 'Full-time',
			experience_level: 'Senior-level',
			salary_min: 60000,
			salary_max: 80000,
			currency: 'USD',
			positions_available: 2,
			status: 'open',
			priority: 'High',
			created_date: '2025-08-01',
			application_deadline: '2025-09-01',
			hiring_manager: 'Sarah Wilson',
			applications_count: 15,
			description: 'We are looking for an experienced software developer to join our growing IT team...'
		},
		{
			id: 2,
			title: 'Marketing Manager',
			department: 'Marketing',
			location: 'Bulawayo, Zimbabwe',
			employment_type: 'Full-time',
			experience_level: 'Mid-level',
			salary_min: 45000,
			salary_max: 60000,
			currency: 'USD',
			positions_available: 1,
			status: 'draft',
			priority: 'Medium',
			created_date: '2025-08-05',
			application_deadline: '2025-09-15',
			hiring_manager: 'Mike Johnson',
			applications_count: 0,
			description: 'Seeking a creative marketing professional to lead our marketing initiatives...'
		},
		{
			id: 3,
			title: 'Financial Analyst',
			department: 'Finance',
			location: 'Harare, Zimbabwe',
			employment_type: 'Full-time',
			experience_level: 'Entry-level',
			salary_min: 35000,
			salary_max: 45000,
			currency: 'USD',
			positions_available: 1,
			status: 'closed',
			priority: 'Low',
			created_date: '2025-07-15',
			application_deadline: '2025-08-15',
			hiring_manager: 'Emily Brown',
			applications_count: 8,
			description: 'Entry-level position for a recent finance graduate to join our finance team...'
		},
		{
			id: 4,
			title: 'Tour Guide',
			department: 'Tourism',
			location: 'Victoria Falls, Zimbabwe',
			employment_type: 'Part-time',
			experience_level: 'Entry-level',
			salary_min: 800,
			salary_max: 1200,
			currency: 'USD',
			positions_available: 3,
			status: 'on_hold',
			priority: 'Medium',
			created_date: '2025-08-10',
			application_deadline: '2025-09-30',
			hiring_manager: 'David Smith',
			applications_count: 12,
			description: 'Enthusiastic tour guides needed for our Victoria Falls operations...'
		}
	]);

	// Filter and search state
	let searchTerm = $state('');
	let statusFilter = $state('all');
	let departmentFilter = $state('all');
	let priorityFilter = $state('all');

	// Modal state
	let showDeleteModal = $state(false);
	let selectedRequisition = $state<typeof requisitions[0] | null>(null);
	let isDeleting = $state(false);

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Status' },
		{ value: 'draft', label: 'Draft' },
		{ value: 'open', label: 'Open' },
		{ value: 'on_hold', label: 'On Hold' },
		{ value: 'closed', label: 'Closed' }
	];

	const departmentOptions = [
		{ value: 'all', label: 'All Departments' },
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Tourism', label: 'Tourism' },
		{ value: 'Human Resources', label: 'Human Resources' }
	];

	const priorityOptions = [
		{ value: 'all', label: 'All Priorities' },
		{ value: 'Low', label: 'Low' },
		{ value: 'Medium', label: 'Medium' },
		{ value: 'High', label: 'High' },
		{ value: 'Urgent', label: 'Urgent' }
	];

	// Computed filtered requisitions
	const filteredRequisitions = $derived(() => {
		return requisitions.filter(req => {
			const matchesSearch = searchTerm === '' || 
				req.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
				req.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
				req.hiring_manager.toLowerCase().includes(searchTerm.toLowerCase());
			
			const matchesStatus = statusFilter === 'all' || req.status === statusFilter;
			const matchesDepartment = departmentFilter === 'all' || req.department === departmentFilter;
			const matchesPriority = priorityFilter === 'all' || req.priority === priorityFilter;

			return matchesSearch && matchesStatus && matchesDepartment && matchesPriority;
		});
	});

	// Status styling
	const getStatusInfo = (status: string) => {
		const statusMap = {
			draft: { color: 'bg-gray-100 text-gray-800', icon: Edit },
			open: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
			on_hold: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
			closed: { color: 'bg-red-100 text-red-800', icon: XCircle }
		};
		return statusMap[status as keyof typeof statusMap] || statusMap.draft;
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case 'Low': return 'bg-blue-100 text-blue-800';
			case 'Medium': return 'bg-yellow-100 text-yellow-800';
			case 'High': return 'bg-orange-100 text-orange-800';
			case 'Urgent': return 'bg-red-100 text-red-800';
			default: return 'bg-gray-100 text-gray-800';
		}
	};

	// Actions
	const handleCreateRequisition = () => {
		goto('/recruitment/create');
	};

	const handleViewRequisition = (id: number) => {
		// In a real app, this would go to a detailed view
		console.log('View requisition:', id);
		notificationStore.info('View Requisition', 'Detailed view would open here');
	};

	const handleEditRequisition = (id: number) => {
		// In a real app, this would go to edit page
		console.log('Edit requisition:', id);
		notificationStore.info('Edit Requisition', 'Edit form would open here');
	};

	const handleDeleteRequisition = (requisition: typeof requisitions[0]) => {
		selectedRequisition = requisition;
		showDeleteModal = true;
	};

	const confirmDelete = async () => {
		if (!selectedRequisition) return;

		isDeleting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Remove from array
			requisitions = requisitions.filter(r => r.id !== selectedRequisition!.id);

			notificationStore.success('Requisition Deleted', 'Job requisition has been successfully deleted');
			showDeleteModal = false;
		} catch (error) {
			console.error('Error deleting requisition:', error);
			notificationStore.error('Delete Failed', 'Failed to delete job requisition');
		} finally {
			isDeleting = false;
		}
	};

	const canManageRequisitions = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin' || user?.user_role === 'manager';
</script>

<svelte:head>
	<title>Job Requisitions - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Job Requisitions</h1>
			<p class="text-muted-foreground">Manage job openings and hiring requirements</p>
		</div>
		{#if canManageRequisitions}
			<Button variant="default" onclick={handleCreateRequisition}>
				<Plus class="w-4 h-4" />
				Create Requisition
			</Button>
		{/if}
	</div>

	<!-- Filters -->
	<Card class="p-4">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			<div>
				<Input
					placeholder="Search requisitions..."
					bind:value={searchTerm}
					class="w-full"
				>
					<Search slot="icon" class="w-4 h-4" />
				</Input>
			</div>
			<div>
				<Select
					options={statusOptions}
					bind:value={statusFilter}
					placeholder="Filter by status"
				/>
			</div>
			<div>
				<Select
					options={departmentOptions}
					bind:value={departmentFilter}
					placeholder="Filter by department"
				/>
			</div>
			<div>
				<Select
					options={priorityOptions}
					bind:value={priorityFilter}
					placeholder="Filter by priority"
				/>
			</div>
			<div class="flex items-center">
				<Button variant="outline" class="w-full">
					<Filter class="w-4 h-4" />
					More Filters
				</Button>
			</div>
		</div>
	</Card>

	<!-- Summary Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Requisitions</p>
					<p class="text-2xl font-bold text-foreground">{filteredRequisitions.length}</p>
				</div>
				<Briefcase class="w-8 h-8 text-muted-foreground" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Open Positions</p>
					<p class="text-2xl font-bold text-green-600">
						{filteredRequisitions.filter(r => r.status === 'open').length}
					</p>
				</div>
				<CheckCircle class="w-8 h-8 text-green-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Draft</p>
					<p class="text-2xl font-bold text-gray-600">
						{filteredRequisitions.filter(r => r.status === 'draft').length}
					</p>
				</div>
				<Edit class="w-8 h-8 text-gray-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Applications</p>
					<p class="text-2xl font-bold text-blue-600">
						{filteredRequisitions.reduce((sum, r) => sum + r.applications_count, 0)}
					</p>
				</div>
				<Users class="w-8 h-8 text-blue-600" />
			</div>
		</Card>
	</div>

	<!-- Requisitions List -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		{#each filteredRequisitions as requisition}
			{@const statusInfo = getStatusInfo(requisition.status)}
			{@const StatusIcon = statusInfo.icon}
			<Card class="p-6">
				<div class="flex items-start justify-between mb-4">
					<div class="flex-1">
						<div class="flex items-center gap-2 mb-2">
							<h3 class="text-lg font-semibold text-foreground">{requisition.title}</h3>
							<Badge class={statusInfo.color}>
								<StatusIcon class="w-3 h-3 mr-1" />
								{requisition.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
							</Badge>
						</div>
						<p class="text-sm text-muted-foreground mb-1">{requisition.department}</p>
						<div class="flex items-center gap-4 text-sm text-muted-foreground">
							<div class="flex items-center gap-1">
								<MapPin class="w-4 h-4" />
								<span>{requisition.location}</span>
							</div>
							<div class="flex items-center gap-1">
								<Users class="w-4 h-4" />
								<span>{requisition.positions_available} position{requisition.positions_available > 1 ? 's' : ''}</span>
							</div>
						</div>
					</div>
					<Badge class={getPriorityColor(requisition.priority)}>
						{requisition.priority}
					</Badge>
				</div>

				<div class="space-y-3 mb-4">
					<div class="flex items-center justify-between text-sm">
						<span class="text-muted-foreground">Employment Type:</span>
						<span class="text-foreground">{requisition.employment_type}</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span class="text-muted-foreground">Experience Level:</span>
						<span class="text-foreground">{requisition.experience_level}</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span class="text-muted-foreground">Salary Range:</span>
						<span class="text-foreground">
							{requisition.currency} {requisition.salary_min.toLocaleString()} - {requisition.salary_max.toLocaleString()}
						</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span class="text-muted-foreground">Hiring Manager:</span>
						<span class="text-foreground">{requisition.hiring_manager}</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span class="text-muted-foreground">Applications:</span>
						<span class="text-foreground font-medium">{requisition.applications_count}</span>
					</div>
				</div>

				<div class="flex items-center justify-between pt-4 border-t border-border">
					<div class="text-sm text-muted-foreground">
						<div class="flex items-center gap-1">
							<Calendar class="w-4 h-4" />
							<span>Deadline: {new Date(requisition.application_deadline).toLocaleDateString()}</span>
						</div>
					</div>
					{#if canManageRequisitions}
						<div class="flex gap-2">
							<Button variant="outline" size="sm" onclick={() => handleViewRequisition(requisition.id)}>
								<Eye class="w-4 h-4" />
								View
							</Button>
							<Button variant="outline" size="sm" onclick={() => handleEditRequisition(requisition.id)}>
								<Edit class="w-4 h-4" />
								Edit
							</Button>
							<Button variant="destructive" size="sm" onclick={() => handleDeleteRequisition(requisition)}>
								<Trash2 class="w-4 h-4" />
								Delete
							</Button>
						</div>
					{/if}
				</div>
			</Card>
		{/each}
	</div>

	{#if filteredRequisitions.length === 0}
		<Card class="p-12 text-center">
			<Briefcase class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-semibold text-foreground mb-2">No requisitions found</h3>
			<p class="text-muted-foreground">Try adjusting your search criteria or filters.</p>
			{#if canManageRequisitions}
				<Button variant="default" class="mt-4" onclick={handleCreateRequisition}>
					<Plus class="w-4 h-4" />
					Create First Requisition
				</Button>
			{/if}
		</Card>
	{/if}
</div>

<!-- Delete Confirmation Modal -->
<Modal bind:open={showDeleteModal} title="Delete Job Requisition" size="md">
	{#snippet children()}
		{#if selectedRequisition}
			<div class="space-y-4">
				<div class="flex items-center gap-3 p-4 bg-red-50 rounded-lg">
					<AlertCircle class="w-6 h-6 text-red-600" />
					<div>
						<h4 class="font-medium text-red-900">Are you sure you want to delete this requisition?</h4>
						<p class="text-sm text-red-700">This action cannot be undone.</p>
					</div>
				</div>
				
				<div class="p-4 bg-muted rounded-lg">
					<h4 class="font-medium text-foreground mb-2">Requisition Details</h4>
					<div class="space-y-1 text-sm">
						<div><span class="text-muted-foreground">Title:</span> {selectedRequisition.title}</div>
						<div><span class="text-muted-foreground">Department:</span> {selectedRequisition.department}</div>
						<div><span class="text-muted-foreground">Applications:</span> {selectedRequisition.applications_count}</div>
					</div>
				</div>
			</div>
		{/if}
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showDeleteModal = false} disabled={isDeleting}>
			Cancel
		</Button>
		<Button variant="destructive" onclick={confirmDelete} disabled={isDeleting}>
			{isDeleting ? 'Deleting...' : 'Delete Requisition'}
		</Button>
	{/snippet}
</Modal>
