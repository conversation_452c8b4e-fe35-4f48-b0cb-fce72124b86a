<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	// Redirect to policies page
	onMount(() => {
		goto('/policies', { replaceState: true });
	});
</script>

<!-- This page redirects to the policies page -->
<div class="flex items-center justify-center min-h-screen">
	<div class="text-center">
		<h1 class="text-2xl font-bold mb-2">Redirecting...</h1>
		<p class="text-muted-foreground">Taking you to Company Policies</p>
	</div>
</div>
