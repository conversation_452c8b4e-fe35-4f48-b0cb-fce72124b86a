import { z } from 'zod';

// Password strength requirements
const passwordRequirements = {
	minLength: 8,
	maxLength: 128,
	requireUppercase: true,
	requireLowercase: true,
	requireNumbers: true,
	requireSpecialChars: true,
	forbiddenPatterns: [
		'password',
		'123456',
		'qwerty',
		'admin',
		'rainbow',
		'tourism'
	]
};

// Password validation schema
export const passwordSchema = z
	.string()
	.min(passwordRequirements.minLength, `Password must be at least ${passwordRequirements.minLength} characters long`)
	.max(passwordRequirements.maxLength, `Password must be less than ${passwordRequirements.maxLength} characters`)
	.refine((password) => {
		if (!passwordRequirements.requireUppercase) return true;
		return /[A-Z]/.test(password);
	}, 'Password must contain at least one uppercase letter')
	.refine((password) => {
		if (!passwordRequirements.requireLowercase) return true;
		return /[a-z]/.test(password);
	}, 'Password must contain at least one lowercase letter')
	.refine((password) => {
		if (!passwordRequirements.requireNumbers) return true;
		return /\d/.test(password);
	}, 'Password must contain at least one number')
	.refine((password) => {
		if (!passwordRequirements.requireSpecialChars) return true;
		return /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
	}, 'Password must contain at least one special character')
	.refine((password) => {
		const lowerPassword = password.toLowerCase();
		return !passwordRequirements.forbiddenPatterns.some(pattern => 
			lowerPassword.includes(pattern.toLowerCase())
		);
	}, 'Password contains forbidden patterns');

// Password confirmation schema
export const passwordConfirmationSchema = z.object({
	password: passwordSchema,
	confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
	message: "Passwords don't match",
	path: ["confirmPassword"]
});

// Invite token schema
export const inviteTokenSchema = z
	.string()
	.min(10, 'Invitation token must be at least 10 characters')
	.max(100, 'Invitation token is too long')
	.regex(/^[A-Za-z0-9_-]+$/, 'Invalid invitation token format');

// Complete account creation schema
export const accountCreationSchema = z.object({
	inviteToken: inviteTokenSchema,
	password: passwordSchema,
	confirmPassword: z.string(),
	acceptTerms: z.boolean().refine(val => val === true, {
		message: 'You must accept the terms and conditions'
	}),
	acceptPrivacy: z.boolean().refine(val => val === true, {
		message: 'You must accept the privacy policy'
	})
}).refine((data) => data.password === data.confirmPassword, {
	message: "Passwords don't match",
	path: ["confirmPassword"]
});

// Type inference
export type PasswordFormData = z.infer<typeof passwordConfirmationSchema>;
export type AccountCreationFormData = z.infer<typeof accountCreationSchema>;

// Password strength calculation
export interface PasswordStrength {
	score: number; // 0-100
	level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
	feedback: string[];
	requirements: {
		length: boolean;
		uppercase: boolean;
		lowercase: boolean;
		numbers: boolean;
		specialChars: boolean;
		noForbiddenPatterns: boolean;
	};
}

export const calculatePasswordStrength = (password: string): PasswordStrength => {
	const requirements = {
		length: password.length >= passwordRequirements.minLength,
		uppercase: /[A-Z]/.test(password),
		lowercase: /[a-z]/.test(password),
		numbers: /\d/.test(password),
		specialChars: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password),
		noForbiddenPatterns: !passwordRequirements.forbiddenPatterns.some(pattern => 
			password.toLowerCase().includes(pattern.toLowerCase())
		)
	};

	const feedback: string[] = [];
	let score = 0;

	// Base score for length
	if (requirements.length) {
		score += 20;
	} else {
		feedback.push(`Use at least ${passwordRequirements.minLength} characters`);
	}

	// Character variety
	if (requirements.uppercase) score += 15;
	else feedback.push('Add uppercase letters');

	if (requirements.lowercase) score += 15;
	else feedback.push('Add lowercase letters');

	if (requirements.numbers) score += 15;
	else feedback.push('Add numbers');

	if (requirements.specialChars) score += 15;
	else feedback.push('Add special characters');

	if (requirements.noForbiddenPatterns) score += 10;
	else feedback.push('Avoid common patterns');

	// Bonus points for longer passwords
	if (password.length >= 12) score += 5;
	if (password.length >= 16) score += 5;

	// Determine level
	let level: PasswordStrength['level'];
	if (score < 30) level = 'weak';
	else if (score < 50) level = 'fair';
	else if (score < 70) level = 'good';
	else if (score < 90) level = 'strong';
	else level = 'very-strong';

	return {
		score: Math.min(100, score),
		level,
		feedback,
		requirements
	};
};

// Validation helper functions
export const validatePassword = (password: string) => {
	return passwordSchema.safeParse(password);
};

export const validatePasswordConfirmation = (data: { password: string; confirmPassword: string }) => {
	return passwordConfirmationSchema.safeParse(data);
};

export const validateInviteToken = (token: string) => {
	return inviteTokenSchema.safeParse(token);
};

export const validateAccountCreation = (data: unknown) => {
	return accountCreationSchema.safeParse(data);
};

// Default values
export const passwordFormDefaults: Partial<PasswordFormData> = {
	password: '',
	confirmPassword: ''
};

export const accountCreationDefaults: Partial<AccountCreationFormData> = {
	inviteToken: '',
	password: '',
	confirmPassword: '',
	acceptTerms: false,
	acceptPrivacy: false
};
