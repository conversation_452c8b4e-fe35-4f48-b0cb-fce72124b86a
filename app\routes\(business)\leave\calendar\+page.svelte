<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { 
		Calendar as CalendarIcon, 
		ChevronLeft, 
		ChevronRight, 
		Filter,
		Users,
		Clock,
		CheckCircle,
		XCircle
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Calendar state
	let currentDate = $state(new Date());
	let selectedDate = $state<Date | null>(null);
	let viewMode = $state<'month' | 'week'>('month');

	// Mock leave data
	const leaveRequests = [
		{
			id: 1,
			employee: '<PERSON>',
			type: 'Annual Leave',
			startDate: '2025-08-25',
			endDate: '2025-08-27',
			status: 'approved',
			days: 3
		},
		{
			id: 2,
			employee: '<PERSON>',
			type: 'Sick Leave',
			startDate: '2025-08-22',
			endDate: '2025-08-22',
			status: 'approved',
			days: 1
		},
		{
			id: 3,
			employee: '<PERSON>',
			type: 'Personal Leave',
			startDate: '2025-08-28',
			endDate: '2025-08-30',
			status: 'pending',
			days: 3
		},
		{
			id: 4,
			employee: 'Emily Brown',
			type: 'Annual Leave',
			startDate: '2025-09-02',
			endDate: '2025-09-06',
			status: 'approved',
			days: 5
		}
	];

	// Calendar utilities
	const monthNames = [
		'January', 'February', 'March', 'April', 'May', 'June',
		'July', 'August', 'September', 'October', 'November', 'December'
	];

	const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

	const getMonthDays = (date: Date) => {
		const year = date.getFullYear();
		const month = date.getMonth();
		const firstDay = new Date(year, month, 1);
		const lastDay = new Date(year, month + 1, 0);
		const daysInMonth = lastDay.getDate();
		const startingDayOfWeek = firstDay.getDay();

		const days = [];
		
		// Add empty cells for days before the first day of the month
		for (let i = 0; i < startingDayOfWeek; i++) {
			days.push(null);
		}
		
		// Add all days of the month
		for (let day = 1; day <= daysInMonth; day++) {
			days.push(new Date(year, month, day));
		}
		
		return days;
	};

	const getLeaveForDate = (date: Date) => {
		const dateStr = date.toISOString().split('T')[0];
		return leaveRequests.filter(leave => {
			const start = new Date(leave.startDate);
			const end = new Date(leave.endDate);
			return date >= start && date <= end;
		});
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'approved': return 'bg-green-100 text-green-800 border-green-200';
			case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
			case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
			default: return 'bg-gray-100 text-gray-800 border-gray-200';
		}
	};

	const getTypeColor = (type: string) => {
		switch (type) {
			case 'Annual Leave': return 'bg-blue-500';
			case 'Sick Leave': return 'bg-red-500';
			case 'Personal Leave': return 'bg-purple-500';
			case 'Maternity Leave': return 'bg-pink-500';
			case 'Paternity Leave': return 'bg-indigo-500';
			default: return 'bg-gray-500';
		}
	};

	const navigateMonth = (direction: 'prev' | 'next') => {
		const newDate = new Date(currentDate);
		if (direction === 'prev') {
			newDate.setMonth(newDate.getMonth() - 1);
		} else {
			newDate.setMonth(newDate.getMonth() + 1);
		}
		currentDate = newDate;
	};

	const goToToday = () => {
		currentDate = new Date();
		selectedDate = new Date();
	};

	const isToday = (date: Date) => {
		const today = new Date();
		return date.toDateString() === today.toDateString();
	};

	const isSelected = (date: Date) => {
		return selectedDate && date.toDateString() === selectedDate.toDateString();
	};

	const monthDays = $derived(getMonthDays(currentDate));
</script>

<svelte:head>
	<title>Leave Calendar - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Leave Calendar</h1>
			<p class="text-muted-foreground">View and manage team leave schedules</p>
		</div>
		
		<div class="flex items-center gap-2">
			<Button variant="outline" onclick={goToToday}>
				<CalendarIcon class="w-4 h-4" />
				Today
			</Button>
			<Button variant="outline">
				<Filter class="w-4 h-4" />
				Filter
			</Button>
		</div>
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
		<!-- Calendar -->
		<div class="lg:col-span-3">
			<Card class="p-6">
				<!-- Calendar Header -->
				<div class="flex items-center justify-between mb-6">
					<div class="flex items-center gap-4">
						<h2 class="text-xl font-semibold text-foreground">
							{monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
						</h2>
						<div class="flex items-center gap-1">
							<Button variant="ghost" size="icon" onclick={() => navigateMonth('prev')}>
								<ChevronLeft class="w-4 h-4" />
							</Button>
							<Button variant="ghost" size="icon" onclick={() => navigateMonth('next')}>
								<ChevronRight class="w-4 h-4" />
							</Button>
						</div>
					</div>
					
					<div class="flex items-center gap-2">
						<Button 
							variant={viewMode === 'month' ? 'default' : 'outline'} 
							size="sm"
							onclick={() => viewMode = 'month'}
						>
							Month
						</Button>
						<Button 
							variant={viewMode === 'week' ? 'default' : 'outline'} 
							size="sm"
							onclick={() => viewMode = 'week'}
						>
							Week
						</Button>
					</div>
				</div>

				<!-- Calendar Grid -->
				<div class="grid grid-cols-7 gap-1">
					<!-- Day Headers -->
					{#each dayNames as dayName}
						<div class="p-2 text-center text-sm font-medium text-muted-foreground border-b">
							{dayName}
						</div>
					{/each}

					<!-- Calendar Days -->
					{#each monthDays as day}
						{@const dayLeave = day ? getLeaveForDate(day) : []}
						<div class="min-h-[100px] border border-border p-1 {day ? 'bg-background hover:bg-muted/50' : 'bg-muted/20'} transition-colors">
							{#if day}
								<div class="h-full">
									<!-- Day Number -->
									<div class="flex items-center justify-between mb-1">
										<button
											class="w-6 h-6 text-sm rounded-full flex items-center justify-center transition-colors {isToday(day)
												? 'bg-primary text-primary-foreground font-semibold'
												: isSelected(day)
													? 'bg-primary/20 text-primary font-medium'
													: 'hover:bg-muted text-foreground'}"
											onclick={() => selectedDate = day}
										>
											{day.getDate()}
										</button>
									</div>

									<!-- Leave Events -->
									<div class="space-y-1">
										{#each dayLeave.slice(0, 2) as leave}
											<div class="text-xs p-1 rounded border {getStatusColor(leave.status)} truncate">
												<div class="flex items-center gap-1">
													<div class="w-2 h-2 rounded-full {getTypeColor(leave.type)}"></div>
													<span class="font-medium">{leave.employee}</span>
												</div>
											</div>
										{/each}
										{#if dayLeave.length > 2}
											<div class="text-xs text-muted-foreground text-center">
												+{dayLeave.length - 2} more
											</div>
										{/if}
									</div>
								</div>
							{/if}
						</div>
					{/each}
				</div>
			</Card>
		</div>

		<!-- Sidebar -->
		<div class="space-y-6">
			<!-- Leave Summary -->
			<Card class="p-4">
				<h3 class="text-lg font-semibold text-foreground mb-4">Leave Summary</h3>
				<div class="space-y-3">
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<Users class="w-4 h-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">On Leave Today</span>
						</div>
						<span class="text-sm font-medium text-foreground">2</span>
					</div>
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<Clock class="w-4 h-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">Pending Requests</span>
						</div>
						<span class="text-sm font-medium text-foreground">3</span>
					</div>
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<CheckCircle class="w-4 h-4 text-muted-foreground" />
							<span class="text-sm text-muted-foreground">Approved This Month</span>
						</div>
						<span class="text-sm font-medium text-foreground">12</span>
					</div>
				</div>
			</Card>

			<!-- Leave Types Legend -->
			<Card class="p-4">
				<h3 class="text-lg font-semibold text-foreground mb-4">Leave Types</h3>
				<div class="space-y-2">
					<div class="flex items-center gap-2">
						<div class="w-3 h-3 rounded-full bg-blue-500"></div>
						<span class="text-sm text-foreground">Annual Leave</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3 h-3 rounded-full bg-red-500"></div>
						<span class="text-sm text-foreground">Sick Leave</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3 h-3 rounded-full bg-purple-500"></div>
						<span class="text-sm text-foreground">Personal Leave</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3 h-3 rounded-full bg-pink-500"></div>
						<span class="text-sm text-foreground">Maternity Leave</span>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3 h-3 rounded-full bg-indigo-500"></div>
						<span class="text-sm text-foreground">Paternity Leave</span>
					</div>
				</div>
			</Card>

			<!-- Selected Date Details -->
			{#if selectedDate}
				{@const selectedLeave = getLeaveForDate(selectedDate)}
				<Card class="p-4">
					<h3 class="text-lg font-semibold text-foreground mb-4">
						{selectedDate.toLocaleDateString('en-US', { 
							weekday: 'long', 
							year: 'numeric', 
							month: 'long', 
							day: 'numeric' 
						})}
					</h3>
					{#if selectedLeave.length > 0}
						<div class="space-y-3">
							{#each selectedLeave as leave}
								<div class="p-3 border border-border rounded-lg">
									<div class="flex items-center justify-between mb-2">
										<span class="font-medium text-foreground">{leave.employee}</span>
										<Badge variant="secondary" class={getStatusColor(leave.status)}>
											{leave.status}
										</Badge>
									</div>
									<div class="text-sm text-muted-foreground">
										<div class="flex items-center gap-1 mb-1">
											<div class="w-2 h-2 rounded-full {getTypeColor(leave.type)}"></div>
											{leave.type}
										</div>
										<div>
											{leave.startDate === leave.endDate 
												? '1 day' 
												: `${leave.days} days (${leave.startDate} - ${leave.endDate})`}
										</div>
									</div>
								</div>
							{/each}
						</div>
					{:else}
						<p class="text-sm text-muted-foreground">No leave requests for this date.</p>
					{/if}
				</Card>
			{/if}
		</div>
	</div>
</div>
