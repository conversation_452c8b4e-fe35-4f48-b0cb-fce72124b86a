# 06 — Performance Management (Frontend-only)

Pages:
- `/performance/goals` — list & create goals
- `/performance/reviews` — start review, send to reviewers (mock)
- `/performance/360` — reviewer dashboard for 360 reviews

Components:
- `GoalEditor.svelte` — SMART goal form with progress tracking
- `ReviewForm.svelte` — scoring form for competencies
- `CalibrationBoard.svelte` — HR view for aggregated scores

Workflow:
1. Manager sets goals with targets and milestones.
2. Periodic check-ins are added as comments to goals.
3. HR schedules 360 review -> reviewers receive in-app tasks -> submit ratings -> results aggregated.

Acceptance:
- Goals persist in mock store,
- 360 flows simulate anonymity by masking reviewer id in aggregated view.
