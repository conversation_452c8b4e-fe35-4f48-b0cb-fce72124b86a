# 05 — Employee Self-Service (ESS) — Frontend

Routes:
- `/ess` — employee dashboard
- `/ess/profile` — profile view & edit
- `/ess/payslips` — list of payslips (mock urls)
- `/ess/policies` — policy acknowledgements

Components:
- `ProfileForm.svelte` — edit with fields requiring HR approval (show "pending approval" badge)
- `PayslipList.svelte` — list downloadable items (signed-url requested via mock)
- `PolicyAcknowledgement.svelte` — show modal & store ack

Mock API details:
- Profile updates by employee produce a `pending_changes` object in mock DB; HR route shows pending approvals.
- Payslip downloads request a `signed_url` from `mock/signed-url` endpoint.

Acceptance:
- Employee can edit non-sensitive fields locally.
- Policy acknowledgement persists and shows timestamp.
