<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { Briefcase, Plus, Users, FileText, Calendar, Eye, Edit, Trash2 } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock job requisitions data
	const jobRequisitions = [
		{
			id: 1,
			title: 'Senior Software Developer',
			department: 'IT',
			location: 'Harare',
			type: 'Full-time',
			status: 'Open',
			posted_date: '2024-01-15',
			applications: 12,
			interviews_scheduled: 3,
			description: 'We are looking for an experienced software developer to join our growing IT team.',
			requirements: ['5+ years experience', 'React/Svelte expertise', 'Team leadership skills']
		},
		{
			id: 2,
			title: 'Marketing Manager',
			department: 'Marketing',
			location: 'Victoria Falls',
			type: 'Full-time',
			status: 'In Review',
			posted_date: '2024-01-10',
			applications: 8,
			interviews_scheduled: 2,
			description: 'Lead our marketing initiatives and drive brand awareness across Zimbabwe.',
			requirements: ['Marketing degree', '3+ years management experience', 'Digital marketing skills']
		},
		{
			id: 3,
			title: 'HR Assistant',
			department: 'Human Resources',
			location: 'Harare',
			type: 'Contract',
			status: 'Closed',
			posted_date: '2023-12-20',
			applications: 25,
			interviews_scheduled: 5,
			description: 'Support HR operations and employee relations activities.',
			requirements: ['HR qualification', 'Strong communication skills', 'Attention to detail']
		}
	];

	// Mock candidates data
	const candidates = [
		{
			id: 1,
			name: 'Alice Cooper',
			email: '<EMAIL>',
			phone: '+263 77 111 2222',
			position_applied: 'Senior Software Developer',
			status: 'Interview Scheduled',
			applied_date: '2024-01-18',
			experience: '6 years',
			interview_date: '2024-02-05'
		},
		{
			id: 2,
			name: 'Bob Smith',
			email: '<EMAIL>',
			phone: '+263 77 333 4444',
			position_applied: 'Marketing Manager',
			status: 'Under Review',
			applied_date: '2024-01-16',
			experience: '4 years',
			interview_date: null
		},
		{
			id: 3,
			name: 'Carol Johnson',
			email: '<EMAIL>',
			phone: '+263 77 555 6666',
			position_applied: 'Senior Software Developer',
			status: 'Shortlisted',
			applied_date: '2024-01-20',
			experience: '8 years',
			interview_date: null
		}
	];

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'open': return 'text-green-600 bg-green-100';
			case 'in review': return 'text-yellow-600 bg-yellow-100';
			case 'closed': return 'text-gray-600 bg-gray-100';
			case 'interview scheduled': return 'text-blue-600 bg-blue-100';
			case 'under review': return 'text-yellow-600 bg-yellow-100';
			case 'shortlisted': return 'text-purple-600 bg-purple-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	const handleCreateRequisition = () => {
		console.log('Create new job requisition');
	};

	const handleViewRequisition = (id: number) => {
		console.log('View requisition:', id);
	};

	const handleEditRequisition = (id: number) => {
		console.log('Edit requisition:', id);
	};

	const handleDeleteRequisition = (id: number) => {
		console.log('Delete requisition:', id);
	};

	const handleViewCandidate = (id: number) => {
		console.log('View candidate:', id);
	};

	const handleScheduleInterview = (id: number) => {
		console.log('Schedule interview for candidate:', id);
	};
</script>

<svelte:head>
	<title>Recruitment - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Recruitment</h1>
			<p class="text-muted-foreground">Manage job requisitions and candidate applications</p>
		</div>
		{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
			<Button variant="primary" onclick={handleCreateRequisition}>
				{#snippet children()}
					<Plus class="w-4 h-4" />
					Create Job Requisition
				{/snippet}
			</Button>
		{/if}
	</div>

	<!-- Recruitment Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">
						{jobRequisitions.filter(job => job.status === 'Open').length}
					</div>
					<div class="text-sm text-muted-foreground">Open Positions</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">
						{jobRequisitions.reduce((sum, job) => sum + job.applications, 0)}
					</div>
					<div class="text-sm text-muted-foreground">Total Applications</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">
						{candidates.filter(c => c.status === 'Interview Scheduled').length}
					</div>
					<div class="text-sm text-muted-foreground">Interviews Scheduled</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">
						{candidates.filter(c => c.status === 'Shortlisted').length}
					</div>
					<div class="text-sm text-muted-foreground">Shortlisted</div>
				</div>
			{/snippet}
		</Card>
	</div>

	<!-- Job Requisitions -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Job Requisitions</h2>
		{/snippet}
		{#snippet children()}
			<div class="space-y-4">
				{#each jobRequisitions as job}
					<div class="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center gap-3 mb-2">
									<Briefcase class="w-5 h-5 text-muted-foreground" />
									<div>
										<h3 class="font-medium text-foreground">{job.title}</h3>
										<p class="text-sm text-muted-foreground">
											{job.department} • {job.location} • {job.type}
										</p>
									</div>
								</div>
								<p class="text-sm text-muted-foreground mb-3">{job.description}</p>
								<div class="flex items-center gap-4 text-sm text-muted-foreground">
									<span>Posted: {new Date(job.posted_date).toLocaleDateString()}</span>
									<span>{job.applications} applications</span>
									<span>{job.interviews_scheduled} interviews scheduled</span>
								</div>
							</div>
							<div class="flex items-center gap-3">
								<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(job.status)}">
									{job.status}
								</span>
								<div class="flex gap-2">
									<button
										onclick={() => handleViewRequisition(job.id)}
										class="text-primary hover:text-primary/80 transition-colors"
									>
										<Eye class="w-4 h-4" />
									</button>
									{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
										<button
											onclick={() => handleEditRequisition(job.id)}
											class="text-primary hover:text-primary/80 transition-colors"
										>
											<Edit class="w-4 h-4" />
										</button>
										<button
											onclick={() => handleDeleteRequisition(job.id)}
											class="text-destructive hover:text-destructive/80 transition-colors"
										>
											<Trash2 class="w-4 h-4" />
										</button>
									{/if}
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/snippet}
	</Card>

	<!-- Recent Candidates -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Recent Candidates</h2>
		{/snippet}
		{#snippet children()}
			<div class="space-y-4">
				{#each candidates as candidate}
					<div class="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center gap-3 mb-2">
									<div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
										<span class="text-sm font-medium text-primary-foreground">
											{candidate.name.split(' ').map(n => n.charAt(0)).join('')}
										</span>
									</div>
									<div>
										<h3 class="font-medium text-foreground">{candidate.name}</h3>
										<p class="text-sm text-muted-foreground">
											Applied for: {candidate.position_applied}
										</p>
									</div>
								</div>
								<div class="flex items-center gap-4 text-sm text-muted-foreground mb-2">
									<span>{candidate.email}</span>
									<span>{candidate.phone}</span>
									<span>{candidate.experience} experience</span>
								</div>
								<p class="text-xs text-muted-foreground">
									Applied on {new Date(candidate.applied_date).toLocaleDateString()}
									{#if candidate.interview_date}
										• Interview: {new Date(candidate.interview_date).toLocaleDateString()}
									{/if}
								</p>
							</div>
							<div class="flex items-center gap-3">
								<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(candidate.status)}">
									{candidate.status}
								</span>
								<div class="flex gap-2">
									<button
										onclick={() => handleViewCandidate(candidate.id)}
										class="text-primary hover:text-primary/80 transition-colors"
									>
										<Eye class="w-4 h-4" />
									</button>
									{#if candidate.status !== 'Interview Scheduled'}
										<button
											onclick={() => handleScheduleInterview(candidate.id)}
											class="text-primary hover:text-primary/80 transition-colors"
										>
											<Calendar class="w-4 h-4" />
										</button>
									{/if}
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/snippet}
	</Card>
</div>
