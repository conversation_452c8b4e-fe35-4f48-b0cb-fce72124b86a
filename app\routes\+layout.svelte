<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import favicon from '../assets/images/favicon.svg';
	import '$lib/styles/global.css';
	import { authStore } from '$lib/stores/authStore';
	import { uiStore } from '$lib/stores/uiStore';
	import ToastContainer from '$lib/components/ui/ToastContainer.svelte';
	import { ModeWatcher } from 'mode-watcher';

	let { children } = $props();

	// Subscribe to auth state
	const auth = $derived($authStore);

	// Initialize stores on mount
	onMount(() => {
		authStore.initialize();
		uiStore.initialize();
	});

	// Handle authentication redirect for auth routes only
	$effect(() => {
		if (typeof window !== 'undefined' && !auth.isLoading) {
			if (auth.isAuthenticated && $page.url.pathname === '/auth/login') {
				goto('/');
			}
		}
	});
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
	<title>HRIMS - Human Resource Information Management System</title>
	<meta name="description" content="Rainbow Tourism Group HRIMS - Comprehensive HR management solution" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
</svelte:head>

<ModeWatcher />

<!-- Root layout - minimal, just renders children -->
{@render children?.()}

<!-- Toast Container - Always present for notifications -->
<ToastContainer
	position="bottom-right"
	maxVisible={5}
	enableAnimations={true}
	enableSounds={false}
/>
