<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore, type BankDetails } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Shield, CheckCircle } from '@lucide/svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const bankDetails = $derived(store.data.bankDetails || {});

	// Form state
	let formData = $state({
		bankName: bankDetails.bankName || '',
		accountName: bankDetails.accountName || '',
		accountNumber: bankDetails.accountNumber || '',
		swiftCode: bankDetails.swiftCode || ''
	});

	// Validation state
	let errors = $state<Record<string, string>>({});
	let isVerifying = $state(false);
	let verificationStatus = $state<'none' | 'pending' | 'verified' | 'failed'>('none');

	// Bank options (Zimbabwe banks)
	const bankOptions = [
		{ value: 'CBZ Bank', label: 'CBZ Bank' },
		{ value: 'Stanbic Bank', label: 'Stanbic Bank Zimbabwe' },
		{ value: 'Standard Chartered', label: 'Standard Chartered Bank' },
		{ value: 'FBC Bank', label: 'FBC Bank' },
		{ value: 'NMB Bank', label: 'NMB Bank' },
		{ value: 'Steward Bank', label: 'Steward Bank' },
		{ value: 'CABS', label: 'CABS' },
		{ value: 'Agribank', label: 'Agribank' },
		{ value: 'Other', label: 'Other Bank' }
	];

	// Validation functions
	const validateAccountNumber = (accountNumber: string) => {
		// Basic validation for account number (numeric, appropriate length)
		const cleanNumber = accountNumber.replace(/\s/g, '');
		return /^\d{8,16}$/.test(cleanNumber);
	};

	const validateSwiftCode = (swiftCode: string) => {
		// SWIFT code validation (8 or 11 characters, alphanumeric)
		if (!swiftCode) return true; // Optional field
		return /^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/.test(swiftCode.toUpperCase());
	};

	// Form validation
	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		// Required fields
		if (!formData.bankName.trim()) newErrors.bankName = 'Bank name is required';
		if (!formData.accountName.trim()) newErrors.accountName = 'Account name is required';
		if (!formData.accountNumber.trim()) newErrors.accountNumber = 'Account number is required';

		// Format validation
		if (formData.accountNumber && !validateAccountNumber(formData.accountNumber)) {
			newErrors.accountNumber = 'Please enter a valid account number (8-16 digits)';
		}
		if (formData.swiftCode && !validateSwiftCode(formData.swiftCode)) {
			newErrors.swiftCode = 'Please enter a valid SWIFT code';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Mock bank verification
	const verifyBankDetails = async () => {
		if (!validateForm()) return;

		isVerifying = true;
		verificationStatus = 'pending';

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			// Mock verification result (90% success rate)
			const isValid = Math.random() > 0.1;
			
			if (isValid) {
				verificationStatus = 'verified';
				notificationStore.add({
					type: 'success',
					message: 'Bank details verified successfully!'
				});
			} else {
				verificationStatus = 'failed';
				notificationStore.add({
					type: 'error',
					message: 'Bank details could not be verified. Please check and try again.'
				});
			}
		} catch (error) {
			verificationStatus = 'failed';
			notificationStore.add({
				type: 'error',
				message: 'Verification failed. Please try again later.'
			});
		} finally {
			isVerifying = false;
		}
	};

	// Auto-save functionality
	let saveTimeout: NodeJS.Timeout;
	const autoSave = () => {
		clearTimeout(saveTimeout);
		saveTimeout = setTimeout(() => {
			onboardingStore.updateBankDetails(formData);
		}, 1000);
	};

	// Handle input changes
	const handleInputChange = () => {
		verificationStatus = 'none'; // Reset verification when details change
		autoSave();
	};

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		if (validateForm()) {
			onboardingStore.updateBankDetails(formData);
			onboardingStore.completeStep('bank-details');
			onboardingStore.setCurrentStep('documents');
			notificationStore.add({
				type: 'success',
				message: 'Bank details saved successfully!'
			});
			goto('/onboarding/documents');
		} else {
			notificationStore.add({
				type: 'error',
				message: 'Please fix the errors below before continuing.'
			});
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/tax-details');
	};
</script>

<svelte:head>
	<title>Bank Details - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Bank Details"
		subtitle="Please provide your banking information for salary payments. All information is encrypted and secure."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		onBack={handleBack}
		onNext={handleNext}
	>
		<form class="space-y-8" on:submit|preventDefault={handleNext}>
			<!-- Bank Information -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Bank Information
				</h3>
				
				<div class="grid grid-cols-1 gap-6">
					<!-- Bank Name -->
					<div class="space-y-2">
						<Label for="bankName" class="text-sm font-medium text-[#5C4024]">
							Bank Name <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={bankOptions}
							bind:value={formData.bankName}
							placeholder="Select your bank"
							class="onboarding-input {errors.bankName ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.bankName}
							<p class="text-xs text-[#EF4444]">{errors.bankName}</p>
						{/if}
					</div>

					<!-- Account Name -->
					<div class="space-y-2">
						<Label for="accountName" class="text-sm font-medium text-[#5C4024]">
							Account Holder Name <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="accountName"
							bind:value={formData.accountName}
							oninput={handleInputChange}
							placeholder="Full name as it appears on your bank account"
							class="onboarding-input {errors.accountName ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.accountName}
							aria-describedby={errors.accountName ? 'accountName-error' : 'accountName-help'}
						/>
						{#if errors.accountName}
							<p id="accountName-error" class="text-xs text-[#EF4444]">{errors.accountName}</p>
						{:else}
							<p id="accountName-help" class="text-xs text-[#8A6A52]">
								Must match the name on your bank account exactly
							</p>
						{/if}
					</div>

					<!-- Account Number -->
					<div class="space-y-2">
						<Label for="accountNumber" class="text-sm font-medium text-[#5C4024]">
							Account Number <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="accountNumber"
							bind:value={formData.accountNumber}
							oninput={handleInputChange}
							placeholder="Enter your account number"
							class="onboarding-input {errors.accountNumber ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.accountNumber}
							aria-describedby={errors.accountNumber ? 'accountNumber-error' : 'accountNumber-help'}
						/>
						{#if errors.accountNumber}
							<p id="accountNumber-error" class="text-xs text-[#EF4444]">{errors.accountNumber}</p>
						{:else}
							<p id="accountNumber-help" class="text-xs text-[#8A6A52]">
								Enter your full bank account number (8-16 digits)
							</p>
						{/if}
					</div>

					<!-- SWIFT Code -->
					<div class="space-y-2">
						<Label for="swiftCode" class="text-sm font-medium text-[#5C4024]">
							SWIFT Code
						</Label>
						<Input
							id="swiftCode"
							bind:value={formData.swiftCode}
							oninput={handleInputChange}
							placeholder="e.g. CBZWZWHA"
							class="onboarding-input {errors.swiftCode ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.swiftCode}
							aria-describedby={errors.swiftCode ? 'swiftCode-error' : 'swiftCode-help'}
						/>
						{#if errors.swiftCode}
							<p id="swiftCode-error" class="text-xs text-[#EF4444]">{errors.swiftCode}</p>
						{:else}
							<p id="swiftCode-help" class="text-xs text-[#8A6A52]">
								Optional - required for international transfers
							</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Verification Section -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Account Verification
				</h3>
				
				<div class="space-y-4">
					{#if verificationStatus === 'none'}
						<div class="bg-[#FCF8F2] border border-[#EDE0CF] rounded-xl p-4">
							<p class="text-sm text-[#5C4024] mb-3">
								We recommend verifying your bank details to ensure accurate salary payments.
							</p>
							<Button
								type="button"
								variant="outline"
								onclick={verifyBankDetails}
								disabled={!formData.bankName || !formData.accountNumber || isVerifying}
								class="w-full"
							>
								<Shield class="w-4 h-4 mr-2" />
								Verify Bank Details
							</Button>
						</div>
					{:else if verificationStatus === 'pending'}
						<div class="bg-[#FFF7ED] border border-[#F59E0B]/20 rounded-xl p-4">
							<div class="flex items-center gap-3">
								<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-[#F59E0B]"></div>
								<p class="text-sm text-[#92400E]">Verifying your bank details...</p>
							</div>
						</div>
					{:else if verificationStatus === 'verified'}
						<div class="bg-[#E8F9EE] border border-[#10B981]/20 rounded-xl p-4">
							<div class="flex items-center gap-3">
								<CheckCircle class="w-5 h-5 text-[#10B981]" />
								<p class="text-sm text-[#027A3A]">Bank details verified successfully!</p>
							</div>
						</div>
					{:else if verificationStatus === 'failed'}
						<div class="bg-[#FFF1F0] border border-[#EF4444]/20 rounded-xl p-4">
							<div class="flex items-center justify-between">
								<p class="text-sm text-[#9B2B2B]">Verification failed. Please check your details.</p>
								<Button
									type="button"
									variant="outline"
									size="sm"
									onclick={verifyBankDetails}
									disabled={isVerifying}
								>
									Try Again
								</Button>
							</div>
						</div>
					{/if}
				</div>
			</div>

			<!-- Security Notice -->
			<div class="bg-[#E8F9EE] border border-[#10B981]/20 rounded-xl p-4">
				<div class="flex items-start gap-3">
					<Shield class="w-5 h-5 text-[#10B981] flex-shrink-0 mt-0.5" />
					<div>
						<h4 class="font-medium text-[#027A3A] mb-1">Your Information is Secure</h4>
						<p class="text-sm text-[#027A3A] leading-relaxed">
							All banking information is encrypted using industry-standard security protocols. 
							Only authorized payroll personnel have access to this data for salary processing.
						</p>
					</div>
				</div>
			</div>

			<!-- Auto-save indicator -->
			{#if store.lastSaved}
				<div class="text-xs text-[#8A6A52] text-center py-2">
					Last saved: {store.lastSaved.toLocaleTimeString()}
				</div>
			{/if}
		</form>
	</OnboardingCard>
</div>
