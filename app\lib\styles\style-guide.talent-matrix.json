{"meta": {"name": "Talent Management Matrix Style Guide", "file": "style-guide.talent-matrix.json", "version": "1.0.0", "description": "9-box talent matrix, skill editor, succession planning tokens using RTG branding."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"matrixCellBg": "#FFFFFF", "employeeCardBg": "#FFF7ED", "dragPreview": "#F5D6A1", "skillChip": "#C49A6C", "gridBorder": "#EAD9BF"}, "gradients": {"matrixHighlight": "linear-gradient(135deg,#F5D6A1 0%,#8C6239 100%)"}, "components": {"nineBox": {"cellSize": 220, "cellBg": "#FFFFFF", "cellBorder": "1px solid #EAD9BF", "employeeCard": {"bg": "#FFF7ED", "accent": "#C49A6C", "avatarSize": 36}}, "skillEditor": {"tagBg": "#FFF7ED", "proficiencyFill": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}, "successionPanel": {"row": {"height": 64, "cta": {"bg": "#8C6239", "text": "#FFFFFF"}}}}, "interactions": {"dragDrop": {"snap": true, "animationMs": 180}, "recommendation": {"confidencePill": "#C49A6C", "linkToTraining": true}}, "accessibility": {"keyboardDrag": "Alternative keyboard controls for moving items"}, "notes": {"mocks": ["/api/talent", "/api/skills"], "acceptance": ["Nine-box supports drag/drop with mock persistence", "Skill editor updates employee profile locally"]}}