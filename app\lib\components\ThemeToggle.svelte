<script lang="ts">
	import { mode, setMode } from 'mode-watcher';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
	import { Sun, Moon, Monitor } from '@lucide/svelte';

	const themes = [
		{ value: 'light', label: 'Light', icon: Sun },
		{ value: 'dark', label: 'Dark', icon: Moon },
		{ value: 'system', label: 'System', icon: Monitor }
	] as const;

	const currentTheme = $derived(themes.find(theme => theme.value === mode.current) || themes[0]);
</script>

<DropdownMenu.Root>
	<DropdownMenu.Trigger>
		<Button variant="ghost" size="icon" class="h-9 w-9">
			{@const IconComponent = currentTheme.icon}
			<IconComponent class="h-4 w-4" />
			<span class="sr-only">Toggle theme</span>
		</Button>
	</DropdownMenu.Trigger>
	<DropdownMenu.Content align="end" class="w-48">
		<DropdownMenu.Label>Theme</DropdownMenu.Label>
		<DropdownMenu.Separator />
		{#each themes as theme}
			{@const ThemeIcon = theme.icon}
			<DropdownMenu.Item
				onSelect={() => setMode(theme.value)}
				class="flex items-center gap-2 {mode.current === theme.value ? 'bg-accent text-accent-foreground' : ''}"
			>
				<ThemeIcon class="h-4 w-4" />
				{theme.label}
				{#if mode.current === theme.value}
					<div class="ml-auto h-2 w-2 rounded-full bg-primary"></div>
				{/if}
			</DropdownMenu.Item>
		{/each}
	</DropdownMenu.Content>
</DropdownMenu.Root>
