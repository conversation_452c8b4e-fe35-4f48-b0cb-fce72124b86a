{"meta": {"name": "Rainbow Tourism Group - Dashboard Style Guide", "version": "1.1.0", "source_image": "HR Management_ Two-sided Job Finder Dashboard Design - CMARIX InfoTech.jpeg", "description": "Complete visual design tokens and component style guide derived from the provided HR dashboard image. Font: Poppins. Colors, spacing, radii, shadows, components and interactions included. Updated to RTG brand palette and gradients."}, "fonts": {"primary": {"family": "<PERSON><PERSON>s, system-ui, -apple-system, 'Segoe UI', <PERSON>o, 'Helvetica Neue', <PERSON><PERSON>", "import": "https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap", "weights": {"thin": 300, "regular": 400, "medium": 500, "semibold": 600, "bold": 700, "heavy": 800}, "usage": {"display": "700", "h1": "600", "h2": "600", "h3": "600", "body": "400", "small": "400", "label": "500"}}}, "colors": {"brand": {"primary": "#F5D6A1", "primary-400": "#E9C584", "accent": "#C49A6C", "secondary": "#8C6239", "secondary-dark": "#5C4024", "neutral-dark": "#3B2A1A", "neutral-light": "#F9F5F0", "black": "#1C1C1C", "white": "#FFFFFF"}, "sidebar": {"bg": "#3B2A1A", "bg-2": "#2F2416", "text": "#FFFFFF", "muted": "#BFA88A", "active-bg": "linear-gradient(90deg, rgba(245,214,161,0.08), rgba(196,154,108,0.03))", "active-indicator": "#F5D6A1"}, "surface": {"page-bg": "#F9F5F0", "card-bg": "#FFFFFF", "card-border": "#EAD9BF", "muted-surface": "#FCF8F2"}, "text": {"primary": "#1C1C1C", "secondary": "#5C4024", "muted": "#8A6A52", "inverted": "#FFFFFF", "success": "#10B981", "danger": "#EF4444", "warning": "#F59E0B", "info": "#0EA5E9"}, "subtle": {"light-1": "#FEFBF7", "light-2": "#FFF7ED", "border": "#F0E1CB", "divider": "#EDE0CF"}, "charts": {"bar-1": "#F5D6A1", "bar-2": "#C49A6C", "bar-3": "#8C6239", "donut-1": "#C49A6C", "donut-2": "#8C6239", "donut-3": "#F9F5F0", "positive": "#10B981", "negative": "#EF4444"}}, "gradients": {"primary": "linear-gradient(135deg, #F5D6A1 0%, #C49A6C 100%)", "secondary": "linear-gradient(135deg, #8C6239 0%, #3B2A1A 100%)", "accent": "linear-gradient(135deg, #C49A6C 0%, #F5D6A1 100%)", "hero": "linear-gradient(120deg, #F5D6A1 0%, #8C6239 50%, #3B2A1A 100%)", "soft-beige": "linear-gradient(180deg, rgba(245,214,161,0.08), rgba(245,214,161,0.02))"}, "spacing": {"scale": {"xs": 4, "sm": 8, "md": 16, "lg": 24, "xl": 32, "xxl": 48, "xxx": 64}, "layout": {"page-gutter": 24, "content-gap": 18, "card-padding": 20, "sidebar-padding": 20}}, "layout": {"page": {"maxWidth": 1400, "containerPadding": 24, "gridColumns": 12, "gutter": 20}, "sidebar": {"width": 260, "minWidthCollapsed": 72, "topPadding": 28, "logoHeight": 36, "navItemHeight": 48, "navItemRadius": 12, "navItemIconSize": 18, "selectedIndicatorWidth": 4}, "header": {"height": 72, "paddingLeft": 24, "paddingRight": 24}}, "radius": {"none": 0, "sm": 6, "md": 10, "lg": 14, "xl": 20, "pill": 9999, "card": 12, "avatar": 9999}, "shadows": {"elevation-0": "none", "elevation-1": "0px 2px 6px rgba(28,28,28,0.04)", "elevation-2": "0px 8px 20px rgba(59,42,26,0.06)", "elevation-3": "0px 12px 32px rgba(59,42,26,0.08)", "kpi-shadow": "0px 10px 30px rgba(196,154,108,0.08)"}, "borders": {"thin": "1px solid #EAD9BF", "muted": "1px solid #F0E1CB", "heavy": "2px solid rgba(59,42,26,0.06)", "card": "1px solid #EAD9BF"}, "typography": {"scale": {"display-1": {"sizePx": 28, "sizeRem": 1.75, "lineHeight": 36, "weight": 700, "letterSpacing": -0.2}, "h1": {"sizePx": 22, "sizeRem": 1.375, "lineHeight": 30, "weight": 600}, "h2": {"sizePx": 18, "sizeRem": 1.125, "lineHeight": 26, "weight": 600}, "h3": {"sizePx": 16, "sizeRem": 1, "lineHeight": 22, "weight": 600}, "body-lg": {"sizePx": 15, "sizeRem": 0.9375, "lineHeight": 22, "weight": 400}, "body": {"sizePx": 14, "sizeRem": 0.875, "lineHeight": 20, "weight": 400}, "small": {"sizePx": 12, "sizeRem": 0.75, "lineHeight": 18, "weight": 400}, "label": {"sizePx": 13, "sizeRem": 0.8125, "lineHeight": 18, "weight": 500}}, "defaults": {"fontFamily": "Poppins, system-ui, -apple-system", "color": "#1C1C1C"}}, "components": {"appShell": {"background": "#F9F5F0", "padding": 24, "maxWidth": 1400, "gap": 20}, "sidebar": {"background": "#3B2A1A", "width": 260, "logoArea": {"height": 56, "padding": 20, "logoTextColor": "#F5D6A1", "logoAccent": "#C49A6C", "logoFontWeight": 700}, "nav": {"itemHeight": 48, "iconSize": 18, "textColor": "#FFFFFF", "mutedText": "#BFA88A", "active": {"bg": "linear-gradient(90deg, rgba(245,214,161,0.08), rgba(196,154,108,0.03))", "text": "#1C1C1C", "radius": 12, "shadow": "none", "borderLeft": {"width": 4, "color": "#F5D6A1"}}, "inactiveHover": {"bg": "rgba(255,255,255,0.02)", "text": "#FFEFD9"}}, "footer": {"textColor": "#BFA88A", "padding": 20}}, "header": {"bg": "transparent", "height": 72, "search": {"height": 40, "bg": "#FCF8F2", "border": "1px solid #EAD9BF", "radius": 12, "iconColor": "#8A6A52", "placeholderColor": "#8A6A52"}, "actions": {"buttonPrimary": {"bg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "text": "#1C1C1C", "radius": 12, "padding": "10px 16px", "shadow": "none"}, "profile": {"avatarSize": 36, "avatarRadius": 9999}}}, "kpiCard": {"bg": "#FFFFFF", "radius": 12, "padding": 16, "shadow": "0px 8px 20px rgba(59,42,26,0.04)", "title": {"fontSize": 13, "weight": 600, "color": "#5C4024"}, "value": {"fontSize": 20, "weight": 700, "color": "#1C1C1C"}, "metaPositive": {"color": "#10B981", "bg": "rgba(16,185,129,0.06)", "radius": 8, "padding": "2px 8px"}, "metaNegative": {"color": "#EF4444", "bg": "rgba(239,68,68,0.06)", "radius": 8, "padding": "2px 8px"}}, "card": {"bg": "#FFFFFF", "border": "1px solid #EAD9BF", "radius": 12, "padding": 20, "shadow": "elevation-1"}, "listPanel": {"bg": "#FFFFFF", "radius": 12, "padding": 16, "rowHeight": 64, "rowGap": 12, "divider": "1px solid #EDE0CF"}, "avatar": {"sizeSmall": 28, "sizeMedium": 36, "sizeLarge": 56, "border": "2px solid #FFFFFF", "shadow": "0 2px 6px rgba(59,42,26,0.06)"}, "button": {"primary": {"bg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "text": "#1C1C1C", "radius": 12, "padding": "10px 16px", "fontWeight": 600, "hover": {"bg": "linear-gradient(90deg,#E9C584,#C49A6C)", "transform": "translateY(-1px)"}, "focus": {"outline": "2px solid rgba(245,214,161,0.16)", "outlineOffset": 2}}, "secondary": {"bg": "transparent", "text": "#8C6239", "border": "1px solid rgba(140,98,57,0.12)", "radius": 12, "padding": "8px 14px", "hover": {"bg": "rgba(140,98,57,0.04)"}}, "ghost": {"bg": "transparent", "text": "#5C4024", "radius": 10}}, "inputs": {"height": 44, "bg": "#FCF8F2", "border": "1px solid #EAD9BF", "radius": 10, "padding": "10px 12px", "placeholderColor": "#8A6A52", "focus": {"borderColor": "#C49A6C", "boxShadow": "0 0 0 4px rgba(196,154,108,0.06)"}}, "searchBar": {"height": 40, "bg": "#FCF8F2", "radius": 12, "padding": "10px 12px", "iconSize": 18, "placeholderColor": "#8A6A52"}, "table": {"headerBg": "#FFF7ED", "rowBg": "#FFFFFF", "rowHover": "#FCF8F2", "border": "1px solid #EDE0CF", "radius": 10, "cellPadding": "12px 16px", "headerWeight": 600}, "calendar": {"cellSize": 36, "cellRadius": 8, "weekdayColor": "#8A6A52", "todayBg": "#FFF7ED", "selectedBg": "#C49A6C", "selectedText": "#FFFFFF", "eventDot": "#C49A6C", "mutedDate": "#C7B59E"}, "charts": {"bar": {"colors": ["#F5D6A1", "#C49A6C", "#8C6239"], "barRadius": 8, "gridColor": "#FFF7ED", "axisColor": "#8A6A52", "labelColor": "#5C4024"}, "donut": {"colors": ["#C49A6C", "#8C6239", "#F9F5F0"], "thickness": 12, "centerLabel": {"fontSize": 16, "weight": 600}}, "sparkline": {"stroke": "#C49A6C", "width": 2, "fill": "rgba(196,154,108,0.12)"}}, "kpis": {"layout": {"height": 96, "padding": 16, "gap": 12}, "trendChip": {"radius": 10, "textSize": 12, "padding": "4px 8px", "positiveBg": "rgba(16,185,129,0.08)", "negativeBg": "rgba(239,68,68,0.08)", "positiveText": "#10B981", "negativeText": "#EF4444"}}, "notifications": {"toast": {"bgSuccess": "#E8F9EE", "textSuccess": "#027A3A", "bgError": "#FFF1F0", "textError": "#9B2B2B", "radius": 10, "shadow": "0 8px 18px rgba(59,42,26,0.06)"}, "inApp": {"panelBg": "#FFFFFF", "radius": 12, "itemHover": "#FCF8F2"}}, "modal": {"bg": "#FFFFFF", "overlay": "rgba(28,28,28,0.45)", "radius": 14, "padding": 24, "closeSize": 36, "closeColor": "#8A6A52"}}, "states": {"hover": {"opacity": 0.98, "transform": "translateY(-1px)", "transition": "all 150ms cubic-bezier(0.2,0.8,0.2,1)"}, "focus": {"outline": "2px solid rgba(245,214,161,0.16)", "outlineOffset": 2, "boxShadow": "0 0 0 6px rgba(196,154,108,0.06)"}, "disabled": {"opacity": 0.48, "cursor": "not-allowed"}}, "icons": {"size": {"small": 16, "regular": 20, "large": 28}, "color": {"default": "#5C4024", "muted": "#8A6A52", "onPrimary": "#1C1C1C"}}, "accessibility": {"contrast": {"text-on-primary": {"foreground": "#1C1C1C", "background": "#F5D6A1", "ratio": 4.2}, "text-on-sidebar": {"foreground": "#FFFFFF", "background": "#3B2A1A", "ratio": 11.5}, "text-on-card": {"foreground": "#1C1C1C", "background": "#FFFFFF", "ratio": 12}}, "font-scaling": {"baseRem": 16, "mobileScale": 0.9375}, "keyboard": {"focusOrderNotes": "All interactive elements must have logical tab order; focusable areas must show visible focus ring defined in states.focus."}}, "tokensToCssVars": {"prefix": "--rtg", "mapping": {"color-primary": "colors.brand.primary", "color-primary-400": "colors.brand.primary-400", "color-secondary": "colors.brand.secondary", "color-neutral-dark": "colors.brand['neutral-dark']", "bg-page": "colors.surface['page-bg']", "card-bg": "components.card.bg", "radius-card": "radius.card", "shadow-kpi": "shadows.kpi-shadow", "font-family": "fonts.primary.family", "font-weight-regular": "fonts.primary.weights.regular", "font-weight-bold": "fonts.primary.weights.bold", "space-md": "spacing.scale.md", "gradient-primary": "gradients.primary"}, "exampleOutput": {"--rtg-color-primary": "#F5D6A1", "--rtg-card-bg": "#FFFFFF", "--rtg-radius-card": "12px"}, "suggestedUsage": {"tailwindThemeExtend": {"colors": {"rtg-primary": "var(--rtg-color-primary)", "rtg-secondary": "var(--rtg-color-secondary)", "rtg-sidebar": "#3B2A1A"}, "borderRadius": {"rtg-card": "var(--rtg-radius-card)"}, "boxShadow": {"rtg-kpi": "var(--rtg-shadow-kpi)"}}}}, "tailwindSnippet": {"explanation": "Paste into tailwind.config.cjs `theme.extend` to map tokens. This uses CSS variables where helpful so runtime theme switching is possible.", "code": "module.exports = { theme: { extend: { colors: { 'rtg-primary': 'var(--rtg-color-primary)', 'rtg-secondary': 'var(--rtg-color-secondary)', 'rtg-sidebar': '#3B2A1A' }, borderRadius: { 'rtg-card': '12px' }, boxShadow: { 'rtg-kpi': '0px 10px 30px rgba(196,154,108,0.08)' }, backgroundImage: { 'rtg-hero': \"linear-gradient(120deg,#F5D6A1 0%,#8C6239 50%,#3B2A1A 100%)\" } } } }"}, "componentExamples": {"sidebarItem": {"htmlStructure": "<div class='sidebar-item'><span class='icon'></span><span class='label'>Dashboard</span></div>", "cssNotes": "height: 48px; padding-left: 16px; border-radius: 12px; display:flex; align-items:center; gap:12px; color: #FFFFFF; when active use active.bg gradient and left 4px indicator #F5D6A1"}, "kpiCard": {"htmlStructure": "<div class='kpi-card'><div class='title'>Interviews</div><div class='value'>256</div><div class='trend chip positive'>+4.2%</div></div>", "cssNotes": "bg: #FFFFFF; radius 12px; padding 16px; value font-size 20px weight 700; trend chip radius 8px and background rgba(16,185,129,0.06)"}, "rightPanelListRow": {"htmlStructure": "<div class='row'><img class='avatar' src='...' /><div class='meta'><div class='name'><PERSON><PERSON></div><div class='role'>Marketing Specialist</div></div><div class='time'>04:00 PM</div></div>", "cssNotes": "row height 64px; avatar 36px; name font-size 14px weight 600; time font-size 12px muted"}}, "implementationNotes": {"poppinsLoad": "Use the Google Fonts import URL above. Preload with <link rel='preload' as='style' href='...'> if performance is critical.", "pixelPerfectionHint": "Use the spacing scale to approximate intermediate gaps (e.g., 18px = spacing.md + 2). Use rem units for typography.", "responsive": {"breakpoints": {"sm": 640, "md": 768, "lg": 1024, "xl": 1280}, "sidebarBehavior": "collapse to icons-only at < 1024px or to off-canvas at < 768px", "cards": "stack vertically on small screens; reduce padding to spacing.sm"}, "chartsNotes": "Use chart palette colors; ensure tooltips use white bg, shadow elevation-2, radius 8, and font-size 12px weight 500.", "iconography": "Use rounded line icons (stroke 2). Color icons with #5C4024 normally and inverted on dark bg.", "avatars": "Use circular avatars with 2px white border and subtle shadow; initials fallback using background #F9F5F0 and text color #8C6239."}}