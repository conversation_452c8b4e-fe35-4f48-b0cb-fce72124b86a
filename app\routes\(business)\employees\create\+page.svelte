<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import { ArrowLeft, Save, X, Plus } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Form data
	let formData = $state({
		employee_number: '',
		first_name: '',
		last_name: '',
		email: '',
		phone: '',
		position: '',
		department: '',
		manager: '',
		hire_date: new Date().toISOString().split('T')[0], // Today's date
		status: 'Active',
		salary: '',
		address: '',
		emergency_contact_name: '',
		emergency_contact_phone: '',
		skills: ''
	});

	// Form validation errors
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Department options
	const departmentOptions = [
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Human Resources', label: 'Human Resources' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Operations', label: 'Operations' },
		{ value: 'Sales', label: 'Sales' }
	];

	// Status options
	const statusOptions = [
		{ value: 'Active', label: 'Active' },
		{ value: 'Inactive', label: 'Inactive' },
		{ value: 'On Leave', label: 'On Leave' }
	];

	// Manager options (mock data)
	const managerOptions = [
		{ value: 'Sarah Wilson', label: 'Sarah Wilson' },
		{ value: 'Mike Johnson', label: 'Mike Johnson' },
		{ value: 'Emily Brown', label: 'Emily Brown' },
		{ value: 'David Smith', label: 'David Smith' }
	];

	// Generate employee number based on department
	const generateEmployeeNumber = () => {
		const deptCodes: Record<string, string> = {
			'Information Technology': 'IT',
			'Human Resources': 'HR',
			'Finance': 'FN',
			'Marketing': 'MK',
			'Operations': 'OP',
			'Sales': 'SL'
		};
		
		const deptCode = deptCodes[formData.department] || 'GN';
		const randomNum = Math.floor(Math.random() * 9000) + 1000;
		return `RTG${deptCode}${randomNum}`;
	};

	// Auto-generate employee number when department changes
	$effect(() => {
		if (formData.department && !formData.employee_number) {
			formData.employee_number = generateEmployeeNumber();
		}
	});

	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		// Required field validation
		if (!formData.first_name.trim()) {
			newErrors.first_name = 'First name is required';
		}
		if (!formData.last_name.trim()) {
			newErrors.last_name = 'Last name is required';
		}
		if (!formData.email.trim()) {
			newErrors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}
		if (!formData.employee_number.trim()) {
			newErrors.employee_number = 'Employee number is required';
		}
		if (!formData.position.trim()) {
			newErrors.position = 'Position is required';
		}
		if (!formData.department) {
			newErrors.department = 'Department is required';
		}
		if (!formData.hire_date) {
			newErrors.hire_date = 'Hire date is required';
		}

		// Phone validation
		if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
			newErrors.phone = 'Please enter a valid phone number';
		}

		// Salary validation
		if (formData.salary && !/^\d+(\.\d{2})?$/.test(formData.salary)) {
			newErrors.salary = 'Please enter a valid salary amount';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) {
			notificationStore.error('Validation Error', 'Please fix the errors below');
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			console.log('Creating employee:', formData);
			
			notificationStore.success('Employee Created', 'New employee has been successfully added');
			
			// Redirect to employees list
			goto('/employees');
		} catch (error) {
			console.error('Error creating employee:', error);
			notificationStore.error('Creation Failed', 'Failed to create new employee');
		} finally {
			isSubmitting = false;
		}
	};

	const handleCancel = () => {
		goto('/employees');
	};

	const canCreate = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin';

	// Redirect if user doesn't have permission
	if (!canCreate) {
		goto('/employees');
	}
</script>

<svelte:head>
	<title>Create New Employee - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleCancel}>
				<ArrowLeft class="w-4 h-4" />
				Back to Employees
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">Create New Employee</h1>
				<p class="text-muted-foreground">Add a new employee to the system</p>
			</div>
		</div>
		
		<div class="flex gap-2">
			<Button variant="outline" onclick={handleCancel} disabled={isSubmitting}>
				<X class="w-4 h-4" />
				Cancel
			</Button>
			<Button variant="default" onclick={handleSubmit} disabled={isSubmitting}>
				<Plus class="w-4 h-4" />
				{isSubmitting ? 'Creating...' : 'Create Employee'}
			</Button>
		</div>
	</div>

	<!-- Creation Form -->
	<Card class="p-6">
		<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
			<!-- Personal Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Personal Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="first_name">First Name *</Label>
						<Input
							id="first_name"
							bind:value={formData.first_name}
							error={errors.first_name}
							disabled={isSubmitting}
							required
							placeholder="John"
						/>
					</div>
					<div>
						<Label for="last_name">Last Name *</Label>
						<Input
							id="last_name"
							bind:value={formData.last_name}
							error={errors.last_name}
							disabled={isSubmitting}
							required
							placeholder="Doe"
						/>
					</div>
					<div>
						<Label for="email">Email Address *</Label>
						<Input
							id="email"
							type="email"
							bind:value={formData.email}
							error={errors.email}
							disabled={isSubmitting}
							required
							placeholder="<EMAIL>"
						/>
					</div>
					<div>
						<Label for="phone">Phone Number</Label>
						<Input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							error={errors.phone}
							disabled={isSubmitting}
							placeholder="+263 77 123 4567"
						/>
					</div>
				</div>
			</div>

			<!-- Job Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Job Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="department">Department *</Label>
						<Select
							options={departmentOptions}
							bind:value={formData.department}
							error={errors.department}
							disabled={isSubmitting}
							required
							placeholder="Select department..."
						/>
					</div>
					<div>
						<Label for="employee_number">Employee Number *</Label>
						<Input
							id="employee_number"
							bind:value={formData.employee_number}
							error={errors.employee_number}
							disabled={isSubmitting}
							required
							placeholder="Auto-generated"
						/>
					</div>
					<div>
						<Label for="position">Position *</Label>
						<Input
							id="position"
							bind:value={formData.position}
							error={errors.position}
							disabled={isSubmitting}
							required
							placeholder="Software Developer"
						/>
					</div>
					<div>
						<Label for="manager">Manager</Label>
						<Select
							options={managerOptions}
							bind:value={formData.manager}
							error={errors.manager}
							disabled={isSubmitting}
							placeholder="Select manager..."
						/>
					</div>
					<div>
						<Label for="hire_date">Hire Date *</Label>
						<Input
							id="hire_date"
							type="date"
							bind:value={formData.hire_date}
							error={errors.hire_date}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="status">Status *</Label>
						<Select
							options={statusOptions}
							bind:value={formData.status}
							error={errors.status}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="salary">Annual Salary</Label>
						<Input
							id="salary"
							type="number"
							bind:value={formData.salary}
							error={errors.salary}
							disabled={isSubmitting}
							placeholder="75000"
						/>
					</div>
				</div>
			</div>

			<!-- Additional Information -->
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-4">Additional Information</h3>
				<div class="grid grid-cols-1 gap-4">
					<div>
						<Label for="address">Address</Label>
						<Input
							id="address"
							bind:value={formData.address}
							error={errors.address}
							disabled={isSubmitting}
							placeholder="123 Main Street, Harare, Zimbabwe"
						/>
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<Label for="emergency_contact_name">Emergency Contact Name</Label>
							<Input
								id="emergency_contact_name"
								bind:value={formData.emergency_contact_name}
								error={errors.emergency_contact_name}
								disabled={isSubmitting}
								placeholder="Jane Doe"
							/>
						</div>
						<div>
							<Label for="emergency_contact_phone">Emergency Contact Phone</Label>
							<Input
								id="emergency_contact_phone"
								type="tel"
								bind:value={formData.emergency_contact_phone}
								error={errors.emergency_contact_phone}
								disabled={isSubmitting}
								placeholder="+263 77 987 6543"
							/>
						</div>
					</div>
					<div>
						<Label for="skills">Skills (comma-separated)</Label>
						<Input
							id="skills"
							bind:value={formData.skills}
							error={errors.skills}
							disabled={isSubmitting}
							placeholder="JavaScript, TypeScript, Svelte, Node.js"
						/>
					</div>
				</div>
			</div>
		</form>
	</Card>
</div>
