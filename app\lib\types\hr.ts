// HR System Types - Mirror DB Schema
export interface Organization {
	id: string;
	name: string;
	code: string;
	logo_url?: string;
	created_at: string;
	updated_at: string;
}

export interface Department {
	id: string;
	name: string;
	code: string;
	org_id: string;
	manager_id?: string;
	created_at: string;
	updated_at: string;
}

export interface Employee {
	id: string;
	employee_number: string;
	first_name: string;
	last_name: string;
	email: string;
	phone?: string;
	department_id?: string;
	position?: string;
	hire_date: string;
	status: 'active' | 'inactive' | 'terminated';
	auth_user_id?: string;
	org_id: string;
	created_at: string;
	updated_at: string;
}

export interface User {
	id: string;
	email: string;
	user_role: 'super_admin' | 'hr_admin' | 'manager' | 'employee';
	org_id?: string;
	created_at: string;
	updated_at: string;
}

export interface Contract {
	id: string;
	employee_id: string;
	contract_type: 'permanent' | 'temporary' | 'contract' | 'internship';
	start_date: string;
	end_date?: string;
	salary?: number;
	currency?: string;
	status: 'draft' | 'active' | 'expired' | 'terminated';
	document_path?: string;
	created_at: string;
	updated_at: string;
}

export interface LeaveRequest {
	id: string;
	employee_id: string;
	leave_type: 'annual' | 'sick' | 'maternity' | 'paternity' | 'unpaid' | 'other';
	start_date: string;
	end_date: string;
	days_requested: number;
	reason?: string;
	status: 'pending' | 'approved' | 'rejected' | 'cancelled';
	approved_by?: string;
	approved_at?: string;
	created_at: string;
	updated_at: string;
}

export interface Document {
	id: string;
	employee_id?: string;
	contract_id?: string;
	document_type: 'contract' | 'id_copy' | 'cv' | 'certificate' | 'payslip' | 'other';
	file_name: string;
	file_size: number;
	mime_type: string;
	storage_path: string;
	scan_status: 'pending_scan' | 'clean' | 'quarantined';
	uploaded_by: string;
	created_at: string;
	updated_at: string;
}

// UI State Types
export interface AuthState {
	user: User | null;
	employee: Employee | null;
	isAuthenticated: boolean;
	isLoading: boolean;
}

export interface UIState {
	sidebarOpen: boolean;
	theme: 'light' | 'dark';
	notifications: Notification[];
	isLoading: boolean;
}

export interface Notification {
	id: string;
	type: 'success' | 'error' | 'warning' | 'info';
	title: string;
	message: string;
	timestamp: string;
	read: boolean;
}

// Form Types
export interface LoginForm {
	email: string;
	password: string;
}

export interface EmployeeForm {
	employee_number: string;
	first_name: string;
	last_name: string;
	email: string;
	phone?: string;
	department_id?: string;
	position?: string;
	hire_date: string;
}

export interface LeaveForm {
	leave_type: LeaveRequest['leave_type'];
	start_date: string;
	end_date: string;
	reason?: string;
}

// API Response Types
export interface ApiResponse<T> {
	data: T;
	message?: string;
	success: boolean;
}

export interface PaginatedResponse<T> {
	data: T[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

// Navigation Types
export interface NavItem {
	id: string;
	label: string;
	href: string;
	icon?: string;
	badge?: number;
	children?: NavItem[];
	roles?: User['user_role'][];
}

// Mock Data Types
export interface MockUser extends User {
	employee?: Employee;
	organization?: Organization;
}

export interface MockApiOptions {
	delay?: number;
	shouldFail?: boolean;
	failureRate?: number;
}
