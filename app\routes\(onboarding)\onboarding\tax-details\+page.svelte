<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore, type TaxInfo } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const taxInfo = $derived(store.data.taxInfo || {});

	// Form state
	let formData = $state({
		taxId: taxInfo.taxId || '',
		taxStatus: taxInfo.taxStatus || 'Resident',
		preferredPayslipFormat: taxInfo.preferredPayslipFormat || 'PDF',
		payFrequency: taxInfo.payFrequency || 'Monthly'
	});

	// Validation state
	let errors = $state<Record<string, string>>({});

	// Options
	const taxStatusOptions = [
		{ value: 'Resident', label: 'Resident' },
		{ value: 'Non-Resident', label: 'Non-Resident' },
		{ value: 'Exempt', label: 'Exempt' }
	];

	const payslipFormatOptions = [
		{ value: 'PDF', label: 'PDF Document' },
		{ value: 'Email', label: 'Email Only' }
	];

	const payFrequencyOptions = [
		{ value: 'Monthly', label: 'Monthly' },
		{ value: 'Bi-weekly', label: 'Bi-weekly' }
	];

	// Validation functions
	const validateTaxId = (taxId: string) => {
		// Basic validation for tax ID format (adjust based on country requirements)
		const taxIdRegex = /^[A-Z0-9]{6,20}$/;
		return taxIdRegex.test(taxId.replace(/\s/g, ''));
	};

	// Form validation
	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		// Required fields
		if (!formData.taxId.trim()) newErrors.taxId = 'Tax ID is required';
		if (!formData.taxStatus.trim()) newErrors.taxStatus = 'Tax status is required';
		if (!formData.preferredPayslipFormat.trim()) newErrors.preferredPayslipFormat = 'Payslip format is required';
		if (!formData.payFrequency.trim()) newErrors.payFrequency = 'Pay frequency is required';

		// Format validation
		if (formData.taxId && !validateTaxId(formData.taxId)) {
			newErrors.taxId = 'Please enter a valid tax ID format';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Auto-save functionality
	let saveTimeout: NodeJS.Timeout;
	const autoSave = () => {
		clearTimeout(saveTimeout);
		saveTimeout = setTimeout(() => {
			onboardingStore.updateTaxInfo(formData);
		}, 1000);
	};

	// Handle input changes
	const handleInputChange = () => {
		autoSave();
	};

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		if (validateForm()) {
			onboardingStore.updateTaxInfo(formData);
			onboardingStore.completeStep('tax-details');
			onboardingStore.setCurrentStep('bank-details');
			notificationStore.add({
				type: 'success',
				message: 'Tax details saved successfully!'
			});
			goto('/onboarding/bank-details');
		} else {
			notificationStore.add({
				type: 'error',
				message: 'Please fix the errors below before continuing.'
			});
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/employment-details');
	};
</script>

<svelte:head>
	<title>Tax Details - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Tax Details"
		subtitle="Please provide your tax information for payroll setup. This information is required for compliance and will be kept secure."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		onBack={handleBack}
		onNext={handleNext}
	>
		<form class="space-y-8" on:submit|preventDefault={handleNext}>
			<!-- Tax Information -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Tax Information
				</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Tax ID -->
					<div class="space-y-2 md:col-span-2">
						<Label for="taxId" class="text-sm font-medium text-[#5C4024]">
							Tax ID Number <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="taxId"
							bind:value={formData.taxId}
							oninput={handleInputChange}
							placeholder="Enter your tax identification number"
							class="onboarding-input {errors.taxId ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.taxId}
							aria-describedby={errors.taxId ? 'taxId-error' : 'taxId-help'}
						/>
						{#if errors.taxId}
							<p id="taxId-error" class="text-xs text-[#EF4444]">{errors.taxId}</p>
						{:else}
							<p id="taxId-help" class="text-xs text-[#8A6A52]">
								Enter your official tax identification number as it appears on your tax documents
							</p>
						{/if}
					</div>

					<!-- Tax Status -->
					<div class="space-y-2">
						<Label for="taxStatus" class="text-sm font-medium text-[#5C4024]">
							Tax Status <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={taxStatusOptions}
							bind:value={formData.taxStatus}
							placeholder="Select your tax status"
							class="onboarding-input {errors.taxStatus ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.taxStatus}
							<p class="text-xs text-[#EF4444]">{errors.taxStatus}</p>
						{:else}
							<p class="text-xs text-[#8A6A52]">
								Select based on your tax residency status
							</p>
						{/if}
					</div>

					<!-- Pay Frequency -->
					<div class="space-y-2">
						<Label for="payFrequency" class="text-sm font-medium text-[#5C4024]">
							Pay Frequency <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={payFrequencyOptions}
							bind:value={formData.payFrequency}
							placeholder="Select pay frequency"
							class="onboarding-input {errors.payFrequency ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.payFrequency}
							<p class="text-xs text-[#EF4444]">{errors.payFrequency}</p>
						{:else}
							<p class="text-xs text-[#8A6A52]">
								How often you would like to receive your salary
							</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Payroll Preferences -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Payroll Preferences
				</h3>
				
				<div class="grid grid-cols-1 gap-6">
					<!-- Payslip Format -->
					<div class="space-y-2">
						<Label for="payslipFormat" class="text-sm font-medium text-[#5C4024]">
							Preferred Payslip Format <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={payslipFormatOptions}
							bind:value={formData.preferredPayslipFormat}
							placeholder="Select payslip format"
							class="onboarding-input {errors.preferredPayslipFormat ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.preferredPayslipFormat}
							<p class="text-xs text-[#EF4444]">{errors.preferredPayslipFormat}</p>
						{:else}
							<p class="text-xs text-[#8A6A52]">
								Choose how you'd like to receive your payslips each pay period
							</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Information Notice -->
			<div class="bg-[#E8F9EE] border border-[#10B981]/20 rounded-xl p-4">
				<div class="flex items-start gap-3">
					<div class="w-5 h-5 text-[#10B981] flex-shrink-0 mt-0.5">
						<svg fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
						</svg>
					</div>
					<div>
						<h4 class="font-medium text-[#027A3A] mb-1">Secure Information</h4>
						<p class="text-sm text-[#027A3A] leading-relaxed">
							Your tax information is encrypted and stored securely. Only authorized HR and payroll personnel have access to this data. 
							We comply with all local tax regulations and data protection laws.
						</p>
					</div>
				</div>
			</div>

			<!-- Payslip Preview -->
			{#if formData.payFrequency && formData.preferredPayslipFormat}
				<div class="bg-[#FCF8F2] border border-[#EDE0CF] rounded-xl p-4">
					<h4 class="font-medium text-[#1C1C1C] mb-3">Payslip Preview</h4>
					<div class="text-sm text-[#5C4024] space-y-1">
						<p><strong>Frequency:</strong> {formData.payFrequency}</p>
						<p><strong>Format:</strong> {formData.preferredPayslipFormat}</p>
						<p><strong>Tax Status:</strong> {formData.taxStatus}</p>
					</div>
				</div>
			{/if}

			<!-- Auto-save indicator -->
			{#if store.lastSaved}
				<div class="text-xs text-[#8A6A52] text-center py-2">
					Last saved: {store.lastSaved.toLocaleTimeString()}
				</div>
			{/if}
		</form>
	</OnboardingCard>
</div>
