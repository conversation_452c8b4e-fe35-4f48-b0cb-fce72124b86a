<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import { 
		Search, 
		Filter, 
		Eye, 
		Mail, 
		Phone, 
		MapPin, 
		Calendar,
		Star,
		Users,
		Clock,
		CheckCircle,
		XCircle
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock candidates data
	const candidates = [
		{
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			phone: '+263 77 555 0123',
			location: 'Harare, Zimbabwe',
			position_applied: 'Senior Software Developer',
			application_date: '2025-08-15',
			status: 'interview_scheduled',
			experience_years: 5,
			current_company: 'TechCorp Zimbabwe',
			skills: ['JavaScript', 'TypeScript', 'React', 'Node.js'],
			rating: 4.5,
			source: 'LinkedIn'
		},
		{
			id: 2,
			name: '<PERSON> Chen',
			email: '<EMAIL>',
			phone: '+263 77 555 0124',
			location: 'Bulawayo, Zimbabwe',
			position_applied: 'Marketing Manager',
			application_date: '2025-08-12',
			status: 'screening',
			experience_years: 7,
			current_company: 'Marketing Plus',
			skills: ['Digital Marketing', 'SEO', 'Content Strategy', 'Analytics'],
			rating: 4.2,
			source: 'Company Website'
		},
		{
			id: 3,
			name: 'Sarah Williams',
			email: '<EMAIL>',
			phone: '+263 77 555 0125',
			location: 'Harare, Zimbabwe',
			position_applied: 'Financial Analyst',
			application_date: '2025-08-10',
			status: 'offer_extended',
			experience_years: 4,
			current_company: 'Finance Solutions Ltd',
			skills: ['Financial Analysis', 'Excel', 'PowerBI', 'Budgeting'],
			rating: 4.8,
			source: 'Referral'
		},
		{
			id: 4,
			name: 'David Brown',
			email: '<EMAIL>',
			phone: '+263 77 555 0126',
			location: 'Victoria Falls, Zimbabwe',
			position_applied: 'Tour Guide',
			application_date: '2025-08-08',
			status: 'rejected',
			experience_years: 2,
			current_company: 'Adventure Tours',
			skills: ['Tourism', 'Languages', 'Customer Service', 'Wildlife Knowledge'],
			rating: 3.5,
			source: 'Job Portal'
		},
		{
			id: 5,
			name: 'Emma Davis',
			email: '<EMAIL>',
			phone: '+263 77 555 0127',
			location: 'Harare, Zimbabwe',
			position_applied: 'HR Coordinator',
			application_date: '2025-08-05',
			status: 'hired',
			experience_years: 3,
			current_company: 'People First HR',
			skills: ['Recruitment', 'Employee Relations', 'HRIS', 'Training'],
			rating: 4.6,
			source: 'LinkedIn'
		}
	];

	// Filter and search state
	let searchTerm = $state('');
	let statusFilter = $state('all');
	let positionFilter = $state('all');
	let sourceFilter = $state('all');

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Status' },
		{ value: 'applied', label: 'Applied' },
		{ value: 'screening', label: 'Screening' },
		{ value: 'interview_scheduled', label: 'Interview Scheduled' },
		{ value: 'interviewed', label: 'Interviewed' },
		{ value: 'offer_extended', label: 'Offer Extended' },
		{ value: 'hired', label: 'Hired' },
		{ value: 'rejected', label: 'Rejected' },
		{ value: 'withdrawn', label: 'Withdrawn' }
	];

	const positionOptions = [
		{ value: 'all', label: 'All Positions' },
		{ value: 'Senior Software Developer', label: 'Senior Software Developer' },
		{ value: 'Marketing Manager', label: 'Marketing Manager' },
		{ value: 'Financial Analyst', label: 'Financial Analyst' },
		{ value: 'Tour Guide', label: 'Tour Guide' },
		{ value: 'HR Coordinator', label: 'HR Coordinator' }
	];

	const sourceOptions = [
		{ value: 'all', label: 'All Sources' },
		{ value: 'LinkedIn', label: 'LinkedIn' },
		{ value: 'Company Website', label: 'Company Website' },
		{ value: 'Job Portal', label: 'Job Portal' },
		{ value: 'Referral', label: 'Referral' },
		{ value: 'Recruitment Agency', label: 'Recruitment Agency' }
	];

	// Computed filtered candidates
	const filteredCandidates = $derived(() => {
		return candidates.filter(candidate => {
			const matchesSearch = searchTerm === '' || 
				candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
				candidate.position_applied.toLowerCase().includes(searchTerm.toLowerCase());
			
			const matchesStatus = statusFilter === 'all' || candidate.status === statusFilter;
			const matchesPosition = positionFilter === 'all' || candidate.position_applied === positionFilter;
			const matchesSource = sourceFilter === 'all' || candidate.source === sourceFilter;

			return matchesSearch && matchesStatus && matchesPosition && matchesSource;
		});
	});

	// Status styling
	const getStatusInfo = (status: string) => {
		const statusMap = {
			applied: { color: 'bg-blue-100 text-blue-800', icon: Clock },
			screening: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
			interview_scheduled: { color: 'bg-purple-100 text-purple-800', icon: Calendar },
			interviewed: { color: 'bg-indigo-100 text-indigo-800', icon: CheckCircle },
			offer_extended: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
			hired: { color: 'bg-green-500 text-white', icon: CheckCircle },
			rejected: { color: 'bg-red-100 text-red-800', icon: XCircle },
			withdrawn: { color: 'bg-gray-100 text-gray-800', icon: XCircle }
		};
		return statusMap[status as keyof typeof statusMap] || statusMap.applied;
	};

	const renderStars = (rating: number) => {
		const stars = [];
		for (let i = 1; i <= 5; i++) {
			stars.push(i <= rating ? 'text-yellow-400' : 'text-gray-300');
		}
		return stars;
	};

	// Actions
	const handleViewCandidate = (candidateId: number) => {
		goto(`/candidates/${candidateId}`);
	};

	const handleSendEmail = (email: string) => {
		// In real app, this would open email client
		console.log('Send email to:', email);
	};

	const canViewCandidates = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin' || user?.user_role === 'manager';
</script>

<svelte:head>
	<title>Candidates - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Candidates</h1>
			<p class="text-muted-foreground">Manage job applicants and recruitment pipeline</p>
		</div>
	</div>

	<!-- Filters -->
	<Card class="p-4">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			<div>
				<Input
					placeholder="Search candidates..."
					bind:value={searchTerm}
					class="w-full"
				>
					<Search slot="icon" class="w-4 h-4" />
				</Input>
			</div>
			<div>
				<Select
					options={statusOptions}
					bind:value={statusFilter}
					placeholder="Filter by status"
				/>
			</div>
			<div>
				<Select
					options={positionOptions}
					bind:value={positionFilter}
					placeholder="Filter by position"
				/>
			</div>
			<div>
				<Select
					options={sourceOptions}
					bind:value={sourceFilter}
					placeholder="Filter by source"
				/>
			</div>
			<div class="flex items-center">
				<Button variant="outline" class="w-full">
					<Filter class="w-4 h-4" />
					More Filters
				</Button>
			</div>
		</div>
	</Card>

	<!-- Summary Stats -->
	<div class="grid grid-cols-1 md:grid-cols-5 gap-4">
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Candidates</p>
					<p class="text-2xl font-bold text-foreground">{filteredCandidates.length}</p>
				</div>
				<Users class="w-8 h-8 text-muted-foreground" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">In Process</p>
					<p class="text-2xl font-bold text-blue-600">
						{filteredCandidates.filter(c => ['screening', 'interview_scheduled', 'interviewed'].includes(c.status)).length}
					</p>
				</div>
				<Clock class="w-8 h-8 text-blue-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Offers Extended</p>
					<p class="text-2xl font-bold text-green-600">
						{filteredCandidates.filter(c => c.status === 'offer_extended').length}
					</p>
				</div>
				<CheckCircle class="w-8 h-8 text-green-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Hired</p>
					<p class="text-2xl font-bold text-green-700">
						{filteredCandidates.filter(c => c.status === 'hired').length}
					</p>
				</div>
				<CheckCircle class="w-8 h-8 text-green-700" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Rejected</p>
					<p class="text-2xl font-bold text-red-600">
						{filteredCandidates.filter(c => c.status === 'rejected').length}
					</p>
				</div>
				<XCircle class="w-8 h-8 text-red-600" />
			</div>
		</Card>
	</div>

	<!-- Candidates Grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		{#each filteredCandidates as candidate}
			{@const statusInfo = getStatusInfo(candidate.status)}
			{@const StatusIcon = statusInfo.icon}
			<Card class="p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick={() => handleViewCandidate(candidate.id)}>
				<div class="flex items-start justify-between mb-4">
					<div class="flex items-center gap-3">
						<div class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
							<span class="text-primary-foreground font-bold text-lg">
								{candidate.name.split(' ').map(n => n.charAt(0)).join('')}
							</span>
						</div>
						<div>
							<h3 class="text-lg font-semibold text-foreground">{candidate.name}</h3>
							<p class="text-sm text-muted-foreground">{candidate.position_applied}</p>
						</div>
					</div>
					<Badge class={statusInfo.color}>
						<StatusIcon class="w-3 h-3 mr-1" />
						{candidate.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
					</Badge>
				</div>

				<div class="space-y-2 mb-4">
					<div class="flex items-center gap-2 text-sm text-muted-foreground">
						<Mail class="w-4 h-4" />
						<span>{candidate.email}</span>
					</div>
					<div class="flex items-center gap-2 text-sm text-muted-foreground">
						<Phone class="w-4 h-4" />
						<span>{candidate.phone}</span>
					</div>
					<div class="flex items-center gap-2 text-sm text-muted-foreground">
						<MapPin class="w-4 h-4" />
						<span>{candidate.location}</span>
					</div>
					<div class="flex items-center gap-2 text-sm text-muted-foreground">
						<Calendar class="w-4 h-4" />
						<span>Applied: {new Date(candidate.application_date).toLocaleDateString()}</span>
					</div>
				</div>

				<div class="mb-4">
					<div class="flex items-center justify-between mb-2">
						<span class="text-sm font-medium text-foreground">Rating</span>
						<div class="flex items-center gap-1">
							{#each renderStars(candidate.rating) as starColor}
								<Star class="w-4 h-4 {starColor} fill-current" />
							{/each}
							<span class="text-sm text-muted-foreground ml-1">({candidate.rating})</span>
						</div>
					</div>
				</div>

				<div class="mb-4">
					<p class="text-sm font-medium text-foreground mb-2">Skills</p>
					<div class="flex flex-wrap gap-1">
						{#each candidate.skills.slice(0, 3) as skill}
							<Badge variant="secondary" class="text-xs bg-primary/10 text-primary">
								{skill}
							</Badge>
						{/each}
						{#if candidate.skills.length > 3}
							<Badge variant="secondary" class="text-xs bg-muted text-muted-foreground">
								+{candidate.skills.length - 3} more
							</Badge>
						{/if}
					</div>
				</div>

				<div class="flex items-center justify-between pt-4 border-t border-border">
					<div class="text-sm text-muted-foreground">
						{candidate.experience_years} years exp • {candidate.source}
					</div>
					{#if canViewCandidates}
						<div class="flex gap-2">
							<Button variant="outline" size="sm" onclick={(e) => { e.stopPropagation(); handleSendEmail(candidate.email); }}>
								<Mail class="w-4 h-4" />
							</Button>
							<Button variant="default" size="sm" onclick={(e) => { e.stopPropagation(); handleViewCandidate(candidate.id); }}>
								<Eye class="w-4 h-4" />
								View
							</Button>
						</div>
					{/if}
				</div>
			</Card>
		{/each}
	</div>

	{#if filteredCandidates.length === 0}
		<Card class="p-12 text-center">
			<Users class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-semibold text-foreground mb-2">No candidates found</h3>
			<p class="text-muted-foreground">Try adjusting your search criteria or filters.</p>
		</Card>
	{/if}
</div>
