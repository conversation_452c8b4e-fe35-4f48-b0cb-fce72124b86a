# 02 — Employee Lifecycle (Frontend-only)

Goal:
- Build the UI for requisitions, application listing, interview scheduling UI, onboarding checklist, and probation tracking — all frontend-only backed by mocks.

Routes:
- `/requisitions` — list + create requisition modal
- `/requisition/[id]` — view requisition, approval simulation
- `/candidates` — list applications
- `/candidates/[id]` — candidate profile, CV viewer (mocked pdf), scorecards
- `/onboard/[employeeId]/checklist` — onboarding checklist UI, tasks with completion states
- `/probation` — probation dashboard for HR role

Components:
- `RequisitionForm.svelte` — multi-step form (role, cost center, salary band)
- `CandidateCard.svelte` — shortlisting actions (score, note)
- `InterviewScheduler.svelte` — calendar UI that integrates with mocked calendar API (create / list slots)
- `OnboardingChecklist.svelte` — task list with assigned owners and progress bar

Data contracts (mocks):
- Requisition: `{id, org_id, title, department_id, salary_band, approvals: [{role, status, comment}]}`.
- Candidate: `{id, name, email, cv_url, scores: [{competency,score}], applied_at}`.
- Employee (on accepted): `{id, first_name, last_name, employee_number, status, hire_date, probation_end_date, documents: []}`

Workflows (UI flow for frontend):
1. HR opens `/requisitions` -> clicks create -> fills `RequisitionForm` -> UI shows approvals step -> mock returns `pending` approval.
2. After approval simulated, the requisition appears in `/requisitions` as `approved`.
3. Candidates appear in `/candidates`; clicking a candidate shows `Score` panel; panel allows "advance to interview" -> opens scheduler modal.
4. On offer acceptance (simulate), create a mock employee entry and navigate to `/onboard/[employeeId]/checklist`.

Acceptance criteria:
- Forms validate with `zod` and show errors like backend would.
- Candidate CV viewer displays a mocked PDF; upload flows use same `FileUpload` component.
- Onboarding checklist persists state in mock store and shows progress.

Testing:
- Vitest unit tests for components,
- Playwright e2e scenario: "Create requisition → approve → short-list candidate → schedule interview → mark offer accepted → create employee".
