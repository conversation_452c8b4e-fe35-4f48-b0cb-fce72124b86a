<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import ProfileForm from '$lib/components/ProfileForm.svelte';
	import { User, Settings, Mail, Phone, MapPin, UserCheck } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';

	const auth = $derived($authStore);
	const employee = $derived(auth.employee);

	// Form state
	let isEditing = $state(false);

	// Mock employee data with additional fields for ProfileForm
	const employeeData = $derived(() => ({
		id: employee?.id || '',
		firstName: employee?.first_name || 'John',
		lastName: employee?.last_name || 'Doe',
		email: employee?.email || '<EMAIL>',
		phone: employee?.phone || '+****************',
		address: '123 Main Street, Anytown, ST 12345',
		emergencyContact: '<PERSON>',
		emergencyPhone: '+****************'
	}));

	const handleEdit = () => {
		isEditing = true;
	};

	const handleCancel = () => {
		isEditing = false;
	};

	const handleSave = async (data: any) => {
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1000));

			// Update the employee data (in a real app, this would be an API call)
			console.log('Saving profile data:', data);

			notificationStore.success('Profile Updated', 'Your profile has been updated successfully.');
			isEditing = false;
		} catch (error) {
			notificationStore.error('Update Failed', 'Failed to update profile. Please try again.');
		}
	};
</script>

<svelte:head>
	<title>Profile - HRIMS</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Page Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-2xl font-bold text-foreground">My Profile</h1>
			<p class="text-muted-foreground">Manage your personal information and settings</p>
		</div>
		<div class="flex gap-2">
			{#if !isEditing}
				<Button onclick={handleEdit} class="flex items-center gap-2">
					<Settings class="w-4 h-4" />
					Edit Profile
				</Button>
			{/if}
		</div>
	</div>

	{#if !isEditing}
		<!-- Profile View -->
		<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
			<!-- Profile Picture & Basic Info -->
			<div class="lg:col-span-1">
				<Card>
					<CardContent class="p-6">
						<div class="text-center">
							<div class="relative inline-block">
								<div class="w-32 h-32 bg-gradient-to-br from-primary to-primary/80 rounded-full flex items-center justify-center mx-auto mb-4">
									<span class="text-4xl font-bold text-primary-foreground">
										{employeeData().firstName?.charAt(0) || 'U'}{employeeData().lastName?.charAt(0) || ''}
									</span>
								</div>
							</div>
							<h2 class="text-xl font-semibold text-foreground">
								{employeeData().firstName} {employeeData().lastName}
							</h2>
							<p class="text-muted-foreground">{employeeData().email}</p>
							<p class="text-sm text-muted-foreground mt-1">Employee ID: {employeeData().id || 'EMP001'}</p>
						</div>
					</CardContent>
				</Card>

				<!-- Quick Stats -->
				<Card class="mt-6">
					<CardHeader>
						<CardTitle>Quick Stats</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="space-y-3">
							<div class="flex justify-between items-center">
								<span class="text-muted-foreground">Years of Service</span>
								<span class="font-medium">2.5 years</span>
							</div>
							<div class="flex justify-between items-center">
								<span class="text-muted-foreground">Leave Balance</span>
								<span class="font-medium">18 days</span>
							</div>
							<div class="flex justify-between items-center">
								<span class="text-muted-foreground">Performance Rating</span>
								<span class="font-medium text-green-600">Excellent</span>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>

			<!-- Profile Details -->
			<div class="lg:col-span-2">
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<User class="w-5 h-5" />
							Personal Information
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div class="space-y-2">
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<Mail class="w-4 h-4" />
									<span>Email</span>
								</div>
								<p class="font-medium">{employeeData().email}</p>
							</div>
							<div class="space-y-2">
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<Phone class="w-4 h-4" />
									<span>Phone</span>
								</div>
								<p class="font-medium">{employeeData().phone}</p>
							</div>
							<div class="space-y-2 md:col-span-2">
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<MapPin class="w-4 h-4" />
									<span>Address</span>
								</div>
								<p class="font-medium">{employeeData().address}</p>
							</div>
							<div class="space-y-2">
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<UserCheck class="w-4 h-4" />
									<span>Emergency Contact</span>
								</div>
								<p class="font-medium">{employeeData().emergencyContact}</p>
							</div>
							<div class="space-y-2">
								<div class="flex items-center gap-2 text-sm text-muted-foreground">
									<Phone class="w-4 h-4" />
									<span>Emergency Phone</span>
								</div>
								<p class="font-medium">{employeeData().emergencyPhone}</p>
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	{:else}
		<!-- Profile Edit Form -->
		<ProfileForm
			employee={employeeData()}
			isEditing={isEditing}
			onSave={handleSave}
			onCancel={handleCancel}
		/>
	{/if}
</div>
