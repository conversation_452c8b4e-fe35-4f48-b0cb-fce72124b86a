<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { uiStore } from '$lib/stores/uiStore';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Card from '$lib/components/ui/Card.svelte';

	let email = $state('');
	let password = $state('');
	let isLoading = $state(false);
	let error = $state('');

	// Demo accounts for testing
	const demoAccounts = [
		{ email: '<EMAIL>', password: 'admin123', role: 'HR Admin' },
		{ email: '<EMAIL>', password: 'manager123', role: 'Manager' },
		{ email: '<EMAIL>', password: 'employee123', role: 'Employee' }
	];

	const handleLogin = async () => {
		if (!email || !password) {
			error = 'Please enter both email and password';
			return;
		}

		isLoading = true;
		error = '';

		try {
			const result = await authStore.login(email, password);
			
			if (result.success) {
				uiStore.showSuccess('Welcome!', 'You have successfully logged in.');
				goto('/');
			} else {
				error = result.error || 'Login failed';
			}
		} catch (err) {
			error = 'An unexpected error occurred';
			console.error('Login error:', err);
		} finally {
			isLoading = false;
		}
	};

	const handleDemoLogin = (demoEmail: string, demoPassword: string) => {
		email = demoEmail;
		password = demoPassword;
		handleLogin();
	};

	const handleKeyPress = (event: KeyboardEvent) => {
		if (event.key === 'Enter') {
			handleLogin();
		}
	};
</script>

<svelte:head>
	<title>Login - HRIMS</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-muted/50 via-background to-muted/30 flex items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- RTG Branding -->
		<div class="text-center mb-8">
			<div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
				<span class="text-primary-foreground font-bold text-2xl">RTG</span>
			</div>
			<h1 class="text-3xl font-bold text-foreground mb-2">Welcome to HRIMS</h1>
			<p class="text-muted-foreground">Human Resource Information Management System</p>
		</div>

		<!-- Login Form -->
		<Card variant="elevated" padding="lg">
			{#snippet children()}
				<form onsubmit={(e) => { e.preventDefault(); handleLogin(); }} class="space-y-6">
					<div>
						<Input
							id="email"
							type="email"
							label="Email Address"
							bind:value={email}
							placeholder="Enter your email"
							required
							disabled={isLoading}
							onkeypress={handleKeyPress}
						/>
					</div>

					<div>
						<Input
							id="password"
							type="password"
							label="Password"
							bind:value={password}
							placeholder="Enter your password"
							required
							disabled={isLoading}
							onkeypress={handleKeyPress}
						/>
					</div>

					{#if error}
						<div class="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md text-sm">
							{error}
						</div>
					{/if}

					<Button
						type="submit"
						variant="primary"
						size="lg"
						fullWidth
						loading={isLoading}
						disabled={isLoading}
					>
						{#snippet children()}
							{isLoading ? 'Signing In...' : 'Sign In'}
						{/snippet}
					</Button>
				</form>

				<!-- Demo Accounts -->
				<div class="mt-8 pt-6 border-t border-border">
					<h3 class="text-sm font-medium text-foreground mb-4 text-center">Demo Accounts</h3>
					<div class="space-y-2">
						{#each demoAccounts as account}
							<button
								onclick={() => handleDemoLogin(account.email, account.password)}
								disabled={isLoading}
								class="w-full text-left p-3 rounded-lg border border-border hover:bg-muted transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								<div class="flex justify-between items-center">
									<div>
										<p class="text-sm font-medium text-foreground">{account.email}</p>
										<p class="text-xs text-muted-foreground">{account.role}</p>
									</div>
									<svg class="w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
									</svg>
								</div>
							</button>
						{/each}
					</div>
				</div>
			{/snippet}
		</Card>

		<!-- Footer -->
		<div class="text-center mt-8 text-sm text-muted-foreground">
			<p>&copy; 2024 Rainbow Tourism Group. All rights reserved.</p>
		</div>
	</div>
</div>
