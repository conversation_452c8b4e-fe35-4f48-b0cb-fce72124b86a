<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Upload, File, CheckCircle, AlertCircle, X, Eye } from '@lucide/svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const documents = $derived(store.data.documents || []);

	// Document upload state
	let uploadedFiles = $state<Array<{
		id: string;
		name: string;
		type: string;
		size: number;
		status: 'uploading' | 'uploaded' | 'verified' | 'rejected';
		progress: number;
		url?: string;
	}>>(documents.map(doc => ({
		id: doc.id,
		name: doc.name,
		type: doc.type,
		size: 0,
		status: doc.status === 'pending' ? 'uploading' : 
			   doc.status === 'uploaded' ? 'uploaded' :
			   doc.status === 'verified' ? 'verified' : 'rejected',
		progress: 100
	})));

	let isDragOver = $state(false);

	// Required document types
	const requiredDocuments = [
		{
			type: 'id-front',
			title: 'ID Document (Front)',
			description: 'Clear photo of the front of your national ID or passport',
			required: true
		},
		{
			type: 'id-back',
			title: 'ID Document (Back)',
			description: 'Clear photo of the back of your national ID',
			required: true
		},
		{
			type: 'qualification',
			title: 'Educational Qualification',
			description: 'Degree, diploma, or certificate relevant to your position',
			required: true
		},
		{
			type: 'cv',
			title: 'Curriculum Vitae',
			description: 'Your most recent CV or resume',
			required: false
		}
	];

	// File validation
	const validateFile = (file: File) => {
		const maxSize = 10 * 1024 * 1024; // 10MB
		const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];

		if (file.size > maxSize) {
			return 'File size must be less than 10MB';
		}

		if (!allowedTypes.includes(file.type)) {
			return 'Only JPEG, PNG, and PDF files are allowed';
		}

		return null;
	};

	// Handle file upload
	const handleFileUpload = async (files: FileList, documentType?: string) => {
		for (const file of Array.from(files)) {
			const error = validateFile(file);
			if (error) {
				notificationStore.add({
					type: 'error',
					message: `${file.name}: ${error}`
				});
				continue;
			}

			const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
			const newFile = {
				id: fileId,
				name: file.name,
				type: documentType || 'general',
				size: file.size,
				status: 'uploading' as const,
				progress: 0
			};

			uploadedFiles = [...uploadedFiles, newFile];

			// Simulate upload progress
			const uploadInterval = setInterval(() => {
				uploadedFiles = uploadedFiles.map(f => {
					if (f.id === fileId && f.progress < 100) {
						return { ...f, progress: Math.min(f.progress + 10, 100) };
					}
					return f;
				});
			}, 200);

			// Simulate upload completion
			setTimeout(() => {
				clearInterval(uploadInterval);
				uploadedFiles = uploadedFiles.map(f => {
					if (f.id === fileId) {
						return { ...f, status: 'uploaded' as const, progress: 100 };
					}
					return f;
				});

				// Simulate verification after upload
				setTimeout(() => {
					uploadedFiles = uploadedFiles.map(f => {
						if (f.id === fileId) {
							// 90% chance of verification success
							const isVerified = Math.random() > 0.1;
							return { 
								...f, 
								status: isVerified ? 'verified' as const : 'rejected' as const 
							};
						}
						return f;
					});

					if (uploadedFiles.find(f => f.id === fileId)?.status === 'verified') {
						notificationStore.add({
							type: 'success',
							message: `${file.name} uploaded and verified successfully!`
						});
					} else {
						notificationStore.add({
							type: 'error',
							message: `${file.name} could not be verified. Please try uploading again.`
						});
					}
				}, 1500);
			}, 2000);
		}
	};

	// Handle drag and drop
	const handleDragOver = (e: DragEvent) => {
		e.preventDefault();
		isDragOver = true;
	};

	const handleDragLeave = (e: DragEvent) => {
		e.preventDefault();
		isDragOver = false;
	};

	const handleDrop = (e: DragEvent) => {
		e.preventDefault();
		isDragOver = false;
		
		if (e.dataTransfer?.files) {
			handleFileUpload(e.dataTransfer.files);
		}
	};

	// Remove file
	const removeFile = (fileId: string) => {
		uploadedFiles = uploadedFiles.filter(f => f.id !== fileId);
		notificationStore.add({
			type: 'info',
			message: 'Document removed'
		});
	};

	// Get status icon
	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'uploading':
				return 'animate-spin rounded-full h-4 w-4 border-b-2 border-[#F59E0B]';
			case 'uploaded':
				return CheckCircle;
			case 'verified':
				return CheckCircle;
			case 'rejected':
				return AlertCircle;
			default:
				return File;
		}
	};

	// Get status color
	const getStatusColor = (status: string) => {
		switch (status) {
			case 'uploading':
				return 'text-[#F59E0B]';
			case 'uploaded':
				return 'text-[#0EA5E9]';
			case 'verified':
				return 'text-[#10B981]';
			case 'rejected':
				return 'text-[#EF4444]';
			default:
				return 'text-[#8A6A52]';
		}
	};

	// Check if all required documents are uploaded and verified
	const allRequiredDocumentsReady = $derived(() => {
		const requiredTypes = requiredDocuments.filter(doc => doc.required).map(doc => doc.type);
		return requiredTypes.every(type => 
			uploadedFiles.some(file => file.type === type && file.status === 'verified')
		);
	});

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		// Update store with documents
		const documentsData = uploadedFiles.map(file => ({
			id: file.id,
			name: file.name,
			type: file.type,
			status: file.status === 'verified' ? 'verified' as const :
					file.status === 'uploaded' ? 'uploaded' as const :
					file.status === 'rejected' ? 'rejected' as const : 'pending' as const
		}));

		onboardingStore.updatePersonalDetails({ 
			...store.data.personalDetails,
			documents: documentsData 
		});

		onboardingStore.completeStep('documents');
		onboardingStore.setCurrentStep('agreements');
		notificationStore.add({
			type: 'success',
			message: 'Documents saved successfully!'
		});
		goto('/onboarding/agreements');
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/bank-details');
	};
</script>

<svelte:head>
	<title>Documents - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Upload Documents"
		subtitle="Please upload the required documents for verification. All files are encrypted and stored securely."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		nextButtonDisabled={!allRequiredDocumentsReady}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-8">
			<!-- Required Documents List -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Required Documents
				</h3>
				
				<div class="grid gap-4">
					{#each requiredDocuments as docType}
						{@const uploadedDoc = uploadedFiles.find(f => f.type === docType.type)}
						<div class="border border-[#EDE0CF] rounded-xl p-4">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<h4 class="font-medium text-[#1C1C1C] flex items-center gap-2">
										{docType.title}
										{#if docType.required}
											<span class="text-[#EF4444] text-sm">*</span>
										{/if}
									</h4>
									<p class="text-sm text-[#8A6A52] mt-1">{docType.description}</p>
									
									{#if uploadedDoc}
										<div class="flex items-center gap-2 mt-2">
											{#if uploadedDoc.status === 'uploading'}
												<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-[#F59E0B]"></div>
											{:else}
												{@const IconComponent = getStatusIcon(uploadedDoc.status)}
												<svelte:component this={IconComponent} class="w-4 h-4 {getStatusColor(uploadedDoc.status)}" />
											{/if}
											<span class="text-sm {getStatusColor(uploadedDoc.status)}">
												{uploadedDoc.name} - {uploadedDoc.status}
											</span>
											{#if uploadedDoc.status !== 'uploading'}
												<button
													onclick={() => removeFile(uploadedDoc.id)}
													class="text-[#EF4444] hover:text-[#DC2626] ml-2"
												>
													<X class="w-4 h-4" />
												</button>
											{/if}
										</div>
									{/if}
								</div>
								
								{#if !uploadedDoc || uploadedDoc.status === 'rejected'}
									<div>
										<input
											type="file"
											accept=".jpg,.jpeg,.png,.pdf"
											class="hidden"
											id="file-{docType.type}"
											onchange={(e) => {
												const files = e.target?.files;
												if (files) handleFileUpload(files, docType.type);
											}}
										/>
										<Button
											type="button"
											variant="outline"
											size="sm"
											onclick={() => document.getElementById(`file-${docType.type}`)?.click()}
										>
											<Upload class="w-4 h-4 mr-2" />
											Upload
										</Button>
									</div>
								{/if}
							</div>
							
							{#if uploadedDoc?.status === 'uploading'}
								<div class="mt-3">
									<div class="w-full bg-[#F0E1CB] rounded-full h-2">
										<div 
											class="bg-gradient-to-r from-[#F5D6A1] to-[#C49A6C] h-2 rounded-full transition-all duration-300"
											style="width: {uploadedDoc.progress}%"
										></div>
									</div>
								</div>
							{/if}
						</div>
					{/each}
				</div>
			</div>
		</div>
	</OnboardingCard>
</div>
