// Comprehensive Onboarding Flow Test
// Tests all 13 steps of the HRIMS onboarding process

import { test, expect } from '@playwright/test';

const BASE_URL = 'http://localhost:5174';
const TESTING_PARAM = '?testing=true';

// Test data
const testData = {
  invite: {
    token: 'test-invite-token-123',
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  personal: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    dateOfBirth: '1990-01-01',
    address: '123 Test Street',
    city: 'Test City',
    state: 'Test State',
    zipCode: '12345',
    emergencyContactName: '<PERSON>',
    emergencyContactPhone: '+**********',
    emergencyContactRelationship: 'spouse'
  },
  employment: {
    department: 'Human Resources',
    position: 'HR Specialist',
    startDate: '2024-09-01',
    employmentType: 'full-time',
    manager: '<EMAIL>'
  }
};

test.describe('HRIMS Onboarding Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up any necessary cookies or local storage
    await page.goto(BASE_URL);
  });

  test('Step 1: Invite and Account Creation', async ({ page }) => {
    console.log('Testing Step 1: Invite and Account Creation');
    
    // Navigate to invite page with token
    await page.goto(`${BASE_URL}/onboarding/invite?token=${testData.invite.token}`);
    
    // Wait for validation
    await page.waitForSelector('text=Invitation validated successfully!', { timeout: 10000 });
    
    // Take screenshot
    await page.screenshot({ path: 'screenshots/step1-invite-validated.png' });
    
    // Click Accept Invite
    await page.click('button:has-text("Accept Invite & Set Password")');
    
    // Fill password form
    await page.fill('input[type="password"]:first-of-type', testData.invite.password);
    await page.fill('input[type="password"]:last-of-type', testData.invite.password);
    
    // Take screenshot
    await page.screenshot({ path: 'screenshots/step1-password-form.png' });
    
    // Submit password form
    await page.click('button:has-text("Create Account & Continue")');
    
    // Wait for navigation or success
    await page.waitForTimeout(3000);
    await page.screenshot({ path: 'screenshots/step1-completed.png' });
  });

  test('Step 2: Company Intro + Tour', async ({ page }) => {
    console.log('Testing Step 2: Company Intro + Tour');
    
    // Navigate directly to welcome page with testing mode
    await page.goto(`${BASE_URL}/onboarding/welcome${TESTING_PARAM}`);
    
    // Wait for page to load
    await page.waitForSelector('text=Welcome to Rainbow Tourism Group');
    
    // Take screenshot
    await page.screenshot({ path: 'screenshots/step2-welcome.png' });
    
    // Start guided tour
    await page.click('button:has-text("Take a Guided Tour")');
    
    // Go through tour steps
    for (let i = 1; i <= 4; i++) {
      await page.waitForSelector(`text=Step ${i} of 4`);
      await page.screenshot({ path: `screenshots/step2-tour-${i}.png` });
      
      if (i < 4) {
        await page.click('button:has-text("Next")');
      } else {
        await page.click('button:has-text("Get Started")');
      }
      await page.waitForTimeout(1000);
    }
  });

  test('Step 3: Personal Details', async ({ page }) => {
    console.log('Testing Step 3: Personal Details');
    
    // Navigate to personal details page
    await page.goto(`${BASE_URL}/onboarding/personal${TESTING_PARAM}`);
    
    // Check if page loads or has errors
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 3: Personal Details page has errors');
      await page.screenshot({ path: 'screenshots/step3-error.png' });
      return;
    }
    
    console.log('✅ Step 3: Personal Details page loaded successfully');
    await page.screenshot({ path: 'screenshots/step3-personal.png' });
  });

  test('Step 4: Employment Details', async ({ page }) => {
    console.log('Testing Step 4: Employment Details');
    
    await page.goto(`${BASE_URL}/onboarding/employment${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 4: Employment Details page has errors');
      await page.screenshot({ path: 'screenshots/step4-error.png' });
      return;
    }
    
    console.log('✅ Step 4: Employment Details page loaded successfully');
    await page.screenshot({ path: 'screenshots/step4-employment.png' });
  });

  test('Step 5: Tax & Payroll', async ({ page }) => {
    console.log('Testing Step 5: Tax & Payroll');
    
    await page.goto(`${BASE_URL}/onboarding/tax${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 5: Tax & Payroll page has errors');
      await page.screenshot({ path: 'screenshots/step5-error.png' });
      return;
    }
    
    console.log('✅ Step 5: Tax & Payroll page loaded successfully');
    await page.screenshot({ path: 'screenshots/step5-tax.png' });
  });

  test('Step 6: Bank Details', async ({ page }) => {
    console.log('Testing Step 6: Bank Details');
    
    await page.goto(`${BASE_URL}/onboarding/bank${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 6: Bank Details page has errors');
      await page.screenshot({ path: 'screenshots/step6-error.png' });
      return;
    }
    
    console.log('✅ Step 6: Bank Details page loaded successfully');
    await page.screenshot({ path: 'screenshots/step6-bank.png' });
  });

  test('Step 7: Documents Upload & Scan', async ({ page }) => {
    console.log('Testing Step 7: Documents Upload & Scan');
    
    await page.goto(`${BASE_URL}/onboarding/documents${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 7: Documents page has errors');
      await page.screenshot({ path: 'screenshots/step7-error.png' });
      return;
    }
    
    console.log('✅ Step 7: Documents page loaded successfully');
    await page.screenshot({ path: 'screenshots/step7-documents.png' });
  });

  test('Step 8: Contracts & E-Sign', async ({ page }) => {
    console.log('Testing Step 8: Contracts & E-Sign');
    
    await page.goto(`${BASE_URL}/onboarding/contract${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 8: Contract page has errors');
      await page.screenshot({ path: 'screenshots/step8-error.png' });
      return;
    }
    
    console.log('✅ Step 8: Contract page loaded successfully');
    await page.screenshot({ path: 'screenshots/step8-contract.png' });
  });

  test('Step 9: Policy Acknowledgement', async ({ page }) => {
    console.log('Testing Step 9: Policy Acknowledgement');
    
    await page.goto(`${BASE_URL}/onboarding/policies${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 9: Policies page has errors');
      await page.screenshot({ path: 'screenshots/step9-error.png' });
      return;
    }
    
    console.log('✅ Step 9: Policies page loaded successfully');
    await page.screenshot({ path: 'screenshots/step9-policies.png' });
  });

  test('Step 10: Training & First Week Plan', async ({ page }) => {
    console.log('Testing Step 10: Training & First Week Plan');
    
    await page.goto(`${BASE_URL}/onboarding/training${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 10: Training page has errors');
      await page.screenshot({ path: 'screenshots/step10-error.png' });
      return;
    }
    
    console.log('✅ Step 10: Training page loaded successfully');
    await page.screenshot({ path: 'screenshots/step10-training.png' });
  });

  test('Step 11: Equipment & Asset Requests', async ({ page }) => {
    console.log('Testing Step 11: Equipment & Asset Requests');
    
    await page.goto(`${BASE_URL}/onboarding/assets${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 11: Assets page has errors');
      await page.screenshot({ path: 'screenshots/step11-error.png' });
      return;
    }
    
    console.log('✅ Step 11: Assets page loaded successfully');
    await page.screenshot({ path: 'screenshots/step11-assets.png' });
  });

  test('Step 12: Manager Approval & Checklist', async ({ page }) => {
    console.log('Testing Step 12: Manager Approval & Checklist');
    
    await page.goto(`${BASE_URL}/onboarding/approvals${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 12: Approvals page has errors');
      await page.screenshot({ path: 'screenshots/step12-error.png' });
      return;
    }
    
    console.log('✅ Step 12: Approvals page loaded successfully');
    await page.screenshot({ path: 'screenshots/step12-approvals.png' });
  });

  test('Step 13: Final Confirmation & Welcome Pack', async ({ page }) => {
    console.log('Testing Step 13: Final Confirmation & Welcome Pack');
    
    await page.goto(`${BASE_URL}/onboarding/complete${TESTING_PARAM}`);
    
    const hasError = await page.locator('text=500').isVisible().catch(() => false);
    
    if (hasError) {
      console.log('❌ Step 13: Complete page has errors');
      await page.screenshot({ path: 'screenshots/step13-error.png' });
      return;
    }
    
    console.log('✅ Step 13: Complete page loaded successfully');
    await page.screenshot({ path: 'screenshots/step13-complete.png' });
  });
});
