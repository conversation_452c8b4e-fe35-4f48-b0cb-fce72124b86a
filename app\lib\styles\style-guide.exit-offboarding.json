{"meta": {"name": "Exit & Offboarding Style Guide", "file": "style-guide.exit-offboarding.json", "version": "1.0.0", "description": "Resignation, offboarding checklists, asset reclamation, final settlement and exit interview UI tokens. Uses RTG brand colors and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"taskBg": "#FFF7ED", "assetPending": "#F59E0B", "assetReclaimed": "#10B981", "finalSettlementBg": "#FCF8F2", "cta": "#8C6239"}, "gradients": {"offboardHero": "linear-gradient(135deg,#F5D6A1 0%,#8C6239 100%)"}, "components": {"offboardChecklist": {"taskRow": {"bg": "#FFFFFF", "ownerBadge": {"bg": "#F5D6A1", "text": "#1C1C1C"}, "duePill": {"bg": "#FFF7ED", "text": "#8C6239"}}, "progressSummary": {"barHeight": 12, "fill": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}}, "assetReclamation": {"row": {"iconSize": 20, "statusColors": {"reclaimed": "#10B981", "pending": "#F59E0B"}, "confirmBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}}, "finalSettlement": {"fields": ["finalPay", "deductions", "taxes", "notes"], "reviewNoteBg": "#FFF7ED", "cta": {"bg": "#8C6239", "text": "#FFFFFF"}}, "exitInterview": {"form": {"duration": "30min", "questions": 8}, "summaryPdf": {"previewBg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "downloadBtn": {"bg": "#C49A6C", "text": "#1C1C1C"}}}}, "interactions": {"autoChecklist": {"onResignation": "generate checklist and tasks"}, "revocationUi": {"lockReasonBadge": true, "disabledState": true}}, "accessibility": {"sensitiveData": "Provide controlled reveal for sensitive notes; redact by default"}, "notes": {"mocks": ["/api/offboard", "/api/assets", "/api/payroll/finalize"], "acceptance": ["Checklist auto-generation on resignation", "Final settlement validates numeric inputs and produces preview"]}}