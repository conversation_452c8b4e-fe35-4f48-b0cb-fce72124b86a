# 09 — Talent Management Matrix (Frontend-only)

Pages:
- `/talent` — 9-box matrix view
- `/talent/employee/[id]` — skill profile & recommended L&D

Components:
- `NineBox.svelte` — grid with drag/drop nominations
- `SkillEditor.svelte` — edit/add skills with proficiency level

Workflow:
1. Import mock performance & potential into matrix -> HR drags employee boxes to adjust `potential` or `performance`.
2. System recommends training (mock rule engine) -> shows links to `/training`.

Acceptance:
- Talent box movement persists in mock store,
- Suggested L&D shows links.
