{"meta": {"name": "Training & HCD Style Guide", "file": "style-guide.training.json", "version": "1.0.0", "description": "Training catalog, event detail, enrollment, attendance & certificate tokens aligned to RTG brand and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"cardBg": "#FFFFFF", "enrollBtn": "#8C6239", "capacityPill": "#F5D6A1", "attendancePresent": "#10B981", "attendanceAbsent": "#EF4444"}, "gradients": {"trainingCard": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "certificateGradient": "linear-gradient(90deg,#F5D6A1,#8C6239)"}, "components": {"trainingCard": {"thumbnailBg": "#FFF7ED", "title": {"color": "#1C1C1C", "weight": 600}, "enrollBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}, "enrollmentModal": {"bg": "#FFFFFF", "confirmBtn": {"bg": "#C49A6C", "text": "#1C1C1C"}}, "attendanceTracker": {"row": {"height": 64, "statusDot": {"present": "#10B981", "absent": "#EF4444"}}}, "certificate": {"previewBg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "downloadBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}}, "interactions": {"enroll": {"confirmToast": true, "seatLimit": "display waitlist"}, "attendance": {"instantMark": true}}, "accessibility": {"mediaTranscripts": "Provide captions/transcripts for training videos"}, "notes": {"mocks": ["/api/training", "/api/enrollments", "/api/attendance"], "acceptance": ["Training catalog shows cards and enrollment modal functions", "Certificates available after attendance"]}}