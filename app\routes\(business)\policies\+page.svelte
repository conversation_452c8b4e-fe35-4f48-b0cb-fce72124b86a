<script lang="ts">
	import { onMount } from 'svelte';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import { ScrollArea } from '$lib/components/ui/scroll-area/index.js';
	import { CheckCircle, Clock, AlertTriangle, FileText, Eye, Calendar, User, Search } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';

	interface Policy {
		id: string;
		title: string;
		description: string;
		category: 'hr' | 'safety' | 'it' | 'compliance' | 'general';
		version: string;
		effectiveDate: string;
		lastUpdated: string;
		mandatory: boolean;
		content: string;
		acknowledgementStatus: 'acknowledged' | 'pending' | 'overdue';
		acknowledgedAt?: string;
		acknowledgedBy?: string;
		dueDate?: string;
	}

	// Mock policy data
	let policies: Policy[] = [
		{
			id: '1',
			title: 'Code of Conduct',
			description: 'Company code of conduct and ethical guidelines for all employees',
			category: 'hr',
			version: '2.1',
			effectiveDate: '2024-01-01',
			lastUpdated: '2024-01-15',
			mandatory: true,
			content: `# Code of Conduct

## Introduction
This Code of Conduct outlines the ethical standards and behavioral expectations for all Rainbow Tourism Group employees.

## Core Values
- **Integrity**: Act honestly and transparently in all business dealings
- **Respect**: Treat all colleagues, customers, and stakeholders with dignity
- **Excellence**: Strive for the highest quality in all work
- **Accountability**: Take responsibility for actions and decisions

## Professional Behavior
- Maintain professional conduct at all times
- Respect confidentiality and proprietary information
- Avoid conflicts of interest
- Report any unethical behavior or violations

## Compliance
All employees must acknowledge and comply with this code. Violations may result in disciplinary action.`,
			acknowledgementStatus: 'acknowledged',
			acknowledgedAt: '2024-01-20',
			acknowledgedBy: 'John Doe'
		},
		{
			id: '2',
			title: 'Information Security Policy',
			description: 'Guidelines for protecting company data and IT systems',
			category: 'it',
			version: '3.0',
			effectiveDate: '2024-02-01',
			lastUpdated: '2024-02-01',
			mandatory: true,
			content: `# Information Security Policy

## Purpose
This policy establishes guidelines for protecting Rainbow Tourism Group's information assets.

## Password Requirements
- Minimum 12 characters
- Include uppercase, lowercase, numbers, and special characters
- Change every 90 days
- Do not reuse last 12 passwords

## Data Protection
- Classify data according to sensitivity levels
- Use encryption for sensitive data transmission
- Secure physical and digital storage
- Report security incidents immediately

## Access Control
- Use principle of least privilege
- Regular access reviews
- Multi-factor authentication required
- Secure remote access procedures

## Compliance
Failure to comply may result in disciplinary action and potential security breaches.`,
			acknowledgementStatus: 'pending',
			dueDate: '2024-04-15'
		},
		{
			id: '3',
			title: 'Workplace Safety Guidelines',
			description: 'Safety procedures and emergency protocols for all workplace locations',
			category: 'safety',
			version: '1.5',
			effectiveDate: '2024-01-15',
			lastUpdated: '2024-01-15',
			mandatory: true,
			content: `# Workplace Safety Guidelines

## General Safety Rules
- Report all accidents and near-misses immediately
- Use personal protective equipment when required
- Keep work areas clean and organized
- Follow all posted safety signs and procedures

## Emergency Procedures
- Know evacuation routes and assembly points
- Report emergencies to security immediately
- Do not use elevators during fire alarms
- Follow instructions from emergency personnel

## Health and Wellness
- Take regular breaks to prevent fatigue
- Report health and safety concerns
- Participate in safety training programs
- Maintain good ergonomic practices

## Incident Reporting
All incidents must be reported within 24 hours using the incident reporting system.`,
			acknowledgementStatus: 'overdue',
			dueDate: '2024-03-01'
		},
		{
			id: '4',
			title: 'Remote Work Policy',
			description: 'Guidelines and requirements for remote work arrangements',
			category: 'hr',
			version: '1.2',
			effectiveDate: '2024-03-01',
			lastUpdated: '2024-03-01',
			mandatory: false,
			content: `# Remote Work Policy

## Eligibility
- Employees with satisfactory performance ratings
- Roles suitable for remote work
- Manager approval required
- Probationary employees not eligible

## Requirements
- Dedicated workspace with reliable internet
- Maintain regular working hours
- Participate in virtual meetings as required
- Ensure data security and confidentiality

## Communication
- Daily check-ins with supervisor
- Use company communication tools
- Respond to messages within 4 hours
- Attend all scheduled meetings

## Performance
- Meet all job responsibilities and deadlines
- Maintain productivity levels
- Regular performance reviews
- Subject to periodic review`,
			acknowledgementStatus: 'acknowledged',
			acknowledgedAt: '2024-03-05',
			acknowledgedBy: 'John Doe'
		},
		{
			id: '5',
			title: 'Anti-Harassment Policy',
			description: 'Zero-tolerance policy on harassment and discrimination',
			category: 'compliance',
			version: '2.0',
			effectiveDate: '2024-01-01',
			lastUpdated: '2024-01-01',
			mandatory: true,
			content: `# Anti-Harassment Policy

## Policy Statement
Rainbow Tourism Group is committed to providing a workplace free from harassment and discrimination.

## Prohibited Conduct
- Sexual harassment
- Discrimination based on protected characteristics
- Bullying or intimidating behavior
- Retaliation against complainants

## Reporting Procedures
- Report incidents to HR or management
- Anonymous reporting options available
- Confidential investigation process
- Protection against retaliation

## Consequences
Violations will result in appropriate disciplinary action, up to and including termination.

## Support Resources
- Employee Assistance Program
- HR support and counseling
- External reporting agencies
- Legal resources if needed`,
			acknowledgementStatus: 'pending',
			dueDate: '2024-04-01'
		}
	];

	let filteredPolicies = $state(policies);
	let selectedCategory = $state<string>('all');
	let selectedStatus = $state<string>('all');
	let searchQuery = $state('');
	let selectedPolicy: Policy | null = $state(null);
	let isViewDialogOpen = $state(false);
	let acknowledgementChecked = $state(false);

	// Filter policies
	$effect(() => {
		filteredPolicies = policies.filter(policy => {
			const matchesCategory = selectedCategory === 'all' || policy.category === selectedCategory;
			const matchesStatus = selectedStatus === 'all' || policy.acknowledgementStatus === selectedStatus;
			const matchesSearch = policy.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
								policy.description.toLowerCase().includes(searchQuery.toLowerCase());
			return matchesCategory && matchesStatus && matchesSearch;
		});
	});

	const categories = [
		{ value: 'all', label: 'All Categories' },
		{ value: 'hr', label: 'HR Policies' },
		{ value: 'safety', label: 'Safety' },
		{ value: 'it', label: 'IT Security' },
		{ value: 'compliance', label: 'Compliance' },
		{ value: 'general', label: 'General' }
	];

	const statuses = [
		{ value: 'all', label: 'All Status' },
		{ value: 'pending', label: 'Pending' },
		{ value: 'acknowledged', label: 'Acknowledged' },
		{ value: 'overdue', label: 'Overdue' }
	];

	const getStatusColor = (status: Policy['acknowledgementStatus']) => {
		switch (status) {
			case 'acknowledged': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
			case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
			case 'overdue': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
		}
	};

	const getCategoryColor = (category: Policy['category']) => {
		switch (category) {
			case 'hr': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
			case 'safety': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
			case 'it': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
			case 'compliance': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
			case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
		}
	};

	const getStatusIcon = (status: Policy['acknowledgementStatus']) => {
		switch (status) {
			case 'acknowledged': return CheckCircle;
			case 'pending': return Clock;
			case 'overdue': return AlertTriangle;
		}
	};

	const handleViewPolicy = (policy: Policy) => {
		selectedPolicy = policy;
		acknowledgementChecked = false;
		isViewDialogOpen = true;
	};

	const handleAcknowledgePolicy = async () => {
		if (!selectedPolicy || !acknowledgementChecked) {
			notificationStore.warning('Acknowledgement Required', 'Please check the acknowledgement box to confirm you have read and understood the policy.');
			return;
		}

		try {
			// Update policy status
			const policyIndex = policies.findIndex(p => p.id === selectedPolicy.id);
			if (policyIndex !== -1) {
				policies[policyIndex] = {
					...policies[policyIndex],
					acknowledgementStatus: 'acknowledged',
					acknowledgedAt: new Date().toISOString().split('T')[0],
					acknowledgedBy: 'John Doe'
				};
				policies = [...policies]; // Trigger reactivity
			}

			notificationStore.success(
				'Policy Acknowledged', 
				`You have successfully acknowledged the "${selectedPolicy.title}" policy.`,
				{
					action: {
						label: 'View Certificate',
						onClick: () => notificationStore.info('Certificate', 'Acknowledgement certificate functionality will be available soon.')
					}
				}
			);

			isViewDialogOpen = false;
			selectedPolicy = null;
		} catch (error) {
			notificationStore.error('Acknowledgement Failed', 'Failed to acknowledge the policy. Please try again.');
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	const isOverdue = (policy: Policy) => {
		if (!policy.dueDate) return false;
		return new Date(policy.dueDate) < new Date() && policy.acknowledgementStatus !== 'acknowledged';
	};

	// Count policies by status
	const pendingCount = policies.filter(p => p.acknowledgementStatus === 'pending').length;
	const overdueCount = policies.filter(p => p.acknowledgementStatus === 'overdue').length;
	const acknowledgedCount = policies.filter(p => p.acknowledgementStatus === 'acknowledged').length;
</script>

<svelte:head>
	<title>Company Policies - HRIMS</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Company Policies</h1>
			<p class="text-muted-foreground">Review and acknowledge company policies and procedures</p>
		</div>
	</div>

	<!-- Status Summary -->
	<div class="grid gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Pending Acknowledgement</CardTitle>
				<Clock class="h-4 w-4 text-yellow-600" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-yellow-600">{pendingCount}</div>
				<p class="text-xs text-muted-foreground">
					Policies requiring your acknowledgement
				</p>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Overdue</CardTitle>
				<AlertTriangle class="h-4 w-4 text-red-600" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-red-600">{overdueCount}</div>
				<p class="text-xs text-muted-foreground">
					Policies past due date
				</p>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">Acknowledged</CardTitle>
				<CheckCircle class="h-4 w-4 text-green-600" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-green-600">{acknowledgedCount}</div>
				<p class="text-xs text-muted-foreground">
					Policies you've acknowledged
				</p>
			</CardContent>
		</Card>
	</div>

	<!-- Filters -->
	<div class="flex flex-col lg:flex-row gap-4">
		<div class="flex flex-wrap gap-2">
			{#each categories as category}
				<Button
					variant={selectedCategory === category.value ? 'default' : 'outline'}
					size="sm"
					onclick={() => selectedCategory = category.value}
				>
					{category.label}
				</Button>
			{/each}
		</div>

		<div class="flex gap-2">
			{#each statuses as status}
				<Button
					variant={selectedStatus === status.value ? 'default' : 'outline'}
					size="sm"
					onclick={() => selectedStatus = status.value}
				>
					{status.label}
				</Button>
			{/each}
		</div>

		<div class="flex-1 max-w-md">
			<div class="relative">
				<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
				<Input
					type="search"
					placeholder="Search policies..."
					bind:value={searchQuery}
					class="pl-10"
				/>
			</div>
		</div>
	</div>

	<!-- Policies List -->
	<div class="space-y-4">
		{#each filteredPolicies as policy (policy.id)}
			<Card class="hover:shadow-md transition-shadow">
				<CardContent class="p-6">
					<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
						<!-- Policy Info -->
						<div class="flex items-start gap-4">
							<div class="p-3 bg-primary/10 rounded-lg">
								<FileText class="w-6 h-6 text-primary" />
							</div>
							<div class="flex-1">
								<div class="flex items-start justify-between gap-2">
									<div>
										<h3 class="font-semibold text-lg flex items-center gap-2">
											{policy.title}
											{#if policy.mandatory}
												<Badge variant="destructive" class="text-xs">Required</Badge>
											{/if}
										</h3>
										<p class="text-sm text-muted-foreground mt-1">
											{policy.description}
										</p>
									</div>
								</div>
								
								<div class="flex flex-wrap items-center gap-2 mt-3">
									<Badge class={getCategoryColor(policy.category)} variant="secondary">
										{policy.category.toUpperCase()}
									</Badge>
									<Badge class={getStatusColor(policy.acknowledgementStatus)} variant="secondary">
										{@const StatusIcon = getStatusIcon(policy.acknowledgementStatus)}
										<StatusIcon class="w-3 h-3 mr-1" />
										{policy.acknowledgementStatus}
									</Badge>
									<span class="text-xs text-muted-foreground">
										v{policy.version}
									</span>
								</div>

								<div class="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
									<div class="flex items-center gap-1">
										<Calendar class="w-3 h-3" />
										<span>Effective: {formatDate(policy.effectiveDate)}</span>
									</div>
									{#if policy.dueDate && policy.acknowledgementStatus !== 'acknowledged'}
										<div class="flex items-center gap-1">
											<Clock class="w-3 h-3" />
											<span class={isOverdue(policy) ? 'text-red-600' : ''}>
												Due: {formatDate(policy.dueDate)}
											</span>
										</div>
									{/if}
									{#if policy.acknowledgedAt}
										<div class="flex items-center gap-1">
											<User class="w-3 h-3" />
											<span>Acknowledged: {formatDate(policy.acknowledgedAt)}</span>
										</div>
									{/if}
								</div>
							</div>
						</div>

						<!-- Actions -->
						<div class="flex gap-2">
							<Button
								size="sm"
								variant="outline"
								onclick={() => handleViewPolicy(policy)}
								class="flex items-center gap-2"
							>
								<Eye class="w-4 h-4" />
								{policy.acknowledgementStatus === 'acknowledged' ? 'Review' : 'Read & Acknowledge'}
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		{/each}
	</div>

	{#if filteredPolicies.length === 0}
		<div class="text-center py-12">
			<FileText class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-medium text-muted-foreground mb-2">No policies found</h3>
			<p class="text-sm text-muted-foreground">
				{searchQuery || selectedCategory !== 'all' || selectedStatus !== 'all'
					? 'Try adjusting your search or filter criteria.' 
					: 'No policies are currently available.'}
			</p>
		</div>
	{/if}
</div>

<!-- Policy View Dialog -->
<Dialog.Root bind:open={isViewDialogOpen}>
	<Dialog.Content class="max-w-4xl max-h-[80vh]">
		{#if selectedPolicy}
			<Dialog.Header>
				<Dialog.Title class="flex items-center gap-2">
					{selectedPolicy.title}
					{#if selectedPolicy.mandatory}
						<Badge variant="destructive" class="text-xs">Required</Badge>
					{/if}
				</Dialog.Title>
				<Dialog.Description>
					Version {selectedPolicy.version} • Effective {formatDate(selectedPolicy.effectiveDate)}
				</Dialog.Description>
			</Dialog.Header>
			
			<ScrollArea class="max-h-96 w-full rounded-md border p-4">
				<div class="prose prose-sm dark:prose-invert max-w-none">
					{@html selectedPolicy.content.replace(/\n/g, '<br>').replace(/# (.*)/g, '<h1>$1</h1>').replace(/## (.*)/g, '<h2>$1</h2>').replace(/- (.*)/g, '<li>$1</li>')}
				</div>
			</ScrollArea>

			{#if selectedPolicy.acknowledgementStatus !== 'acknowledged'}
				<div class="space-y-4">
					<div class="flex items-center space-x-2">
						<Checkbox 
							id="acknowledge" 
							bind:checked={acknowledgementChecked}
						/>
						<Label for="acknowledge" class="text-sm">
							I have read, understood, and agree to comply with this policy
						</Label>
					</div>
				</div>
			{/if}

			<Dialog.Footer>
				<Button variant="outline" onclick={() => isViewDialogOpen = false}>
					Close
				</Button>
				{#if selectedPolicy.acknowledgementStatus !== 'acknowledged'}
					<Button 
						onclick={handleAcknowledgePolicy}
						disabled={!acknowledgementChecked}
					>
						Acknowledge Policy
					</Button>
				{/if}
			</Dialog.Footer>
		{/if}
	</Dialog.Content>
</Dialog.Root>
