# Rainbow Tourism Group — HRIMS (Frontend-first) — Implementation Pack

This repo contains the frontend-first implementation specification and module workflows for the Rainbow Tourism Group HRIMS.
Start here: implement the UI and client-side flows only (no backend). Use mocks / MSW / contract-first JSON to simulate the backend.

Key tech: SvelteKit, TailwindCSS, TypeScript, Zod, Drizzle ORM, sveltekit-superforms (or equivalent), Vitest + Playwright for tests. Use the Supabase JS client only as a **mock** during frontend phase.

Docs & primary references:
- SvelteKit structure. :contentReference[oaicite:5]{index=5}
- Supabase RLS & storage guidance. :contentReference[oaicite:6]{index=6}
- Supabase Edge Functions examples. :contentReference[oaicite:7]{index=7}
- Tailwind + SvelteKit setup. :contentReference[oaicite:8]{index=8}

Read the module files in numeric order:
1. `01_foundation.md`
2. `02_employee_lifecycle.md`
3. `03_leave_management.md`
4. `04_contracts.md`
5. `05_ess.md`
6. `06_performance.md`
7. `07_training.md`
8. `08_training_evaluation.md`
9. `09_talent_matrix.md`
10. `10_wellness.md`
11. `11_industrial_relations.md`
12. `12_business_resourcing.md`
13. `13_analytics_reporting.md`
14. `14_exit_offboarding.md`

Follow the "Frontend Implementation Plan" in `99_frontend_plan.md`.
