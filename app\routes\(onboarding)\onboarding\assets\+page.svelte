<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Laptop, Monitor, Phone, Car, Key, Package, CheckCircle, Clock } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	// Asset categories
	const assetCategories = [
		{
			id: 'it-equipment',
			title: 'IT Equipment',
			icon: Laptop,
			items: [
				{ id: 'laptop', name: 'Laptop Computer', description: 'Business laptop with required software', required: true, requested: false, approved: false },
				{ id: 'monitor', name: 'External Monitor', description: '24" external display', required: false, requested: false, approved: false },
				{ id: 'phone', name: 'Mobile Phone', description: 'Company mobile phone', required: false, requested: false, approved: false },
				{ id: 'headset', name: 'Headset', description: 'Noise-cancelling headset for calls', required: false, requested: false, approved: false }
			]
		},
		{
			id: 'office-supplies',
			title: 'Office Supplies',
			icon: Package,
			items: [
				{ id: 'stationery', name: 'Stationery Kit', description: 'Basic office supplies and stationery', required: true, requested: false, approved: false },
				{ id: 'business-cards', name: 'Business Cards', description: 'Personalized business cards', required: false, requested: false, approved: false }
			]
		},
		{
			id: 'access-security',
			title: 'Access & Security',
			icon: Key,
			items: [
				{ id: 'id-card', name: 'Employee ID Card', description: 'Photo ID with building access', required: true, requested: false, approved: false },
				{ id: 'parking-pass', name: 'Parking Pass', description: 'Employee parking permit', required: false, requested: false, approved: false }
			]
		},
		{
			id: 'transport',
			title: 'Transportation',
			icon: Car,
			items: [
				{ id: 'company-car', name: 'Company Vehicle', description: 'Company car for business use', required: false, requested: false, approved: false },
				{ id: 'fuel-card', name: 'Fuel Card', description: 'Corporate fuel card', required: false, requested: false, approved: false }
			]
		}
	];

	let specialRequests = '';
	let isSubmitting = false;

	const toggleAssetRequest = (categoryId: string, itemId: string) => {
		const category = assetCategories.find(c => c.id === categoryId);
		const item = category?.items.find(i => i.id === itemId);
		if (item) {
			item.requested = !item.requested;
			if (item.requested) {
				// Simulate approval for required items
				if (item.required) {
					setTimeout(() => {
						item.approved = true;
						uiStore.showSuccess('Request Approved', `${item.name} has been approved automatically.`);
					}, 1000);
				}
			} else {
				item.approved = false;
			}
		}
	};

	const submitRequests = async () => {
		const requestedItems = assetCategories.flatMap(category => 
			category.items.filter(item => item.requested)
		);

		if (requestedItems.length === 0) {
			uiStore.showError('No Requests', 'Please select at least one asset to request.');
			return;
		}

		isSubmitting = true;
		try {
			// Simulate submission
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Auto-approve some items for demo
			requestedItems.forEach(item => {
				if (!item.approved && Math.random() > 0.3) {
					item.approved = true;
				}
			});

			// Save asset requests
			onboardingStore.updateData('assets', {
				requests: requestedItems.map(item => ({
					id: item.id,
					name: item.name,
					requested: true,
					approved: item.approved,
					requestedAt: new Date().toISOString(),
					approvedAt: item.approved ? new Date().toISOString() : null
				})),
				specialRequests,
				submittedAt: new Date().toISOString()
			});

			uiStore.showSuccess('Requests Submitted', 'Your asset requests have been submitted for approval.');
		} catch (error) {
			uiStore.showError('Submission Failed', 'Failed to submit asset requests. Please try again.');
		} finally {
			isSubmitting = false;
		}
	};

	const allRequiredRequested = $derived(
		assetCategories.every(category => 
			category.items.filter(item => item.required).every(item => item.requested)
		)
	);

	const hasRequests = $derived(
		assetCategories.some(category => 
			category.items.some(item => item.requested)
		)
	);

	const handleNext = () => {
		if (!allRequiredRequested) {
			uiStore.showError('Required Assets', 'Please request all required assets before continuing.');
			return;
		}

		if (!hasRequests) {
			uiStore.showError('No Requests', 'Please request at least one asset before continuing.');
			return;
		}

		// Mark step as completed and move to next
		onboardingStore.completeStep('assets');
		onboardingStore.setCurrentStep('approvals');
		goto('/onboarding/approvals');
	};

	const handleBack = () => {
		onboardingStore.setCurrentStep('training');
		goto('/onboarding/training');
	};
</script>

<svelte:head>
	<title>Equipment & Assets - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Equipment & Asset Requests"
		subtitle="Request the equipment and assets you'll need to perform your role effectively. Required items will be approved automatically."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		nextButtonDisabled={!allRequiredRequested || !hasRequests}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-6">
			<!-- Asset Categories -->
			{#each assetCategories as category}
				<div class="space-y-3">
					<div class="flex items-center gap-2">
						<svelte:component this={category.icon} class="w-5 h-5 text-[#C49A6C]" />
						<h3 class="text-lg font-semibold text-[#1C1C1C]">{category.title}</h3>
					</div>

					<div class="grid gap-3">
						{#each category.items as item}
							<div class="border border-[#EDE0CF] rounded-lg p-4 {item.requested ? 'bg-blue-50 border-blue-200' : ''}">
								<div class="flex items-start justify-between">
									<div class="flex items-start gap-3">
										<input
											type="checkbox"
											checked={item.requested}
											onchange={() => toggleAssetRequest(category.id, item.id)}
											class="mt-1 text-[#C49A6C] rounded"
										/>
										<div class="flex-1">
											<div class="flex items-center gap-2 mb-1">
												<h4 class="font-medium text-[#1C1C1C]">{item.name}</h4>
												{#if item.required}
													<span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">Required</span>
												{/if}
												{#if item.approved}
													<CheckCircle class="w-4 h-4 text-green-500" />
												{:else if item.requested}
													<Clock class="w-4 h-4 text-blue-500" />
												{/if}
											</div>
											<p class="text-sm text-[#8A6A52]">{item.description}</p>
											{#if item.requested}
												<div class="mt-2 text-xs">
													{#if item.approved}
														<span class="text-green-600 font-medium">✓ Approved</span>
													{:else}
														<span class="text-blue-600 font-medium">⏳ Pending Approval</span>
													{/if}
												</div>
											{/if}
										</div>
									</div>
								</div>
							</div>
						{/each}
					</div>
				</div>
			{/each}

			<!-- Special Requests -->
			<div class="space-y-3">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">Special Requests</h3>
				<div class="space-y-2">
					<Label for="specialRequests">
						Any additional equipment or accommodations needed? (Optional)
					</Label>
					<Textarea
						id="specialRequests"
						placeholder="Describe any special equipment needs, accessibility requirements, or other requests..."
						bind:value={specialRequests}
						class="onboarding-input min-h-[100px]"
					/>
				</div>
			</div>

			<!-- Submit Button -->
			{#if hasRequests}
				<div class="pt-4 border-t border-[#EDE0CF]">
					<Button
						onclick={submitRequests}
						disabled={isSubmitting}
						class="onboarding-button-primary w-full"
					>
						{#if isSubmitting}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							Submitting Requests...
						{:else}
							<Package class="w-4 h-4 mr-2" />
							Submit Asset Requests
						{/if}
					</Button>
				</div>
			{/if}

			<!-- Summary -->
			<div class="bg-[#FCF8F2] rounded-xl p-4 border border-[#EDE0CF]">
				<h4 class="font-medium text-[#1C1C1C] mb-2">Request Summary</h4>
				<div class="text-sm text-[#8A6A52] space-y-1">
					<p>Total items requested: {assetCategories.flatMap(c => c.items).filter(i => i.requested).length}</p>
					<p>Required items requested: {assetCategories.flatMap(c => c.items).filter(i => i.required && i.requested).length} / {assetCategories.flatMap(c => c.items).filter(i => i.required).length}</p>
					<p>Items approved: {assetCategories.flatMap(c => c.items).filter(i => i.approved).length}</p>
				</div>
			</div>
		</div>
	</OnboardingCard>
</div>
