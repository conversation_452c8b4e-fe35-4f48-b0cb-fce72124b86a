# 14 — Exit & Offboarding (Frontend-only)

Pages:
- `/offboard` — start offboarding, checklist
- `/offboard/[employeeId]/handover` — asset checklist & account revocation simulation

Components:
- `OffboardChecklist.svelte` — tasks for IT, payroll, manager.

Workflow:
1. Resignation flow: employee submits resignation via front-end -> offboard checklist created -> tasks completed by simulated actors -> final settlement mocked.

Acceptance:
- Offboarding tasks mark employees as `terminated/resigned` in mock store.
