import { z } from 'zod';

// Personal Details Schema
export const personalDetailsSchema = z.object({
	// Basic Information
	firstName: z
		.string()
		.min(2, 'First name must be at least 2 characters')
		.max(50, 'First name must be less than 50 characters')
		.regex(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes'),
	
	lastName: z
		.string()
		.min(2, 'Last name must be at least 2 characters')
		.max(50, 'Last name must be less than 50 characters')
		.regex(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes'),
	
	preferredName: z
		.string()
		.max(50, 'Preferred name must be less than 50 characters')
		.regex(/^[a-zA-Z\s'-]*$/, 'Preferred name can only contain letters, spaces, hyphens, and apostrophes')
		.optional(),
	
	dateOfBirth: z
		.string()
		.refine((date) => {
			const birthDate = new Date(date);
			const today = new Date();
			const age = today.getFullYear() - birthDate.getFullYear();
			const monthDiff = today.getMonth() - birthDate.getMonth();
			
			if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
				return age - 1 >= 18;
			}
			return age >= 18;
		}, 'You must be at least 18 years old'),
	
	nationalId: z
		.string()
		.optional()
		.refine((id) => {
			if (!id) return true; // Optional field
			// Zimbabwe National ID format: XX-XXXXXXX-X-XX
			return /^\d{2}-\d{6,7}-[A-Z]-\d{2}$/.test(id);
		}, 'Please enter a valid Zimbabwe National ID (format: XX-XXXXXXX-X-XX)'),
	
	email: z
		.string()
		.email('Please enter a valid email address')
		.max(100, 'Email must be less than 100 characters'),
	
	phone: z
		.string()
		.min(10, 'Phone number must be at least 10 digits')
		.max(15, 'Phone number must be less than 15 digits')
		.regex(/^[\+]?[0-9\s\-\(\)]+$/, 'Please enter a valid phone number'),
	
	// Address Information
	address: z.object({
		street: z
			.string()
			.min(5, 'Street address must be at least 5 characters')
			.max(100, 'Street address must be less than 100 characters'),
		
		street2: z
			.string()
			.max(100, 'Street address 2 must be less than 100 characters')
			.optional(),
		
		city: z
			.string()
			.min(2, 'City must be at least 2 characters')
			.max(50, 'City must be less than 50 characters')
			.regex(/^[a-zA-Z\s'-]+$/, 'City can only contain letters, spaces, hyphens, and apostrophes'),
		
		postalCode: z
			.string()
			.min(3, 'Postal code must be at least 3 characters')
			.max(10, 'Postal code must be less than 10 characters')
			.regex(/^[A-Z0-9\s-]+$/i, 'Please enter a valid postal code'),
		
		country: z
			.string()
			.min(2, 'Please select a country')
			.max(2, 'Invalid country code')
	}),
	
	// Emergency Contact
	emergencyContact: z.object({
		name: z
			.string()
			.min(2, 'Emergency contact name must be at least 2 characters')
			.max(100, 'Emergency contact name must be less than 100 characters')
			.regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes'),
		
		relationship: z
			.string()
			.min(1, 'Please select a relationship')
			.max(50, 'Relationship must be less than 50 characters'),
		
		phone: z
			.string()
			.min(10, 'Emergency contact phone must be at least 10 digits')
			.max(15, 'Emergency contact phone must be less than 15 digits')
			.regex(/^[\+]?[0-9\s\-\(\)]+$/, 'Please enter a valid phone number')
	})
});

// Type inference
export type PersonalDetailsFormData = z.infer<typeof personalDetailsSchema>;

// Validation helper function
export const validatePersonalDetails = (data: unknown) => {
	return personalDetailsSchema.safeParse(data);
};

// Field-specific validation helpers
export const validateEmail = (email: string) => {
	return z.string().email().safeParse(email);
};

export const validatePhone = (phone: string) => {
	return z.string().regex(/^[\+]?[0-9\s\-\(\)]+$/).safeParse(phone);
};

export const validateAge = (dateOfBirth: string) => {
	const birthDate = new Date(dateOfBirth);
	const today = new Date();
	const age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();
	
	if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
		return age - 1 >= 18;
	}
	return age >= 18;
};

// Default values for form initialization
export const personalDetailsDefaults: Partial<PersonalDetailsFormData> = {
	firstName: '',
	lastName: '',
	preferredName: '',
	dateOfBirth: '',
	nationalId: '',
	email: '',
	phone: '',
	address: {
		street: '',
		street2: '',
		city: '',
		postalCode: '',
		country: 'ZW'
	},
	emergencyContact: {
		name: '',
		relationship: '',
		phone: ''
	}
};
