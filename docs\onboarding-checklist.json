{"meta": {"name": "Onboarding - Single File Checklist", "version": "1.0.0", "description": "Machine-friendly checklist for implementing the frontend-first onboarding flow for RTG HRIMS. Contains routes, components, validation schemas, MSW mocks and acceptance tests.", "notes": "Paths use `app/` as the project root (no `src/`). If you keep default SvelteKit layout, set files paths accordingly in svelte.config.ts."}, "global": {"styleGuide": "app/lib/styles/style-guide.onboarding.json", "chartLibrary": "Apache ECharts (only used on analytics pages, not onboarding)", "animationLibrary": "motion-one (Motion One) recommended", "preferredFormLib": "zod + react-like small form helper or use svelte-forms-lib; schemas in TypeScript", "storage": "Supabase storage (signed-URL flow) — mocked in MSW", "edgeFunctions": "Edge functions orchestration (mocked) for file-scan trigger", "packageManager": "pnpm (recommended for monorepos and fast installs) - use your preferred manager"}, "routesAndFiles": [{"stepOrder": 1, "id": "invite-and-account-creation", "displayName": "Invite & Account Creation", "description": "Accept invite token, create password or SSO with Microsoft/Azure.", "routes": ["app/routes/onboarding/invite/+page.svelte", "app/routes/onboarding/invite/+page.ts (load actions for invite token)", "app/routes/onboarding/set-password/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/InviteLanding.svelte", "props": ["inviteToken", "companyName"], "notes": "Shows invite CT<PERSON>, SSO button, accepts token from query string."}, {"file": "app/lib/components/onboarding/PasswordForm.svelte", "props": ["onSubmit"], "notes": "Shows password strength meter, shows rules. Use zod schema at lib/forms/onboarding/password.schema.ts"}], "zodSchemas": ["app/lib/forms/onboarding/password.schema.ts"], "stores": ["app/lib/stores/onboardingStore.ts"], "mswMocks": [{"handler": "validateInviteHandler", "file": "mocks/msw/onboardingHandlers.ts", "endpoint": "/api/onboarding/invite/validate", "method": "GET", "behavior": "return { valid:true, email:'<EMAIL>' } (delay 200-500ms)"}, {"handler": "acceptInviteHandler", "endpoint": "/api/onboarding/invite/accept", "method": "POST", "behavior": "return { success:true, onboardingId:'onb_xxx' } (delay 300ms)"}], "acceptance": ["Invite token loaded via page load", "SSO button present", "Password form validates client-side with zod"]}, {"stepOrder": 2, "id": "company-intro-tour", "displayName": "Company Intro + Tour", "routes": ["app/routes/onboarding/welcome/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/WelcomeHero.svelte", "props": ["companyLogo", "videoThumbUrl", "startCallback"]}, {"file": "app/lib/components/onboarding/TourOverlay.svelte", "props": ["steps", "onSkip"]}], "mswMocks": [], "acceptance": ["Hero gradient displayed", "Video thumbnail overlay present", "Start onboarding CTA advances to next step"]}, {"stepOrder": 3, "id": "personal-details", "displayName": "Personal Details", "routes": ["app/routes/onboarding/personal/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/PersonalForm.svelte", "props": ["initialValues", "onSave"], "notes": "2-column form desktop-first, autosaves to onboardingStore and localStorage"}, {"file": "app/lib/components/onboarding/AvatarUploader.svelte", "props": ["onUpload"]}], "zodSchemas": ["app/lib/forms/onboarding/personal.schema.ts"], "stores": ["app/lib/stores/onboardingStore.ts"], "mswMocks": [{"handler": "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "endpoint": "/api/onboarding/personal/save", "method": "POST", "behavior": "echo back { success: true }, 300ms delay"}], "acceptance": ["Form validates (zod) for DOB >= 18", "Autosave triggers toast (aria-live) and persists draft"]}, {"stepOrder": 4, "id": "employment-details", "displayName": "Employment Details", "routes": ["app/routes/onboarding/employment/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/EmploymentForm.svelte", "props": ["departments", "managers", "onSave"], "notes": "Manager autocomplete calls mock managers API"}, {"file": "app/lib/components/onboarding/ManagerLookup.svelte", "props": ["query", "onSelect"]}], "zodSchemas": ["app/lib/forms/onboarding/employment.schema.ts"], "mswMocks": [{"handler": "saveEmploymentHandler", "endpoint": "/api/onboarding/employment/save", "method": "POST"}, {"handler": "listManagersHandler", "endpoint": "/api/staff/managers", "method": "GET", "behavior": "return 8 sample managers with avatars and emails"}], "acceptance": ["Manager autocomplete works and populates manager avatar", "Start date validation enforced"]}, {"stepOrder": 5, "id": "tax-and-payroll", "displayName": "Tax & Payroll", "routes": ["app/routes/onboarding/tax/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/TaxForm.svelte", "props": ["onSave"]}, {"file": "app/lib/components/onboarding/PayslipPreview.svelte", "props": ["payslipSampleUrl"]}], "zodSchemas": ["app/lib/forms/onboarding/tax.schema.ts"], "mswMocks": [{"handler": "saveTaxHandler", "endpoint": "/api/onboarding/tax/save", "method": "POST"}, {"handler": "payslipPreviewHandler", "endpoint": "/api/onboarding/payslip/preview", "method": "GET", "behavior": "return sample PDF url"}], "acceptance": ["Payslip preview loads lazily", "Tax id basic format checking enforced"]}, {"stepOrder": 6, "id": "bank-details", "displayName": "Bank Details", "routes": ["app/routes/onboarding/bank/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/BankForm.svelte", "props": ["onSave"]}, {"file": "app/lib/components/onboarding/BankVerifyMock.svelte", "props": ["accountId"]}], "zodSchemas": ["app/lib/forms/onboarding/bank.schema.ts"], "mswMocks": [{"handler": "saveBankHandler", "endpoint": "/api/onboarding/bank/save", "method": "POST"}, {"handler": "verifyBankHandler", "endpoint": "/api/onboarding/bank/verify", "method": "POST", "behavior": "return pending -> verified (simulate delay)"}], "acceptance": ["Bank save returns pending verification state", "UI shows pending animated indicator"]}, {"stepOrder": 7, "id": "documents-upload-and-scan", "displayName": "Documents Upload & Scan", "routes": ["app/routes/onboarding/documents/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/DocumentDropzone.svelte", "props": ["acceptedTypes", "maxSizeMB", "onUploadStart"], "notes": "Requests signed URL from backend mock and uploads directly to it"}, {"file": "app/lib/components/onboarding/DocumentThumb.svelte", "props": ["fileMeta", "scanStatus"]}], "zodSchemas": [], "mswMocks": [{"handler": "getUploadUrlHandler", "endpoint": "/api/onboarding/documents/upload-url", "method": "POST", "behavior": "return { uploadUrl: '<mockUploadUrl>', fileId: 'file_x' } (delay 150-300ms)"}, {"handler": "triggerScanHandler", "endpoint": "/api/onboarding/documents/scan-trigger", "method": "POST", "behavior": "return { scanId: 'scan_x', status: 'queued' }"}, {"handler": "scanStatusHandler", "endpoint": "/api/onboarding/documents/scan-status", "method": "GET", "behavior": "simulate transition: pending (2s) -> in_progress (2s) -> clean OR quarantine (random) (2-4s)."}], "acceptance": ["Signed URL flow simulated (getUploadUrl -> upload to URL)", "Polling scan status updates UI badges (pending_scan / clean / quarantine)", "Uploads show thumbnail pop animation and progress bar"]}, {"stepOrder": 8, "id": "contracts-and-e-sign", "displayName": "Contracts & E-Sign", "routes": ["app/routes/onboarding/contract/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/ContractViewer.svelte", "props": ["pdfUrl"]}, {"file": "app/lib/components/onboarding/SignaturePad.svelte", "props": ["onSaveSignature"], "notes": "Provide typed signature fallback and capture dataUri"}], "zodSchemas": ["app/lib/forms/onboarding/contract.schema.ts"], "mswMocks": [{"handler": "generateContractHandler", "endpoint": "/api/onboarding/contracts/generate", "method": "POST", "behavior": "return { pdfUrl: 'https://mock.pdf/contract.pdf' } (delay 800-1200ms)"}, {"handler": "signContractHandler", "endpoint": "/api/onboarding/contracts/sign", "method": "POST", "behavior": "return { signed:true, signedAt: '<iso>' }"}], "acceptance": ["PDF preview loads lazily", "Signature canvas works; typed fallback accepted", "Signed metadata stored in mock response and shown"]}, {"stepOrder": 9, "id": "policy-acknowledgement", "displayName": "Policy Acknowledgement", "routes": ["app/routes/onboarding/policies/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/PolicyList.svelte", "props": ["policies"]}, {"file": "app/lib/components/onboarding/PolicyReader.svelte", "props": ["policyId", "onAcknowledge"], "notes": "Track scroll % and time-on-doc before enabling acknowledge button"}], "mswMocks": [{"handler": "listPoliciesHandler", "endpoint": "/api/onboarding/policies/list", "method": "GET"}, {"handler": "<PERSON>ck<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "endpoint": "/api/onboarding/policies/ack", "method": "POST"}], "acceptance": ["Policies load and acknowledge when read/scroll threshold met", "Acknowledge action sends ack mock"]}, {"stepOrder": 10, "id": "training-and-first-week-plan", "displayName": "Training Enrollment & Week Plan", "routes": ["app/routes/onboarding/training/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/TrainingCard.svelte", "props": ["training"]}, {"file": "app/lib/components/onboarding/FirstWeekPlan.svelte", "props": ["agenda"]}], "mswMocks": [{"handler": "trainingCatalogHandler", "endpoint": "/api/training/catalog", "method": "GET"}, {"handler": "enroll<PERSON><PERSON><PERSON>", "endpoint": "/api/enrollments", "method": "POST"}], "acceptance": ["Auto-enroll mandatory trainings", "Calendar add modal present (mock)"]}, {"stepOrder": 11, "id": "equipment-and-assets", "displayName": "Equipment & Asset Requests", "routes": ["app/routes/onboarding/assets/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/AssetRequestForm.svelte", "props": ["options", "onRequest"]}], "mswMocks": [{"handler": "assetRequestHandler", "endpoint": "/api/assets/request", "method": "POST", "behavior": "return requested status and requestId"}], "acceptance": ["Asset requests saved to mock and show status", "Manager approval flag visible"]}, {"stepOrder": 12, "id": "manager-approval-and-checklist", "displayName": "Manager Approval & Checklist", "routes": ["app/routes/onboarding/approvals/+page.svelte", "app/routes/manager/onboarding/approvals/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/ManagerApprovalView.svelte", "props": ["pendingApprovals"]}, {"file": "app/lib/components/onboarding/OnboardingChecklist.svelte", "props": ["tasks"]}], "mswMocks": [{"handler": "approvalsHandler", "endpoint": "/api/onboarding/approvals", "method": "GET"}, {"handler": "tasksHandler", "endpoint": "/api/onboarding/tasks", "method": "GET"}], "acceptance": ["Manager can approve and onboarding state updates", "Checklist auto-generated tasks present"]}, {"stepOrder": 13, "id": "final-confirmation-and-welcome-pack", "displayName": "Final Confirmation & Welcome Pack", "routes": ["app/routes/onboarding/complete/+page.svelte"], "components": [{"file": "app/lib/components/onboarding/FinalSummary.svelte", "props": ["onboardingSummary", "welcomePackUrl"]}, {"file": "app/lib/components/onboarding/Confetti.svelte", "props": ["enabled"], "notes": "Do not animate if prefers-reduced-motion"}], "mswMocks": [{"handler": "completeOnboardingHandler", "endpoint": "/api/onboarding/complete", "method": "POST", "behavior": "return { status:'complete', welcomePackUrl:'https://mock.welcome/pack.pdf' }"}], "acceptance": ["Signed docs list displayed", "Welcome pack available for download", "Confetti respects reduced motion"]}], "commonComponents": [{"file": "app/lib/components/onboarding/OnboardStepper.svelte", "props": ["steps", "currentStep", "onNavigate"], "notes": "Shows progress; accessible focus; role=progressbar and keyboard nav; animations: stepper-step-entrance"}, {"file": "app/lib/components/ui/Toast.svelte", "props": ["type", "message", "duration"], "notes": "ARIA live region for autosave & scan updates; mini-toast animation"}, {"file": "app/lib/components/ui/Modal.svelte", "props": ["title", "open", "onClose"], "notes": "Use portal; focus trap; animation: slide-up-fade"}, {"file": "app/lib/components/ui/ProgressBar.svelte", "props": ["value", "height", "fillGradient"], "notes": "Used for document scan and payslip preview"}], "storesAndTypes": {"stores": ["app/lib/stores/onboardingStore.ts (primary draft store, persists to localStorage + MSW mock endpoint)", "app/lib/stores/uiStore.ts (toasts, modals, global UI state)"], "types": ["app/lib/types/onboarding.ts (central TS types for onboarding payloads)", "app/lib/types/document.ts (DocumentMeta, scan status enum)"]}, "formsAndSchemas": {"files": ["app/lib/forms/onboarding/personal.schema.ts", "app/lib/forms/onboarding/employment.schema.ts", "app/lib/forms/onboarding/tax.schema.ts", "app/lib/forms/onboarding/bank.schema.ts", "app/lib/forms/onboarding/password.schema.ts", "app/lib/forms/onboarding/contract.schema.ts"], "schemaNotes": "Use zod for validations, export TS types using zod.infer. Keep validation rules consistent with style-guide.onboarding.json"}, "mocksAndMSW": {"file": "mocks/msw/onboardingHandlers.ts", "handlersToInclude": ["validateInviteHandler", "acceptInviteHandler", "save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saveEmploymentHandler", "saveTaxHandler", "saveBankHandler", "getUploadUrlHandler", "triggerScanHandler", "scanStatusHandler", "generateContractHandler", "signContractHandler", "listPoliciesHandler", "<PERSON>ck<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trainingCatalogHandler", "enroll<PERSON><PERSON><PERSON>", "assetRequestHandler", "approvalsHandler", "tasksHandler", "completeOnboardingHandler"], "handlerPatterns": "Use delayed responses (ms), simulate state transitions for scanning and bank verification. Expose helper `advanceMockTime` for tests to speed time."}, "tests": {"unit": ["tests/unit/forms/personal.spec.ts (zod schema tests)", "tests/unit/components/DocumentDropzone.spec.ts (upload behavior & progress)", "tests/unit/components/SignaturePad.spec.ts"], "e2e": ["tests/e2e/onboarding-happy.spec.ts (full onboarding happy path using MSW or Playwright with network interception)", "tests/e2e/onboarding-doc-quarantine.spec.ts (document quarantined path)"], "notes": "Use Vitest for unit and Playwright for e2e. MSW integrates with both."}, "animationsAndMotion": {"library": "motion-one", "tokenFile": "app/lib/styles/style-guide.onboarding.json (motion tokens included)", "componentTokens": {"stepperStepEntrance": "onboard-pop", "cardEntrance": "slide-up-fade", "thumbnailPop": "avatar-pop", "progressFillTransition": "progress-fill"}, "accessibility": "All animations must check `prefers-reduced-motion` and degrade to instant transitions"}, "acceptanceCriteria": {"frontend": ["All routes and components created and wired to onboardingStore", "Autosave works (debounced) and shows ARIA toast", "Document upload uses signed-URL mock and polling mock scan-status; UI badges update", "Contract e-sign works (typed and drawn) and shows signed metadata", "Policy acknowledgement enforces minimal read time/scroll before enabling acknowledge", "Manager approval flow simulated and final summary reflects approvals"], "visual": ["Colors, gradients, spacing derived from style-guide.onboarding.json", "Animations present and subtle; disabled under reduced-motion"], "testing": ["Unit tests for all zod schemas", "E2E for happy and quarantine/unhappy paths"]}, "devNotes": {"1": "Store canonical tokens in app/lib/styles/style-guide.dashboard.json; onboarding style-guide references these tokens.", "2": "If you prefer no `src/`, ensure svelte.config.ts points to `files.routes` and `files.assets` to match `app/` layout.", "3": "When backend is ready, replace MSW handlers with real endpoints and keep UI-level logic unchanged.", "4": "Generate `_variables.css` from style-guide tokens and import into `app/app.html` or top-level layout."}}