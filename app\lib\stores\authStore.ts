import { writable } from 'svelte/store';
import type { User, Employee, AuthState } from '$lib/types/hr';

// Initial state
const initialState: AuthState = {
	user: null,
	employee: null,
	isAuthenticated: false,
	isLoading: true // Start with loading true to check localStorage
};

// Create the store
function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>(initialState);

	return {
		subscribe,
		
		// Actions
		login: async (email: string, password: string) => {
			update(state => ({ ...state, isLoading: true }));
			
			try {
				// Mock authentication - in real app, this would call API
				await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
				
				// Mock user data based on email
				const mockUser: User = {
					id: '1',
					email,
					user_role: email.includes('admin') ? 'hr_admin' : 
							  email.includes('manager') ? 'manager' : 'employee',
					org_id: 'org-1',
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString()
				};

				const mockEmployee: Employee = {
					id: 'emp-1',
					employee_number: email.includes('admin') ? 'RTG001' :
									email.includes('manager') ? 'RTG002' : 'RTG003',
					first_name: email.includes('admin') ? 'HR' :
								email.includes('manager') ? 'Manager' : 'John',
					last_name: email.includes('admin') ? 'Admin' :
							   email.includes('manager') ? 'Smith' : 'Doe',
					email,
					phone: email.includes('admin') ? '+263 77 123 4567' :
						   email.includes('manager') ? '+263 77 234 5678' : '+263 77 345 6789',
					department_id: email.includes('admin') ? 'hr' :
								  email.includes('manager') ? 'management' : 'it',
					position: email.includes('admin') ? 'HR Administrator' :
							 email.includes('manager') ? 'Department Manager' : 'Software Developer',
					hire_date: '2023-01-01',
					status: 'active',
					auth_user_id: mockUser.id,
					org_id: 'org-1',
					created_at: new Date().toISOString(),
					updated_at: new Date().toISOString()
				};

				// Store in localStorage for persistence
				localStorage.setItem('hrims_user', JSON.stringify(mockUser));
				localStorage.setItem('hrims_employee', JSON.stringify(mockEmployee));

				update(state => ({
					...state,
					user: mockUser,
					employee: mockEmployee,
					isAuthenticated: true,
					isLoading: false
				}));

				return { success: true };
			} catch (error) {
				update(state => ({ ...state, isLoading: false }));
				return { success: false, error: 'Login failed' };
			}
		},

		logout: () => {
			localStorage.removeItem('hrims_user');
			localStorage.removeItem('hrims_employee');
			set(initialState);
		},

		// Initialize from localStorage
		initialize: () => {
			if (typeof window === 'undefined') {
				// On server, just set loading to false
				update(state => ({ ...state, isLoading: false }));
				return;
			}

			try {
				const storedUser = localStorage.getItem('hrims_user');
				const storedEmployee = localStorage.getItem('hrims_employee');

				if (storedUser && storedEmployee) {
					const user = JSON.parse(storedUser) as User;
					const employee = JSON.parse(storedEmployee) as Employee;

					update(state => ({
						...state,
						user,
						employee,
						isAuthenticated: true,
						isLoading: false
					}));
				} else {
					// No stored auth data, set loading to false
					update(state => ({ ...state, isLoading: false }));
				}
			} catch (error) {
				console.error('Failed to initialize auth from localStorage:', error);
				// Clear corrupted data
				localStorage.removeItem('hrims_user');
				localStorage.removeItem('hrims_employee');
				update(state => ({ ...state, isLoading: false }));
			}
		},

		// Update user profile
		updateProfile: (updates: Partial<Employee>) => {
			update(state => {
				if (!state.employee) return state;

				const updatedEmployee = { ...state.employee, ...updates };
				localStorage.setItem('hrims_employee', JSON.stringify(updatedEmployee));

				return {
					...state,
					employee: updatedEmployee
				};
			});
		},

		// Switch role (for development/testing)
		switchRole: (role: User['user_role']) => {
			update(state => {
				if (!state.user) return state;

				const updatedUser = { ...state.user, user_role: role };
				localStorage.setItem('hrims_user', JSON.stringify(updatedUser));

				return {
					...state,
					user: updatedUser
				};
			});
		}
	};
}

export const authStore = createAuthStore();
