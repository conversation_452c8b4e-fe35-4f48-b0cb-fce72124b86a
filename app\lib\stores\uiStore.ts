import { writable } from 'svelte/store';
import type { UIState, Notification } from '$lib/types/hr';

// Initial state
const initialState: UIState = {
	sidebarOpen: false,
	theme: 'light',
	notifications: [],
	isLoading: false
};

// Create the store
function createUIStore() {
	const { subscribe, set, update } = writable<UIState>(initialState);

	return {
		subscribe,
		
		// Sidebar actions
		toggleSidebar: () => {
			update(state => ({ ...state, sidebarOpen: !state.sidebarOpen }));
		},

		openSidebar: () => {
			update(state => ({ ...state, sidebarOpen: true }));
		},

		closeSidebar: () => {
			update(state => ({ ...state, sidebarOpen: false }));
		},

		// Theme actions
		setTheme: (theme: 'light' | 'dark') => {
			update(state => ({ ...state, theme }));
			if (typeof window !== 'undefined') {
				localStorage.setItem('hrims_theme', theme);
			}
		},

		toggleTheme: () => {
			update(state => {
				const newTheme = state.theme === 'light' ? 'dark' : 'light';
				if (typeof window !== 'undefined') {
					localStorage.setItem('hrims_theme', newTheme);
				}
				return { ...state, theme: newTheme };
			});
		},

		// Loading state
		setLoading: (isLoading: boolean) => {
			update(state => ({ ...state, isLoading }));
		},

		// Notification actions
		addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
			const newNotification: Notification = {
				...notification,
				id: crypto.randomUUID(),
				timestamp: new Date().toISOString(),
				read: false
			};

			update(state => ({
				...state,
				notifications: [newNotification, ...state.notifications]
			}));

			// Auto-remove success notifications after 5 seconds
			if (notification.type === 'success') {
				setTimeout(() => {
					update(state => ({
						...state,
						notifications: state.notifications.filter(n => n.id !== newNotification.id)
					}));
				}, 5000);
			}

			return newNotification.id;
		},

		removeNotification: (id: string) => {
			update(state => ({
				...state,
				notifications: state.notifications.filter(n => n.id !== id)
			}));
		},

		markNotificationAsRead: (id: string) => {
			update(state => ({
				...state,
				notifications: state.notifications.map(n => 
					n.id === id ? { ...n, read: true } : n
				)
			}));
		},

		clearAllNotifications: () => {
			update(state => ({ ...state, notifications: [] }));
		},

		// Initialize from localStorage
		initialize: () => {
			if (typeof window === 'undefined') return; // SSR guard
			
			try {
				const storedTheme = localStorage.getItem('hrims_theme') as 'light' | 'dark' | null;
				if (storedTheme) {
					update(state => ({ ...state, theme: storedTheme }));
				}
			} catch (error) {
				console.error('Failed to initialize UI state from localStorage:', error);
			}
		},

		// Utility methods for common notification patterns
		showSuccess: (title: string, message: string) => {
			return update(state => {
				const notification: Notification = {
					id: crypto.randomUUID(),
					type: 'success',
					title,
					message,
					timestamp: new Date().toISOString(),
					read: false
				};

				setTimeout(() => {
					update(s => ({
						...s,
						notifications: s.notifications.filter(n => n.id !== notification.id)
					}));
				}, 5000);

				return {
					...state,
					notifications: [notification, ...state.notifications]
				};
			});
		},

		showError: (title: string, message: string) => {
			return update(state => {
				const notification: Notification = {
					id: crypto.randomUUID(),
					type: 'error',
					title,
					message,
					timestamp: new Date().toISOString(),
					read: false
				};

				return {
					...state,
					notifications: [notification, ...state.notifications]
				};
			});
		},

		showWarning: (title: string, message: string) => {
			return update(state => {
				const notification: Notification = {
					id: crypto.randomUUID(),
					type: 'warning',
					title,
					message,
					timestamp: new Date().toISOString(),
					read: false
				};

				return {
					...state,
					notifications: [notification, ...state.notifications]
				};
			});
		},

		showInfo: (title: string, message: string) => {
			return update(state => {
				const notification: Notification = {
					id: crypto.randomUUID(),
					type: 'info',
					title,
					message,
					timestamp: new Date().toISOString(),
					read: false
				};

				return {
					...state,
					notifications: [notification, ...state.notifications]
				};
			});
		}
	};
}

export const uiStore = createUIStore();
