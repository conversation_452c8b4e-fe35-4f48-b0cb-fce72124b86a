{"meta": {"name": "HR Analytics & Reporting Style Guide", "file": "style-guide.analytics-reporting.json", "version": "1.0.0", "description": "KPI cards, chart styles, export flows and dashboard visuals using RTG brand palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"kpiBg": "#FFFFFF", "kpiValue": "#1C1C1C", "chartAccent": "#C49A6C", "chartNeutral": "#F5D6A1", "exportBtn": "#8C6239"}, "gradients": {"kpiAccent": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "exportHero": "linear-gradient(135deg,#8C6239,#3B2A1A)"}, "components": {"kpiGrid": {"cardBg": "#FFFFFF", "valueColor": "#1C1C1C", "accent": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}, "chartCard": {"legend": {"placement": "top-right", "fontSize": 13}, "downloadBtn": {"bg": "transparent", "text": "#5C4024"}}, "exportModal": {"bg": "#FFFFFF", "cta": {"bg": "#8C6239", "text": "#FFFFFF"}}}, "interactions": {"scheduledReports": {"badgeNextRun": true, "frequency": ["daily", "weekly", "monthly"]}, "adHocPreview": {"previewSample": true}}, "accessibility": {"tables": "aria-sort on columns and keyboard pagination"}, "notes": {"mocks": ["/api/analytics/kpis", "/api/analytics/exports"], "acceptance": ["KPIs show mock data and chart exports produce downloadable files", "Export options validate required fields before export"]}}