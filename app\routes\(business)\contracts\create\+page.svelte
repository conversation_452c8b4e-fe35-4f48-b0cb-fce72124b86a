<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import FileUpload from '$lib/components/ui/FileUpload.svelte';
	import { ArrowLeft, Save, X, FileText, User, Calendar, DollarSign } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Multi-step form state
	let currentStep = $state(1);
	const totalSteps = 4;

	// Form data
	let formData = $state({
		// Step 1: Employee Selection
		employee_id: '',
		employee_name: '',
		position: '',
		department: '',
		
		// Step 2: Contract Details
		contract_type: 'Permanent',
		contract_template: '',
		start_date: '',
		end_date: '',
		probation_period: '6',
		notice_period: '30',
		
		// Step 3: Compensation
		salary: '',
		currency: 'USD',
		salary_frequency: 'Annual',
		benefits: '',
		allowances: '',
		
		// Step 4: Terms & Documents
		terms_conditions: '',
		special_clauses: '',
		documents: [] as File[]
	});

	// Form validation errors
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Options
	const employeeOptions = [
		{ value: 'RTG001', label: 'John Doe - Senior Software Developer' },
		{ value: 'RTG002', label: 'Sarah Wilson - Marketing Manager' },
		{ value: 'RTG003', label: 'Mike Johnson - Project Consultant' },
		{ value: 'RTG004', label: 'Emily Brown - Financial Analyst' },
		{ value: 'RTG005', label: 'David Smith - Operations Manager' }
	];

	const contractTypeOptions = [
		{ value: 'Permanent', label: 'Permanent Employment' },
		{ value: 'Fixed Term', label: 'Fixed Term Contract' },
		{ value: 'Contract', label: 'Independent Contract' },
		{ value: 'Internship', label: 'Internship Agreement' },
		{ value: 'Probation', label: 'Probationary Contract' }
	];

	const templateOptions = [
		{ value: 'standard_permanent', label: 'Standard Permanent Employment' },
		{ value: 'senior_management', label: 'Senior Management Contract' },
		{ value: 'fixed_term', label: 'Fixed Term Agreement' },
		{ value: 'consultant', label: 'Consultant Agreement' },
		{ value: 'internship', label: 'Internship Agreement' }
	];

	const currencyOptions = [
		{ value: 'USD', label: 'USD ($)' },
		{ value: 'ZWL', label: 'ZWL (Z$)' },
		{ value: 'EUR', label: 'EUR (€)' },
		{ value: 'GBP', label: 'GBP (£)' }
	];

	const salaryFrequencyOptions = [
		{ value: 'Annual', label: 'Annual' },
		{ value: 'Monthly', label: 'Monthly' },
		{ value: 'Weekly', label: 'Weekly' },
		{ value: 'Hourly', label: 'Hourly' }
	];

	// Step validation
	const validateStep = (step: number): boolean => {
		const newErrors: Record<string, string> = {};

		switch (step) {
			case 1:
				if (!formData.employee_id) newErrors.employee_id = 'Employee selection is required';
				if (!formData.position) newErrors.position = 'Position is required';
				if (!formData.department) newErrors.department = 'Department is required';
				break;
			case 2:
				if (!formData.contract_type) newErrors.contract_type = 'Contract type is required';
				if (!formData.start_date) newErrors.start_date = 'Start date is required';
				if (formData.contract_type === 'Fixed Term' && !formData.end_date) {
					newErrors.end_date = 'End date is required for fixed term contracts';
				}
				break;
			case 3:
				if (!formData.salary) newErrors.salary = 'Salary is required';
				if (parseFloat(formData.salary) <= 0) newErrors.salary = 'Salary must be greater than 0';
				break;
			case 4:
				if (!formData.terms_conditions.trim()) {
					newErrors.terms_conditions = 'Terms and conditions are required';
				}
				break;
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Navigation
	const nextStep = () => {
		if (validateStep(currentStep)) {
			currentStep = Math.min(currentStep + 1, totalSteps);
		}
	};

	const prevStep = () => {
		currentStep = Math.max(currentStep - 1, 1);
	};

	const handleSubmit = async () => {
		if (!validateStep(currentStep)) {
			notificationStore.error('Validation Error', 'Please fix the errors below');
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 3000));
			
			console.log('Creating contract:', formData);
			
			notificationStore.success('Contract Created', 'New contract has been successfully created');
			
			// Redirect to contracts page
			goto('/contracts');
		} catch (error) {
			console.error('Error creating contract:', error);
			notificationStore.error('Creation Failed', 'Failed to create contract');
		} finally {
			isSubmitting = false;
		}
	};

	const handleCancel = () => {
		goto('/contracts');
	};

	// Auto-populate employee details when employee is selected
	const handleEmployeeChange = (employeeId: string) => {
		const employee = employeeOptions.find(e => e.value === employeeId);
		if (employee) {
			const [name, position] = employee.label.split(' - ');
			formData.employee_name = name;
			formData.position = position;
			// Mock department assignment
			formData.department = position.includes('Software') ? 'Information Technology' :
								position.includes('Marketing') ? 'Marketing' :
								position.includes('Financial') ? 'Finance' : 'Operations';
		}
	};

	const canCreate = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin';

	// Redirect if user doesn't have permission
	if (!canCreate) {
		goto('/contracts');
	}
</script>

<svelte:head>
	<title>Create Contract - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleCancel}>
				<ArrowLeft class="w-4 h-4" />
				Back to Contracts
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">Create New Contract</h1>
				<p class="text-muted-foreground">Step {currentStep} of {totalSteps}</p>
			</div>
		</div>
		
		<div class="flex gap-2">
			<Button variant="outline" onclick={handleCancel} disabled={isSubmitting}>
				<X class="w-4 h-4" />
				Cancel
			</Button>
		</div>
	</div>

	<!-- Progress Indicator -->
	<Card class="p-4">
		<div class="flex items-center justify-between">
			{#each Array(totalSteps) as _, i}
				<div class="flex items-center {i < totalSteps - 1 ? 'flex-1' : ''}">
					<div class="flex items-center justify-center w-8 h-8 rounded-full {currentStep > i + 1 ? 'bg-green-500 text-white' : currentStep === i + 1 ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}">
						{i + 1}
					</div>
					<div class="ml-2 text-sm {currentStep === i + 1 ? 'text-foreground font-medium' : 'text-muted-foreground'}">
						{i === 0 ? 'Employee' : i === 1 ? 'Contract Details' : i === 2 ? 'Compensation' : 'Terms & Documents'}
					</div>
					{#if i < totalSteps - 1}
						<div class="flex-1 h-px bg-border mx-4"></div>
					{/if}
				</div>
			{/each}
		</div>
	</Card>

	<!-- Form Steps -->
	<form onsubmit={(e) => { e.preventDefault(); }} class="space-y-6">
		{#if currentStep === 1}
			<!-- Step 1: Employee Selection -->
			<Card class="p-6">
				<div class="flex items-center gap-3 mb-6">
					<User class="w-6 h-6 text-primary" />
					<h3 class="text-lg font-semibold text-foreground">Employee Information</h3>
				</div>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div class="md:col-span-2">
						<Label for="employee_id">Select Employee *</Label>
						<Select
							options={employeeOptions}
							bind:value={formData.employee_id}
							error={errors.employee_id}
							disabled={isSubmitting}
							required
							placeholder="Choose an employee..."
							onchange={(value) => handleEmployeeChange(value)}
						/>
					</div>
					<div>
						<Label for="position">Position *</Label>
						<Input
							id="position"
							bind:value={formData.position}
							error={errors.position}
							disabled={isSubmitting}
							required
							placeholder="Job title"
						/>
					</div>
					<div>
						<Label for="department">Department *</Label>
						<Input
							id="department"
							bind:value={formData.department}
							error={errors.department}
							disabled={isSubmitting}
							required
							placeholder="Department name"
						/>
					</div>
				</div>
			</Card>
		{:else if currentStep === 2}
			<!-- Step 2: Contract Details -->
			<Card class="p-6">
				<div class="flex items-center gap-3 mb-6">
					<FileText class="w-6 h-6 text-primary" />
					<h3 class="text-lg font-semibold text-foreground">Contract Details</h3>
				</div>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<Label for="contract_type">Contract Type *</Label>
						<Select
							options={contractTypeOptions}
							bind:value={formData.contract_type}
							error={errors.contract_type}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="contract_template">Contract Template</Label>
						<Select
							options={templateOptions}
							bind:value={formData.contract_template}
							error={errors.contract_template}
							disabled={isSubmitting}
							placeholder="Select template..."
						/>
					</div>
					<div>
						<Label for="start_date">Start Date *</Label>
						<Input
							id="start_date"
							type="date"
							bind:value={formData.start_date}
							error={errors.start_date}
							disabled={isSubmitting}
							required
						/>
					</div>
					<div>
						<Label for="end_date">End Date {formData.contract_type === 'Fixed Term' ? '*' : ''}</Label>
						<Input
							id="end_date"
							type="date"
							bind:value={formData.end_date}
							error={errors.end_date}
							disabled={isSubmitting || formData.contract_type === 'Permanent'}
							required={formData.contract_type === 'Fixed Term'}
						/>
					</div>
					<div>
						<Label for="probation_period">Probation Period (months)</Label>
						<Input
							id="probation_period"
							type="number"
							bind:value={formData.probation_period}
							error={errors.probation_period}
							disabled={isSubmitting}
							min="0"
							max="12"
						/>
					</div>
					<div>
						<Label for="notice_period">Notice Period (days)</Label>
						<Input
							id="notice_period"
							type="number"
							bind:value={formData.notice_period}
							error={errors.notice_period}
							disabled={isSubmitting}
							min="0"
						/>
					</div>
				</div>
			</Card>
		{:else if currentStep === 3}
			<!-- Step 3: Compensation -->
			<Card class="p-6">
				<div class="flex items-center gap-3 mb-6">
					<DollarSign class="w-6 h-6 text-primary" />
					<h3 class="text-lg font-semibold text-foreground">Compensation Details</h3>
				</div>
				<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div>
						<Label for="currency">Currency</Label>
						<Select
							options={currencyOptions}
							bind:value={formData.currency}
							error={errors.currency}
							disabled={isSubmitting}
						/>
					</div>
					<div>
						<Label for="salary">Salary Amount *</Label>
						<Input
							id="salary"
							type="number"
							bind:value={formData.salary}
							error={errors.salary}
							disabled={isSubmitting}
							required
							placeholder="50000"
							min="0"
							step="0.01"
						/>
					</div>
					<div>
						<Label for="salary_frequency">Frequency</Label>
						<Select
							options={salaryFrequencyOptions}
							bind:value={formData.salary_frequency}
							error={errors.salary_frequency}
							disabled={isSubmitting}
						/>
					</div>
					<div class="md:col-span-3">
						<Label for="benefits">Benefits Package</Label>
						<Textarea
							id="benefits"
							bind:value={formData.benefits}
							error={errors.benefits}
							disabled={isSubmitting}
							rows={3}
							placeholder="Health insurance, dental coverage, retirement plan..."
						/>
					</div>
					<div class="md:col-span-3">
						<Label for="allowances">Allowances & Bonuses</Label>
						<Textarea
							id="allowances"
							bind:value={formData.allowances}
							error={errors.allowances}
							disabled={isSubmitting}
							rows={3}
							placeholder="Transport allowance, performance bonuses, overtime rates..."
						/>
					</div>
				</div>
			</Card>
		{:else if currentStep === 4}
			<!-- Step 4: Terms & Documents -->
			<Card class="p-6">
				<div class="flex items-center gap-3 mb-6">
					<Calendar class="w-6 h-6 text-primary" />
					<h3 class="text-lg font-semibold text-foreground">Terms & Documentation</h3>
				</div>
				<div class="space-y-4">
					<div>
						<Label for="terms_conditions">Terms & Conditions *</Label>
						<Textarea
							id="terms_conditions"
							bind:value={formData.terms_conditions}
							error={errors.terms_conditions}
							disabled={isSubmitting}
							required
							rows={6}
							placeholder="Standard terms and conditions, confidentiality clauses, non-compete agreements..."
						/>
					</div>
					<div>
						<Label for="special_clauses">Special Clauses</Label>
						<Textarea
							id="special_clauses"
							bind:value={formData.special_clauses}
							error={errors.special_clauses}
							disabled={isSubmitting}
							rows={4}
							placeholder="Any special terms, conditions, or clauses specific to this contract..."
						/>
					</div>
					<div>
						<Label for="documents">Supporting Documents</Label>
						<FileUpload
							bind:files={formData.documents}
							accept=".pdf,.doc,.docx"
							multiple={true}
							disabled={isSubmitting}
							maxSize={10 * 1024 * 1024}
							description="Upload contract templates, policy documents, or other supporting files (PDF, DOC, DOCX - Max 10MB each)"
						/>
					</div>
				</div>
			</Card>
		{/if}

		<!-- Navigation Buttons -->
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					{#if currentStep > 1}
						<Button variant="outline" onclick={prevStep} disabled={isSubmitting}>
							<ArrowLeft class="w-4 h-4" />
							Previous
						</Button>
					{/if}
				</div>
				<div class="flex gap-2">
					{#if currentStep < totalSteps}
						<Button variant="default" onclick={nextStep} disabled={isSubmitting}>
							Next
							<ArrowLeft class="w-4 h-4 rotate-180" />
						</Button>
					{:else}
						<Button variant="default" onclick={handleSubmit} disabled={isSubmitting}>
							<Save class="w-4 h-4" />
							{isSubmitting ? 'Creating Contract...' : 'Create Contract'}
						</Button>
					{/if}
				</div>
			</div>
		</Card>
	</form>
</div>
