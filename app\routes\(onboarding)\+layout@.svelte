<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { authStore } from '$lib/stores/authStore';
	import { uiStore } from '$lib/stores/uiStore';
	import favicon from '../../assets/images/favicon.svg';
	import '$lib/styles/global.css';
	import ToastContainer from '$lib/components/ui/ToastContainer.svelte';
	import { ModeWatcher } from 'mode-watcher';

	let { children } = $props();

	// Subscribe to auth state
	const auth = $derived($authStore);

	// Initialize stores on mount (since we're bypassing root layout)
	onMount(() => {
		authStore.initialize();
		uiStore.initialize();
		
		// Allow access to onboarding if user is authenticated or has valid invite token
		const hasInviteToken = $page.url.searchParams.has('token');
		const isTestMode = $page.url.searchParams.has('testing');
		
		if (!auth.isAuthenticated && !hasInviteToken && !isTestMode) {
			goto('/auth/login');
		}
	});
</script>

<svelte:head>
	<link rel="icon" href={favicon} />
	<title>Onboarding - Rainbow Tourism Group</title>
	<meta name="description" content="Complete your onboarding process with Rainbow Tourism Group" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
</svelte:head>

<ModeWatcher />

<!-- Onboarding Layout - Clean, minimal layout without dashboard navigation -->
<div class="min-h-screen flex items-center justify-center p-6">
	{@render children()}
</div>

<!-- Toast Container - Always present for notifications -->
<ToastContainer
	position="bottom-right"
	maxVisible={5}
	enableAnimations={true}
	enableSounds={false}
/>

<style>
	/* Onboarding-specific styles */
	:global(.onboarding-card) {
		background: #FFFFFF;
		border-radius: 24px;
		box-shadow: 0 6px 18px rgba(18, 22, 26, 0.06);
		width: 100%;
		max-width: 980px;
		overflow: hidden;
	}

	:global(.onboarding-stepper) {
		width: 280px;
		border-right: 1px solid rgba(234, 217, 191, 0.1);
	}

	:global(.onboarding-content) {
		flex: 1;
		padding: 48px;
	}

	:global(.onboarding-input) {
		height: 48px;
		border-radius: 12px;
		border: 1px solid #D8E6E2;
		padding: 0.75rem;
		font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
	}

	:global(.onboarding-input:focus) {
		outline: none;
		border-color: #1FAF9A;
		box-shadow: 0 0 0 4px rgba(31, 175, 154, 0.12);
	}

	:global(.onboarding-button-primary) {
		background: linear-gradient(135deg, #F5D6A1 0%, #C49A6C 100%);
		color: #1C1C1C;
		border-radius: 9999px;
		padding: 0.5rem 1.5rem;
		font-weight: 600;
		border: none;
		cursor: pointer;
		transition: all 220ms cubic-bezier(0.2, 0.9, 0.2, 1);
	}

	:global(.onboarding-button-primary:hover) {
		background: linear-gradient(135deg, #E9C584 0%, #C49A6C 100%);
		transform: translateY(-2px);
	}

	:global(.onboarding-button-primary:disabled) {
		opacity: 0.5;
		cursor: not-allowed;
		transform: none;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		:global(.onboarding-card) {
			margin: 1rem;
			max-width: calc(100vw - 2rem);
		}
		
		:global(.onboarding-stepper) {
			width: 100%;
			border-right: none;
			border-bottom: 1px solid rgba(234, 217, 191, 0.1);
		}
		
		:global(.onboarding-content) {
			padding: 20px;
		}
	}
</style>
