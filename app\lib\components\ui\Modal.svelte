<script lang="ts">
	// RTG-themed Modal component using shadcn-svelte Dialog
	import * as Dialog from './dialog/index.js';
	import { Button } from './button/index.js';
	import { X } from '@lucide/svelte';

	interface Props {
		open?: boolean;
		title?: string;
		description?: string;
		size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
		showCloseButton?: boolean;
		onClose?: () => void;
		children: import('svelte').Snippet;
		footer?: import('svelte').Snippet;
	}

	let {
		open = $bindable(false),
		title,
		description,
		size = 'md',
		showCloseButton = true,
		onClose,
		children,
		footer
	}: Props = $props();

	const sizeClasses = {
		sm: 'max-w-sm',
		md: 'max-w-md',
		lg: 'max-w-lg',
		xl: 'max-w-xl',
		full: 'max-w-full mx-4'
	};

	const handleClose = () => {
		open = false;
		onClose?.();
	};
</script>

<Dialog.Root bind:open>
	<Dialog.Content class="sm:{sizeClasses[size]} max-h-[90vh] overflow-y-auto">
		{#if title || showCloseButton}
			<Dialog.Header class="flex items-center justify-between">
				<div>
					{#if title}
						<Dialog.Title class="text-lg font-semibold text-foreground">
							{title}
						</Dialog.Title>
					{/if}
					{#if description}
						<Dialog.Description class="text-sm text-muted-foreground mt-1">
							{description}
						</Dialog.Description>
					{/if}
				</div>
				{#if showCloseButton}
					<Button
						variant="ghost"
						size="icon"
						onclick={handleClose}
						class="h-6 w-6 rounded-full"
					>
						<X class="h-4 w-4" />
						<span class="sr-only">Close</span>
					</Button>
				{/if}
			</Dialog.Header>
		{/if}

		<div class="py-4">
			{@render children()}
		</div>

		{#if footer}
			<Dialog.Footer class="flex justify-end gap-2 pt-4 border-t border-border">
				{@render footer()}
			</Dialog.Footer>
		{/if}
	</Dialog.Content>
</Dialog.Root>
