<script lang="ts">
	// shadcn/ui-based ToastContainer for managing multiple toast notifications
	import ToastProvider from './toast/toast-provider.svelte';
	import type { EnhancedNotification } from '$lib/stores/notificationStore';

	interface Props {
		position?: EnhancedNotification['position'];
		maxVisible?: number;
		enableAnimations?: boolean;
		enableSounds?: boolean;
		className?: string;
	}

	let {
		position = 'bottom-right',
		maxVisible = 5,
		enableAnimations = true,
		enableSounds = false,
		className = ''
	}: Props = $props();
</script>

<ToastProvider
	{position}
	{maxVisible}
	{enableAnimations}
	{className}
/>

