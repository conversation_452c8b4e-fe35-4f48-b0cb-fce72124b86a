<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import Modal from '$lib/components/ui/Modal.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import { 
		Search, 
		Filter, 
		CheckCircle, 
		XCircle, 
		Clock, 
		Calendar,
		User,
		FileText,
		MessageSquare
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock leave requests data
	let leaveRequests = $state([
		{
			id: 1,
			employee_name: '<PERSON>',
			employee_id: 'RTG001',
			department: 'Information Technology',
			leave_type: 'Annual Leave',
			start_date: '2025-08-25',
			end_date: '2025-08-27',
			days_requested: 3,
			status: 'pending',
			applied_date: '2025-08-15',
			reason: 'Family vacation',
			manager: '<PERSON>',
			remaining_balance: 18
		},
		{
			id: 2,
			employee_name: 'Emily <PERSON>',
			employee_id: 'RTG002',
			department: 'Marketing',
			leave_type: 'Sick Leave',
			start_date: '2025-08-20',
			end_date: '2025-08-21',
			days_requested: 2,
			status: 'approved',
			applied_date: '2025-08-18',
			reason: 'Medical appointment',
			manager: 'Mike Johnson',
			remaining_balance: 8
		},
		{
			id: 3,
			employee_name: 'David Smith',
			employee_id: 'RTG003',
			department: 'Finance',
			leave_type: 'Personal Leave',
			start_date: '2025-09-01',
			end_date: '2025-09-03',
			days_requested: 3,
			status: 'rejected',
			applied_date: '2025-08-10',
			reason: 'Personal matters',
			manager: 'Sarah Wilson',
			remaining_balance: 5,
			rejection_reason: 'Insufficient leave balance'
		},
		{
			id: 4,
			employee_name: 'Lisa Johnson',
			employee_id: 'RTG004',
			department: 'Human Resources',
			leave_type: 'Maternity Leave',
			start_date: '2025-09-15',
			end_date: '2025-12-15',
			days_requested: 90,
			status: 'approved',
			applied_date: '2025-07-20',
			reason: 'Maternity leave',
			manager: 'Sarah Wilson',
			remaining_balance: 0
		}
	]);

	// Filter and search state
	let searchTerm = $state('');
	let statusFilter = $state('all');
	let departmentFilter = $state('all');
	let leaveTypeFilter = $state('all');

	// Modal state
	let showActionModal = $state(false);
	let selectedRequest = $state<typeof leaveRequests[0] | null>(null);
	let actionType = $state<'approve' | 'reject'>('approve');
	let actionNotes = $state('');
	let isProcessing = $state(false);

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Status' },
		{ value: 'pending', label: 'Pending' },
		{ value: 'approved', label: 'Approved' },
		{ value: 'rejected', label: 'Rejected' }
	];

	const departmentOptions = [
		{ value: 'all', label: 'All Departments' },
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Human Resources', label: 'Human Resources' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Operations', label: 'Operations' }
	];

	const leaveTypeOptions = [
		{ value: 'all', label: 'All Leave Types' },
		{ value: 'Annual Leave', label: 'Annual Leave' },
		{ value: 'Sick Leave', label: 'Sick Leave' },
		{ value: 'Personal Leave', label: 'Personal Leave' },
		{ value: 'Maternity Leave', label: 'Maternity Leave' },
		{ value: 'Paternity Leave', label: 'Paternity Leave' }
	];

	// Computed filtered requests
	const filteredRequests = $derived(() => {
		if (!Array.isArray(leaveRequests)) return [];

		return leaveRequests.filter(request => {
			const matchesSearch = searchTerm === '' ||
				request.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				request.employee_id.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
			const matchesDepartment = departmentFilter === 'all' || request.department === departmentFilter;
			const matchesLeaveType = leaveTypeFilter === 'all' || request.leave_type === leaveTypeFilter;

			return matchesSearch && matchesStatus && matchesDepartment && matchesLeaveType;
		});
	});

	// Status styling
	const getStatusColor = (status: string) => {
		switch (status) {
			case 'approved': return 'bg-green-100 text-green-800 border-green-200';
			case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
			case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
			default: return 'bg-gray-100 text-gray-800 border-gray-200';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'approved': return CheckCircle;
			case 'rejected': return XCircle;
			case 'pending': return Clock;
			default: return Clock;
		}
	};

	// Actions
	const handleApprove = (request: typeof leaveRequests[0]) => {
		selectedRequest = request;
		actionType = 'approve';
		showActionModal = true;
	};

	const handleReject = (request: typeof leaveRequests[0]) => {
		selectedRequest = request;
		actionType = 'reject';
		showActionModal = true;
	};

	const handleProcessAction = async () => {
		if (!selectedRequest) return;

		isProcessing = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Update the request status
			const requestIndex = leaveRequests.findIndex(r => r.id === selectedRequest.id);
			if (requestIndex !== -1) {
				leaveRequests[requestIndex] = {
					...leaveRequests[requestIndex],
					status: actionType === 'approve' ? 'approved' : 'rejected',
					...(actionType === 'reject' && { rejection_reason: actionNotes })
				};
			}

			notificationStore.success(
				`Request ${actionType === 'approve' ? 'Approved' : 'Rejected'}`,
				`Leave request for ${selectedRequest.employee_name} has been ${actionType === 'approve' ? 'approved' : 'rejected'}`
			);

			showActionModal = false;
			actionNotes = '';
		} catch (error) {
			console.error('Error processing request:', error);
			notificationStore.error('Action Failed', 'Failed to process leave request');
		} finally {
			isProcessing = false;
		}
	};

	const canManageRequests = $derived(user?.user_role === 'hr_admin' || user?.user_role === 'super_admin' || user?.user_role === 'manager');
</script>

<svelte:head>
	<title>Leave Requests - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Leave Requests</h1>
			<p class="text-muted-foreground">Review and manage employee leave requests</p>
		</div>
	</div>

	<!-- Filters -->
	<Card class="p-4">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			<div>
				<Input
					placeholder="Search employees..."
					bind:value={searchTerm}
					class="w-full"
				>
					<Search slot="icon" class="w-4 h-4" />
				</Input>
			</div>
			<div>
				<Select
					options={statusOptions}
					bind:value={statusFilter}
					placeholder="Filter by status"
				/>
			</div>
			<div>
				<Select
					options={departmentOptions}
					bind:value={departmentFilter}
					placeholder="Filter by department"
				/>
			</div>
			<div>
				<Select
					options={leaveTypeOptions}
					bind:value={leaveTypeFilter}
					placeholder="Filter by leave type"
				/>
			</div>
			<div class="flex items-center">
				<Button variant="outline" class="w-full">
					<Filter class="w-4 h-4" />
					More Filters
				</Button>
			</div>
		</div>
	</Card>

	<!-- Summary Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Requests</p>
					<p class="text-2xl font-bold text-foreground">{filteredRequests.length}</p>
				</div>
				<FileText class="w-8 h-8 text-muted-foreground" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Pending</p>
					<p class="text-2xl font-bold text-yellow-600">
						{filteredRequests.filter(r => r.status === 'pending').length}
					</p>
				</div>
				<Clock class="w-8 h-8 text-yellow-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Approved</p>
					<p class="text-2xl font-bold text-green-600">
						{filteredRequests.filter(r => r.status === 'approved').length}
					</p>
				</div>
				<CheckCircle class="w-8 h-8 text-green-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Rejected</p>
					<p class="text-2xl font-bold text-red-600">
						{filteredRequests.filter(r => r.status === 'rejected').length}
					</p>
				</div>
				<XCircle class="w-8 h-8 text-red-600" />
			</div>
		</Card>
	</div>

	<!-- Requests List -->
	<Card class="overflow-hidden">
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead class="bg-muted/50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Employee
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Leave Details
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Duration
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Status
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Applied
						</th>
						{#if canManageRequests}
							<th class="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Actions
							</th>
						{/if}
					</tr>
				</thead>
				<tbody class="bg-background divide-y divide-border">
					{#each filteredRequests as request}
						{@const StatusIcon = getStatusIcon(request.status)}
						<tr class="hover:bg-muted/50 transition-colors">
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="flex items-center">
									<div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
										<span class="text-primary-foreground font-medium text-sm">
											{request.employee_name.split(' ').map(n => n.charAt(0)).join('')}
										</span>
									</div>
									<div class="ml-3">
										<div class="text-sm font-medium text-foreground">{request.employee_name}</div>
										<div class="text-sm text-muted-foreground">{request.employee_id} • {request.department}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-foreground">{request.leave_type}</div>
								<div class="text-sm text-muted-foreground">{request.reason}</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-foreground">
									{new Date(request.start_date).toLocaleDateString()} - {new Date(request.end_date).toLocaleDateString()}
								</div>
								<div class="text-sm text-muted-foreground">{request.days_requested} day{request.days_requested > 1 ? 's' : ''}</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<Badge class={getStatusColor(request.status)}>
									<StatusIcon class="w-3 h-3 mr-1" />
									{request.status.charAt(0).toUpperCase() + request.status.slice(1)}
								</Badge>
								{#if request.status === 'rejected' && request.rejection_reason}
									<div class="text-xs text-red-600 mt-1">{request.rejection_reason}</div>
								{/if}
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
								{new Date(request.applied_date).toLocaleDateString()}
							</td>
							{#if canManageRequests}
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									{#if request.status === 'pending'}
										<div class="flex items-center justify-end gap-2">
											<Button variant="outline" size="sm" onclick={() => handleApprove(request)}>
												<CheckCircle class="w-4 h-4" />
												Approve
											</Button>
											<Button variant="destructive" size="sm" onclick={() => handleReject(request)}>
												<XCircle class="w-4 h-4" />
												Reject
											</Button>
										</div>
									{:else}
										<span class="text-muted-foreground">No actions</span>
									{/if}
								</td>
							{/if}
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</Card>
</div>

<!-- Action Modal -->
<Modal bind:open={showActionModal} title="{actionType === 'approve' ? 'Approve' : 'Reject'} Leave Request" size="md">
	{#snippet children()}
		{#if selectedRequest}
			<div class="space-y-4">
				<div class="p-4 bg-muted rounded-lg">
					<h4 class="font-medium text-foreground mb-2">Request Details</h4>
					<div class="grid grid-cols-2 gap-2 text-sm">
						<div><span class="text-muted-foreground">Employee:</span> {selectedRequest.employee_name}</div>
						<div><span class="text-muted-foreground">Leave Type:</span> {selectedRequest.leave_type}</div>
						<div><span class="text-muted-foreground">Duration:</span> {selectedRequest.days_requested} days</div>
						<div><span class="text-muted-foreground">Dates:</span> {new Date(selectedRequest.start_date).toLocaleDateString()} - {new Date(selectedRequest.end_date).toLocaleDateString()}</div>
					</div>
				</div>
				
				<div>
					<label class="text-sm font-medium text-foreground mb-2 block">
						{actionType === 'approve' ? 'Approval Notes (Optional)' : 'Rejection Reason *'}
					</label>
					<textarea
						bind:value={actionNotes}
						class="w-full p-2 border border-border rounded-md bg-background text-foreground"
						rows="3"
						placeholder={actionType === 'approve' 
							? 'Add any notes about this approval...' 
							: 'Please provide a reason for rejection...'}
						disabled={isProcessing}
						required={actionType === 'reject'}
					></textarea>
				</div>
			</div>
		{/if}
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showActionModal = false} disabled={isProcessing}>
			Cancel
		</Button>
		<Button 
			variant={actionType === 'approve' ? 'default' : 'destructive'} 
			onclick={handleProcessAction} 
			disabled={isProcessing || (actionType === 'reject' && !actionNotes.trim())}
		>
			{isProcessing ? 'Processing...' : actionType === 'approve' ? 'Approve Request' : 'Reject Request'}
		</Button>
	{/snippet}
</Modal>
