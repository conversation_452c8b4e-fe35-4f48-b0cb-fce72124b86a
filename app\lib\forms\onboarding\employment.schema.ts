import { z } from 'zod';

// Employment Details Schema
export const employmentDetailsSchema = z.object({
	// Job Information
	jobTitle: z
		.string()
		.min(2, 'Job title must be at least 2 characters')
		.max(100, 'Job title must be less than 100 characters'),
	
	department: z
		.string()
		.min(1, 'Please select a department')
		.max(50, 'Department must be less than 50 characters'),
	
	employeeId: z
		.string()
		.min(3, 'Employee ID must be at least 3 characters')
		.max(20, 'Employee ID must be less than 20 characters')
		.regex(/^[A-Z0-9-]+$/, 'Employee ID can only contain uppercase letters, numbers, and hyphens')
		.optional(), // Usually auto-generated
	
	startDate: z
		.string()
		.refine((date) => {
			const startDate = new Date(date);
			const today = new Date();
			// Start date should not be more than 1 year in the past or more than 6 months in the future
			const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
			const sixMonthsFromNow = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
			
			return startDate >= oneYearAgo && startDate <= sixMonthsFromNow;
		}, 'Start date must be within the last year or next 6 months'),
	
	// Manager Information
	managerId: z
		.string()
		.min(1, 'Please select a manager')
		.max(20, 'Manager ID must be less than 20 characters'),
	
	managerName: z
		.string()
		.min(2, 'Manager name must be at least 2 characters')
		.max(100, 'Manager name must be less than 100 characters'),
	
	managerEmail: z
		.string()
		.email('Please enter a valid manager email address')
		.max(100, 'Manager email must be less than 100 characters'),
	
	// Employment Type
	employmentType: z.enum(['full-time', 'part-time', 'contract', 'temporary'], {
		errorMap: () => ({ message: 'Please select an employment type' })
	}),
	
	// Work Arrangement
	workArrangement: z.enum(['on-site', 'remote', 'hybrid'], {
		errorMap: () => ({ message: 'Please select a work arrangement' })
	}),
	
	// Location Information
	workLocation: z
		.string()
		.min(1, 'Please select a work location')
		.max(100, 'Work location must be less than 100 characters'),
	
	// Salary Information (optional for some roles)
	salaryBand: z
		.string()
		.max(20, 'Salary band must be less than 20 characters')
		.optional(),
	
	currency: z
		.string()
		.length(3, 'Currency must be a 3-letter code')
		.default('USD'),
	
	// Probation Period
	probationPeriod: z
		.number()
		.min(0, 'Probation period cannot be negative')
		.max(12, 'Probation period cannot exceed 12 months')
		.default(3),
	
	// Reports and Team
	reportsTo: z
		.string()
		.max(100, 'Reports to must be less than 100 characters')
		.optional(),
	
	teamSize: z
		.number()
		.min(0, 'Team size cannot be negative')
		.max(1000, 'Team size seems unrealistic')
		.optional(),
	
	// Additional Information
	jobDescription: z
		.string()
		.max(1000, 'Job description must be less than 1000 characters')
		.optional(),
	
	specialRequirements: z
		.string()
		.max(500, 'Special requirements must be less than 500 characters')
		.optional()
});

// Manager lookup schema for autocomplete
export const managerLookupSchema = z.object({
	id: z.string(),
	name: z.string(),
	email: z.string().email(),
	department: z.string(),
	title: z.string(),
	avatar: z.string().url().optional()
});

// Department schema
export const departmentSchema = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().optional(),
	managerId: z.string(),
	location: z.string()
});

// Type inference
export type EmploymentDetailsFormData = z.infer<typeof employmentDetailsSchema>;
export type ManagerLookupData = z.infer<typeof managerLookupSchema>;
export type DepartmentData = z.infer<typeof departmentSchema>;

// Validation helper function
export const validateEmploymentDetails = (data: unknown) => {
	return employmentDetailsSchema.safeParse(data);
};

// Field-specific validation helpers
export const validateStartDate = (startDate: string) => {
	const date = new Date(startDate);
	const today = new Date();
	const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
	const sixMonthsFromNow = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
	
	return date >= oneYearAgo && date <= sixMonthsFromNow;
};

export const validateEmployeeId = (employeeId: string) => {
	return z.string().regex(/^[A-Z0-9-]+$/).safeParse(employeeId);
};

// Default values for form initialization
export const employmentDetailsDefaults: Partial<EmploymentDetailsFormData> = {
	jobTitle: '',
	department: '',
	employeeId: '',
	startDate: '',
	managerId: '',
	managerName: '',
	managerEmail: '',
	employmentType: 'full-time',
	workArrangement: 'on-site',
	workLocation: '',
	salaryBand: '',
	currency: 'USD',
	probationPeriod: 3,
	reportsTo: '',
	teamSize: 0,
	jobDescription: '',
	specialRequirements: ''
};

// Common department options
export const commonDepartments = [
	{ id: 'hr', name: 'Human Resources' },
	{ id: 'finance', name: 'Finance & Accounting' },
	{ id: 'operations', name: 'Operations' },
	{ id: 'marketing', name: 'Marketing & Sales' },
	{ id: 'it', name: 'Information Technology' },
	{ id: 'guest-services', name: 'Guest Services' },
	{ id: 'housekeeping', name: 'Housekeeping' },
	{ id: 'food-beverage', name: 'Food & Beverage' },
	{ id: 'maintenance', name: 'Maintenance' },
	{ id: 'security', name: 'Security' }
];

// Common work locations for RTG
export const workLocations = [
	{ id: 'harare-head-office', name: 'Harare - Head Office' },
	{ id: 'victoria-falls', name: 'Victoria Falls' },
	{ id: 'bulawayo', name: 'Bulawayo' },
	{ id: 'eastern-highlands', name: 'Eastern Highlands' },
	{ id: 'remote', name: 'Remote' }
];
