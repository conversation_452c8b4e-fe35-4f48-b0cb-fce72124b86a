# 04 — Contracts Management (Frontend-only)

Pages:
- `/contracts` — list of contracts, search and filter
- `/contracts/[id]` — contract detail & preview (PDF viewer)
- `/contracts/create` — contract generator wizard (fill fields, preview, request signature)

Components:
- `ContractWizard.svelte` — fields + template preview pane
- `PdfViewer.svelte` — show mock PDF (use pdf.js)
- `SignaturePad.svelte` — canvas-like signature capture (store as base64 in mock)

UI flows:
1. H<PERSON> opens `/contracts/create`, fills wizard -> press "Generate PDF" (client-side template renderer) -> preview.
2. Click "Send for signature" -> an in-app notification to mock user -> signature modal available.
3. On sign, contract state toggles to `signed` and `signed_at` set in mock DB.

Acceptance:
- Contracts generated from templates with correct merge fields.
- SignaturePad stores an image and displays "signed" flag.

Security note:
- For frontend-only stage, store contract previews in mock store, but design UI with places to pass a `storage_path` and scan status later.
