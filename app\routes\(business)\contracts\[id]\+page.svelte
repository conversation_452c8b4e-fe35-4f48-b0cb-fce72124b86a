<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import Modal from '$lib/components/ui/Modal.svelte';
	import { 
		ArrowLeft, 
		Edit, 
		Download, 
		FileText, 
		Calendar, 
		DollarSign,
		User,
		Building,
		Clock,
		CheckCircle,
		XCircle,
		AlertTriangle,
		Mail,
		Phone,
		MapPin
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const contractId = page.params.id;

	// Mock contract data - in real app, this would be fetched from API
	const contract = {
		id: contractId,
		employee_name: '<PERSON>',
		employee_id: 'RTG001',
		employee_email: '<EMAIL>',
		employee_phone: '+263 77 555 0123',
		employee_address: '123 Main Street, Harare, Zimbabwe',
		contract_number: 'RTG-2024-001',
		contract_type: 'Permanent',
		position: 'Senior Software Developer',
		department: 'Information Technology',
		start_date: '2024-01-15',
		end_date: null,
		salary: 75000,
		currency: 'USD',
		salary_frequency: 'Annual',
		status: 'active',
		created_date: '2024-01-10',
		created_by: 'HR Admin',
		last_modified: '2024-01-15',
		probation_period: 6,
		notice_period: 30,
		benefits: 'Health insurance, dental coverage, retirement plan, life insurance',
		allowances: 'Transport allowance: $200/month, Performance bonus: Up to 15% annually',
		terms_conditions: 'This employment contract is governed by the laws of Zimbabwe. The employee agrees to maintain confidentiality of company information and comply with all company policies and procedures.',
		special_clauses: 'Remote work allowed up to 2 days per week with manager approval. Annual performance review required.',
		documents: [
			{ name: 'Employment Contract.pdf', size: '2.3 MB', uploaded: '2024-01-10' },
			{ name: 'Job Description.pdf', size: '1.1 MB', uploaded: '2024-01-10' },
			{ name: 'Company Policies.pdf', size: '3.2 MB', uploaded: '2024-01-10' }
		]
	};

	// Contract history/timeline
	const contractHistory = [
		{
			date: '2024-01-15',
			action: 'Contract Activated',
			description: 'Contract became active on employee start date',
			user: 'HR Admin',
			status: 'active'
		},
		{
			date: '2024-01-12',
			action: 'Contract Signed',
			description: 'Employee signed the employment contract',
			user: 'John Doe',
			status: 'signed'
		},
		{
			date: '2024-01-10',
			action: 'Contract Created',
			description: 'Initial contract created and sent for review',
			user: 'HR Admin',
			status: 'created'
		}
	];

	let showStatusModal = $state(false);
	let newStatus = $state(contract.status);
	let statusNotes = $state('');
	let isUpdatingStatus = $state(false);

	const statusOptions = [
		{ value: 'active', label: 'Active', color: 'bg-green-100 text-green-800' },
		{ value: 'pending_signature', label: 'Pending Signature', color: 'bg-yellow-100 text-yellow-800' },
		{ value: 'expired', label: 'Expired', color: 'bg-red-100 text-red-800' },
		{ value: 'terminated', label: 'Terminated', color: 'bg-gray-100 text-gray-800' },
		{ value: 'draft', label: 'Draft', color: 'bg-blue-100 text-blue-800' }
	];

	const getStatusInfo = (status: string) => {
		return statusOptions.find(s => s.value === status) || statusOptions[0];
	};

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'active': return CheckCircle;
			case 'pending_signature': return Clock;
			case 'expired': return XCircle;
			case 'terminated': return XCircle;
			case 'draft': return Edit;
			default: return Clock;
		}
	};

	const handleBack = () => {
		goto('/contracts');
	};

	const handleEdit = () => {
		goto(`/contracts/${contractId}/edit`);
	};

	const handleUpdateStatus = async () => {
		isUpdatingStatus = true;
		
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1500));
			
			console.log('Updating contract status:', { contractId, newStatus, statusNotes });
			
			notificationStore.success('Status Updated', 'Contract status has been successfully updated');
			showStatusModal = false;
			statusNotes = '';
		} catch (error) {
			console.error('Error updating status:', error);
			notificationStore.error('Update Failed', 'Failed to update contract status');
		} finally {
			isUpdatingStatus = false;
		}
	};

	const handleDownloadContract = () => {
		// In real app, this would download the actual contract
		notificationStore.info('Download Started', 'Contract download has started');
	};

	const handleDownloadDocument = (document: typeof contract.documents[0]) => {
		// In real app, this would download the specific document
		notificationStore.info('Download Started', `${document.name} download has started`);
	};

	const canManage = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin';
</script>

<svelte:head>
	<title>Contract {contract.contract_number} - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleBack}>
				<ArrowLeft class="w-4 h-4" />
				Back to Contracts
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">Contract {contract.contract_number}</h1>
				<p class="text-muted-foreground">{contract.employee_name} - {contract.position}</p>
			</div>
		</div>
		
		{#if canManage}
			<div class="flex gap-2">
				<Button variant="outline" onclick={handleDownloadContract}>
					<Download class="w-4 h-4" />
					Download
				</Button>
				<Button variant="outline" onclick={handleEdit}>
					<Edit class="w-4 h-4" />
					Edit Contract
				</Button>
				<Button variant="default" onclick={() => showStatusModal = true}>
					Update Status
				</Button>
			</div>
		{/if}
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Contract Overview -->
		<div class="lg:col-span-1">
			<Card class="p-6">
				<div class="text-center mb-6">
					<div class="w-20 h-20 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
						<FileText class="w-10 h-10 text-primary-foreground" />
					</div>
					<h2 class="text-xl font-semibold text-foreground mb-1">{contract.contract_number}</h2>
					<p class="text-muted-foreground mb-3">{contract.contract_type}</p>
					<Badge class={getStatusInfo(contract.status).color}>
						{getStatusInfo(contract.status).label}
					</Badge>
				</div>

				<div class="space-y-4">
					<div class="flex items-center justify-between">
						<span class="text-sm font-medium text-muted-foreground">Created</span>
						<span class="text-sm text-foreground">{new Date(contract.created_date).toLocaleDateString()}</span>
					</div>
					<div class="flex items-center justify-between">
						<span class="text-sm font-medium text-muted-foreground">Start Date</span>
						<span class="text-sm text-foreground">{new Date(contract.start_date).toLocaleDateString()}</span>
					</div>
					{#if contract.end_date}
						<div class="flex items-center justify-between">
							<span class="text-sm font-medium text-muted-foreground">End Date</span>
							<span class="text-sm text-foreground">{new Date(contract.end_date).toLocaleDateString()}</span>
						</div>
					{/if}
					<div class="flex items-center justify-between">
						<span class="text-sm font-medium text-muted-foreground">Probation</span>
						<span class="text-sm text-foreground">{contract.probation_period} months</span>
					</div>
					<div class="flex items-center justify-between">
						<span class="text-sm font-medium text-muted-foreground">Notice Period</span>
						<span class="text-sm text-foreground">{contract.notice_period} days</span>
					</div>
				</div>

				<div class="mt-6 pt-6 border-t border-border">
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground">
							{contract.currency} {contract.salary.toLocaleString()}
						</div>
						<div class="text-sm text-muted-foreground">{contract.salary_frequency}</div>
					</div>
				</div>
			</Card>
		</div>

		<!-- Contract Details -->
		<div class="lg:col-span-2 space-y-6">
			<!-- Employee Information -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Employee Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<div class="flex items-center gap-3 mb-4">
							<div class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
								<span class="text-primary-foreground font-bold text-lg">
									{contract.employee_name.split(' ').map(n => n.charAt(0)).join('')}
								</span>
							</div>
							<div>
								<h4 class="text-lg font-semibold text-foreground">{contract.employee_name}</h4>
								<p class="text-sm text-muted-foreground">{contract.employee_id}</p>
							</div>
						</div>
						<div class="space-y-2">
							<div class="flex items-center gap-2 text-sm">
								<Mail class="w-4 h-4 text-muted-foreground" />
								<span class="text-foreground">{contract.employee_email}</span>
							</div>
							<div class="flex items-center gap-2 text-sm">
								<Phone class="w-4 h-4 text-muted-foreground" />
								<span class="text-foreground">{contract.employee_phone}</span>
							</div>
							<div class="flex items-center gap-2 text-sm">
								<MapPin class="w-4 h-4 text-muted-foreground" />
								<span class="text-foreground">{contract.employee_address}</span>
							</div>
						</div>
					</div>
					<div>
						<div class="space-y-4">
							<div>
								<div class="text-sm font-medium text-muted-foreground mb-1">Position</div>
								<p class="text-foreground">{contract.position}</p>
							</div>
							<div>
								<div class="text-sm font-medium text-muted-foreground mb-1">Department</div>
								<p class="text-foreground">{contract.department}</p>
							</div>
							<div>
								<div class="text-sm font-medium text-muted-foreground mb-1">Contract Type</div>
								<p class="text-foreground">{contract.contract_type}</p>
							</div>
						</div>
					</div>
				</div>
			</Card>

			<!-- Compensation -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Compensation & Benefits</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Base Salary</div>
						<p class="text-lg font-semibold text-foreground">
							{contract.currency} {contract.salary.toLocaleString()} / {contract.salary_frequency}
						</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Benefits Package</div>
						<p class="text-foreground text-sm">{contract.benefits}</p>
					</div>
					<div class="md:col-span-2">
						<div class="text-sm font-medium text-muted-foreground mb-1">Allowances & Bonuses</div>
						<p class="text-foreground text-sm">{contract.allowances}</p>
					</div>
				</div>
			</Card>

			<!-- Terms & Conditions -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Terms & Conditions</h3>
				<div class="space-y-4">
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-2">General Terms</div>
						<p class="text-foreground text-sm leading-relaxed">{contract.terms_conditions}</p>
					</div>
					{#if contract.special_clauses}
						<div>
							<div class="text-sm font-medium text-muted-foreground mb-2">Special Clauses</div>
							<p class="text-foreground text-sm leading-relaxed">{contract.special_clauses}</p>
						</div>
					{/if}
				</div>
			</Card>

			<!-- Documents -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Contract Documents</h3>
				<div class="space-y-3">
					{#each contract.documents as document}
						<div class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50 transition-colors">
							<div class="flex items-center gap-3">
								<FileText class="w-5 h-5 text-muted-foreground" />
								<div>
									<div class="text-sm font-medium text-foreground">{document.name}</div>
									<div class="text-xs text-muted-foreground">
										{document.size} • Uploaded {new Date(document.uploaded).toLocaleDateString()}
									</div>
								</div>
							</div>
							<Button variant="outline" size="sm" onclick={() => handleDownloadDocument(document)}>
								<Download class="w-4 h-4" />
								Download
							</Button>
						</div>
					{/each}
				</div>
			</Card>

			<!-- Contract History -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Contract History</h3>
				<div class="space-y-4">
					{#each contractHistory as event}
						<div class="flex items-start gap-3">
							<div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
							<div class="flex-1">
								<div class="flex items-center justify-between mb-1">
									<h4 class="text-sm font-medium text-foreground">{event.action}</h4>
									<span class="text-xs text-muted-foreground">
										{new Date(event.date).toLocaleDateString()}
									</span>
								</div>
								<p class="text-sm text-muted-foreground mb-1">{event.description}</p>
								<p class="text-xs text-muted-foreground">by {event.user}</p>
							</div>
						</div>
					{/each}
				</div>
			</Card>
		</div>
	</div>
</div>

<!-- Status Update Modal -->
<Modal bind:open={showStatusModal} title="Update Contract Status" size="md">
	{#snippet children()}
		<div class="space-y-4">
			<div>
				<label class="text-sm font-medium text-foreground mb-2 block">New Status</label>
				<select 
					bind:value={newStatus}
					class="w-full p-2 border border-border rounded-md bg-background text-foreground"
					disabled={isUpdatingStatus}
				>
					{#each statusOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>
			<div>
				<label class="text-sm font-medium text-foreground mb-2 block">Notes (Optional)</label>
				<textarea
					bind:value={statusNotes}
					class="w-full p-2 border border-border rounded-md bg-background text-foreground"
					rows="3"
					placeholder="Add any notes about this status change..."
					disabled={isUpdatingStatus}
				></textarea>
			</div>
		</div>
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showStatusModal = false} disabled={isUpdatingStatus}>
			Cancel
		</Button>
		<Button variant="default" onclick={handleUpdateStatus} disabled={isUpdatingStatus}>
			{isUpdatingStatus ? 'Updating...' : 'Update Status'}
		</Button>
	{/snippet}
</Modal>
