<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import { Calendar, TrendingUp, Clock, FileText } from '@lucide/svelte';

	const auth = $derived($authStore);
	const employee = $derived(auth.employee);

	// Mock leave balance data
	const leaveBalance = {
		annual: {
			total: 21,
			used: 8,
			remaining: 13,
			pending: 2,
			accrued_this_year: 21,
			carried_forward: 0
		},
		sick: {
			total: 10,
			used: 2,
			remaining: 8,
			pending: 0,
			accrued_this_year: 10,
			carried_forward: 0
		},
		personal: {
			total: 5,
			used: 1,
			remaining: 4,
			pending: 0,
			accrued_this_year: 5,
			carried_forward: 0
		},
		maternity: {
			total: 90,
			used: 0,
			remaining: 90,
			pending: 0,
			accrued_this_year: 90,
			carried_forward: 0
		}
	};

	// Mock leave history
	const leaveHistory = [
		{
			id: 1,
			type: 'Annual Leave',
			start_date: '2024-01-15',
			end_date: '2024-01-19',
			days: 5,
			status: 'Approved',
			reason: 'Family vacation'
		},
		{
			id: 2,
			type: 'Sick Leave',
			start_date: '2024-01-08',
			end_date: '2024-01-09',
			days: 2,
			status: 'Approved',
			reason: 'Medical appointment'
		},
		{
			id: 3,
			type: 'Annual Leave',
			start_date: '2023-12-20',
			end_date: '2023-12-22',
			days: 3,
			status: 'Approved',
			reason: 'Holiday break'
		},
		{
			id: 4,
			type: 'Personal Leave',
			start_date: '2023-11-15',
			end_date: '2023-11-15',
			days: 1,
			status: 'Approved',
			reason: 'Personal matters'
		}
	];

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'approved': return 'text-green-600 bg-green-100';
			case 'pending': return 'text-yellow-600 bg-yellow-100';
			case 'rejected': return 'text-red-600 bg-red-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	const getLeaveTypeColor = (type: string) => {
		switch (type.toLowerCase()) {
			case 'annual leave': return 'text-blue-600';
			case 'sick leave': return 'text-red-600';
			case 'personal leave': return 'text-purple-600';
			case 'maternity leave': return 'text-pink-600';
			default: return 'text-gray-600';
		}
	};

	const calculateUsagePercentage = (used: number, total: number) => {
		return total > 0 ? Math.round((used / total) * 100) : 0;
	};
</script>

<svelte:head>
	<title>Leave Balance - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Leave Balance</h1>
			<p class="text-muted-foreground">View your leave entitlements and usage</p>
		</div>
		<div class="text-sm text-muted-foreground">
			Leave Year: January 2024 - December 2024
		</div>
	</div>

	<!-- Leave Balance Overview -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
		{#each Object.entries(leaveBalance) as [key, balance]}
			{@const leaveType = key.charAt(0).toUpperCase() + key.slice(1)}
			{@const usagePercentage = calculateUsagePercentage(balance.used, balance.total)}
			<Card variant="default" padding="lg">
				{#snippet children()}
					<div class="text-center">
						<div class="flex items-center justify-center mb-4">
							<div class="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
								<Calendar class="w-8 h-8 text-primary" />
							</div>
						</div>
						<h3 class="text-lg font-semibold text-foreground mb-2">{leaveType} Leave</h3>
						
						<!-- Remaining Days -->
						<div class="text-3xl font-bold text-foreground mb-1">{balance.remaining}</div>
						<div class="text-sm text-muted-foreground mb-4">days remaining</div>
						
						<!-- Progress Bar -->
						<div class="w-full bg-muted rounded-full h-2 mb-4">
							<div 
								class="bg-primary h-2 rounded-full transition-all duration-300"
								style="width: {usagePercentage}%"
							></div>
						</div>
						
						<!-- Usage Stats -->
						<div class="space-y-2 text-sm">
							<div class="flex justify-between">
								<span class="text-muted-foreground">Total:</span>
								<span class="font-medium text-foreground">{balance.total}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-muted-foreground">Used:</span>
								<span class="font-medium text-foreground">{balance.used}</span>
							</div>
							{#if balance.pending > 0}
								<div class="flex justify-between">
									<span class="text-muted-foreground">Pending:</span>
									<span class="font-medium text-yellow-600">{balance.pending}</span>
								</div>
							{/if}
						</div>
					</div>
				{/snippet}
			</Card>
		{/each}
	</div>

	<!-- Leave Accrual Information -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Leave Accrual Details</h2>
		{/snippet}
		{#snippet children()}
			<div class="overflow-x-auto">
				<table class="w-full">
					<thead>
						<tr class="border-b border-border">
							<th class="text-left py-3 px-4 font-medium text-foreground">Leave Type</th>
							<th class="text-left py-3 px-4 font-medium text-foreground">Annual Entitlement</th>
							<th class="text-left py-3 px-4 font-medium text-foreground">Accrued This Year</th>
							<th class="text-left py-3 px-4 font-medium text-foreground">Carried Forward</th>
							<th class="text-left py-3 px-4 font-medium text-foreground">Used</th>
							<th class="text-left py-3 px-4 font-medium text-foreground">Remaining</th>
						</tr>
					</thead>
					<tbody>
						{#each Object.entries(leaveBalance) as [key, balance]}
							{@const leaveType = key.charAt(0).toUpperCase() + key.slice(1)}
							<tr class="border-b border-border hover:bg-muted/50 transition-colors">
								<td class="py-3 px-4 font-medium text-foreground">{leaveType} Leave</td>
								<td class="py-3 px-4 text-muted-foreground">{balance.total}</td>
								<td class="py-3 px-4 text-muted-foreground">{balance.accrued_this_year}</td>
								<td class="py-3 px-4 text-muted-foreground">{balance.carried_forward}</td>
								<td class="py-3 px-4 text-muted-foreground">{balance.used}</td>
								<td class="py-3 px-4 font-medium text-foreground">{balance.remaining}</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/snippet}
	</Card>

	<!-- Leave History -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<div class="flex items-center justify-between">
				<h2 class="text-xl font-semibold text-foreground">Recent Leave History</h2>
				<Button variant="tertiary" size="sm">
					{#snippet children()}
						<FileText class="w-4 h-4" />
						View All
					{/snippet}
				</Button>
			</div>
		{/snippet}
		{#snippet children()}
			<div class="space-y-4">
				{#each leaveHistory as leave}
					<div class="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center gap-3 mb-2">
									<Clock class="w-5 h-5 {getLeaveTypeColor(leave.type)}" />
									<div>
										<h3 class="font-medium text-foreground">{leave.type}</h3>
										<p class="text-sm text-muted-foreground">
											{new Date(leave.start_date).toLocaleDateString()} - 
											{new Date(leave.end_date).toLocaleDateString()}
											({leave.days} {leave.days === 1 ? 'day' : 'days'})
										</p>
									</div>
								</div>
								<p class="text-sm text-muted-foreground">{leave.reason}</p>
							</div>
							<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(leave.status)}">
								{leave.status}
							</span>
						</div>
					</div>
				{/each}
			</div>
		{/snippet}
	</Card>

	<!-- Leave Policy Summary -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Leave Policy Summary</h2>
		{/snippet}
		{#snippet children()}
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div>
					<h3 class="font-medium text-foreground mb-3">Annual Leave</h3>
					<ul class="text-sm text-muted-foreground space-y-1">
						<li>• 21 days per year for full-time employees</li>
						<li>• Can carry forward up to 5 days to next year</li>
						<li>• Must be approved by line manager</li>
						<li>• Minimum 2 weeks notice required</li>
					</ul>
				</div>
				<div>
					<h3 class="font-medium text-foreground mb-3">Sick Leave</h3>
					<ul class="text-sm text-muted-foreground space-y-1">
						<li>• 10 days per year</li>
						<li>• Medical certificate required for 3+ days</li>
						<li>• Cannot be carried forward</li>
						<li>• Immediate notification required</li>
					</ul>
				</div>
				<div>
					<h3 class="font-medium text-foreground mb-3">Personal Leave</h3>
					<ul class="text-sm text-muted-foreground space-y-1">
						<li>• 5 days per year</li>
						<li>• For personal emergencies and matters</li>
						<li>• Manager approval required</li>
						<li>• Cannot be carried forward</li>
					</ul>
				</div>
				<div>
					<h3 class="font-medium text-foreground mb-3">Maternity Leave</h3>
					<ul class="text-sm text-muted-foreground space-y-1">
						<li>• 90 days for maternity</li>
						<li>• 10 days for paternity</li>
						<li>• Medical certificate required</li>
						<li>• 4 weeks notice required</li>
					</ul>
				</div>
			</div>
		{/snippet}
	</Card>
</div>
