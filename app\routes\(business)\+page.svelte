<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';

	const auth = $derived($authStore);
	const employee = $derived(auth.employee);
	const user = $derived(auth.user);

	const welcomeMessage = $derived(employee
		? `Welcome back, ${employee.first_name}!`
		: 'Welcome to HRIMS');

	const roleDisplayName = $derived(user?.user_role?.replace('_', ' ').toUpperCase() || 'EMPLOYEE');

	// Mock KPI data
	const kpiData = [
		{ label: 'Total Employees', value: '247', change: '+12', trend: 'up' },
		{ label: 'Active Contracts', value: '231', change: '+5', trend: 'up' },
		{ label: 'Pending Leave', value: '8', change: '-3', trend: 'down' },
		{ label: 'Open Positions', value: '12', change: '+2', trend: 'up' }
	];

	// Mock recent activities
	const recentActivities = [
		{ type: 'leave', message: '<PERSON> applied for annual leave', time: '2 hours ago' },
		{ type: 'employee', message: 'New employee <PERSON> <PERSON> onboarded', time: '4 hours ago' },
		{ type: 'contract', message: 'Contract renewal for Mike Johnson', time: '1 day ago' },
		{ type: 'recruitment', message: 'New job requisition for IT Department', time: '2 days ago' }
	];
</script>

<svelte:head>
	<title>Dashboard - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Welcome Header -->
	<div class="bg-gradient-to-r from-primary via-accent to-secondary rounded-lg p-6 text-primary-foreground shadow-lg">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-2xl font-bold mb-2">{welcomeMessage}</h1>
				<p class="text-primary-foreground/90">Role: {roleDisplayName}</p>
				{#if employee}
					<p class="text-primary-foreground/80 text-sm">Employee ID: {employee.employee_number} | Department: {employee.position}</p>
				{/if}
			</div>
			<div class="hidden md:block">
				<div class="w-20 h-20 bg-primary-foreground/20 rounded-full flex items-center justify-center">
					<span class="text-3xl font-bold text-primary-foreground">
						{employee?.first_name?.charAt(0) || 'U'}
					</span>
				</div>
			</div>
		</div>
	</div>

	<!-- KPI Cards -->
	{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
			{#each kpiData as kpi}
				<Card variant="kpi" padding="md">
					{#snippet children()}
						<div class="text-center">
							<div class="text-3xl font-bold text-foreground mb-2">{kpi.value}</div>
							<div class="text-sm text-muted-foreground mb-2">{kpi.label}</div>
							<div class="flex items-center justify-center gap-1 text-xs {kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'}">
								<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									{#if kpi.trend === 'up'}
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 17l9.2-9.2M17 17V7H7" />
									{:else}
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 7l-9.2 9.2M7 7v10h10" />
									{/if}
								</svg>
								<span>{kpi.change}</span>
							</div>
						</div>
					{/snippet}
				</Card>
			{/each}
		</div>
	{/if}

	<!-- Quick Actions -->
	<Card variant="elevated" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Quick Actions</h2>
		{/snippet}
		{#snippet children()}
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{#if user?.user_role === 'employee'}
					<Button variant="primary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
							</svg>
							Apply for Leave
						{/snippet}
					</Button>
					<Button variant="secondary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
							</svg>
							Update Profile
						{/snippet}
					</Button>
					<Button variant="tertiary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
							</svg>
							View Documents
						{/snippet}
					</Button>
				{:else}
					<Button variant="primary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
							</svg>
							Manage Employees
						{/snippet}
					</Button>
					<Button variant="secondary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
							</svg>
							Review Leave Requests
						{/snippet}
					</Button>
					<Button variant="tertiary" size="lg" fullWidth>
						{#snippet children()}
							<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
							</svg>
							Recruitment
						{/snippet}
					</Button>
				{/if}
			</div>
		{/snippet}
	</Card>

	<!-- Recent Activities -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">Recent Activities</h2>
		{/snippet}
		{#snippet children()}
			<div class="space-y-4">
				{#each recentActivities as activity}
					<div class="flex items-start gap-3 p-3 rounded-lg hover:bg-muted transition-colors duration-200">
						<div class="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
						<div class="flex-1">
							<p class="text-sm text-foreground">{activity.message}</p>
							<p class="text-xs text-muted-foreground mt-1">{activity.time}</p>
						</div>
					</div>
				{/each}
			</div>
		{/snippet}
	</Card>
</div>
