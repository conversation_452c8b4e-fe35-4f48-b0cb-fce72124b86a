{"meta": {"name": "Industrial Relations Style Guide", "file": "style-guide.industrial-relations.json", "version": "1.0.0", "description": "Incident reporting, confidential case UI, investigator workflows and case timelines using RTG palette + gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"incidentBg": "#FFF7ED", "caseOpen": "#F59E0B", "caseInvestigating": "#8C6239", "caseResolved": "#10B981", "confidentialShield": "#3B2A1A", "evidenceThumbBg": "#FCF8F2"}, "gradients": {"caseHeader": "linear-gradient(135deg,#8C6239 0%,#3B2A1A 100%)", "incidentTag": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}, "components": {"incidentForm": {"structuredFields": ["incident_date", "location", "witnesses", "summary", "attachments"], "attachmentDropzone": {"bg": "#FCF8F2", "confidentialFlag": {"bg": "#3B2A1A", "text": "#FFFFFF"}}}, "caseCard": {"statusPill": {"open": "#F59E0B", "investigating": "#8C6239", "resolved": "#10B981"}, "confidentialIcon": {"bg": "#3B2A1A", "text": "#FFFFFF"}}, "investigatorPanel": {"timelineEvent": {"bg": "#FFFFFF", "border": "1px solid #EDE0CF"}, "evidenceThumb": {"bg": "#FCF8F2", "size": 120}}}, "interactions": {"caseAssignment": {"autoNotify": true, "escalation": true}, "evidenceUpload": {"quarantineIfSuspect": true}}, "accessibility": {"confidentialData": "Restrict display with role gating; provide clear locked UI to non-authorized roles"}, "notes": {"mocks": ["/api/ir/cases", "/api/ir/investigations"], "acceptance": ["Incident form with confidential attachments", "Timeline that records events and investigator notes"]}}