<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { ArrowLeft, ArrowRight } from '@lucide/svelte';

	interface Props {
		title: string;
		subtitle?: string;
		showBackButton?: boolean;
		showNextButton?: boolean;
		nextButtonText?: string;
		nextButtonDisabled?: boolean;
		onBack?: () => void;
		onNext?: () => void;
		children: import('svelte').Snippet;
		actions?: import('svelte').Snippet;
	}

	let {
		title,
		subtitle,
		showBackButton = false,
		showNextButton = true,
		nextButtonText = 'Next',
		nextButtonDisabled = false,
		onBack,
		onNext,
		children,
		actions
	}: Props = $props();
</script>

<div class="onboarding-content flex flex-col h-full">
	<!-- Header -->
	<div class="mb-8">
		<h1 class="text-2xl font-semibold text-[#1C1C1C] mb-2">{title}</h1>
		{#if subtitle}
			<p class="text-[#8A6A52] text-base leading-relaxed">{subtitle}</p>
		{/if}
	</div>

	<!-- Content -->
	<div class="flex-1 mb-8">
		{@render children()}
	</div>

	<!-- Actions -->
	<div class="flex items-center justify-between pt-6 border-t border-[#EDE0CF]">
		<div>
			{#if showBackButton}
				<Button
					variant="ghost"
					size="default"
					onclick={onBack}
					class="text-[#8A6A52] hover:text-[#5C4024] hover:bg-[#FCF8F2]"
				>
					<ArrowLeft class="w-4 h-4 mr-2" />
					Back
				</Button>
			{/if}
		</div>

		<div class="flex items-center gap-3">
			{#if actions}
				{@render actions()}
			{/if}
			
			{#if showNextButton}
				<button
					class="onboarding-button-primary flex items-center gap-2 px-6 py-3 text-sm font-medium"
					onclick={onNext}
					disabled={nextButtonDisabled}
				>
					{nextButtonText}
					<ArrowRight class="w-4 h-4" />
				</button>
			{/if}
		</div>
	</div>
</div>

<style>
	.onboarding-content {
		animation: slideInUp 420ms cubic-bezier(0.22, 0.95, 0.26, 1);
	}

	@keyframes slideInUp {
		from {
			opacity: 0;
			transform: translateY(12px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@media (prefers-reduced-motion: reduce) {
		.onboarding-content {
			animation: none;
		}
	}
</style>
