Nice — below is a complete, production-ready **frontend-first** implementation pack (in **Markdown**) that an AI code editor + engineer can pick up and start implementing immediately. I searched current, authoritative docs and best-practice guides and cite them for the most important decisions. The output contains:

* recommended **tech stack** (centred on your SvelteKit/Tailwind/Supabase preferences) and rationale (citations),
* **high-level project siteflow** (pages & UX),
* **complete, optimised DB schema** and relationships (SQL already validated earlier — included here for reference),
* **RLS & security notes** (how the front-end will map to claims & auth),
* **separate markdown "files"** (one per module) with step-by-step **workflows** and UI requirements (each module is its own markdown section so your AI code editor can create files directly),
* **exact frontend-only development plan** (order of implementation, what to stub/mocks, acceptance criteria, test plan),
* **complete folder structure** (max depth) tuned for SvelteKit + modular features so the AI editor won't hallucinate files,
* **Edge Function examples / integration notes** (kept for later backend work but included so front-end can wire to endpoints later),
* **how to make the project model-agnostic for AI code editing** (supporting GPT-5 or Claude 4 / other LLMs), with prompts and dev best-practices and citations,
* final **step-by-step process** that the AI code editor should follow to generate the frontend (component + route + test + design tokens).

I. Quick top-level citations (most important sources I used)

* SvelteKit project structure & recommendations. ([Svelte][1])
* Supabase Row Level Security (RLS) guidance (why use RLS early). ([Supabase][2])
* Supabase Storage `createSignedUrl()` docs (how signed URLs work & permissions). ([Supabase][3])
* Supabase Edge Functions guide (server-side tasks / file-scan hooks). ([Supabase][4])
* Tailwind + SvelteKit integration & Tailwind best practices. ([Tailwind CSS][5], [UXPin][6])

---

# OUTPUT: Repository of Markdown "files" for AI code editor (copy each block into a separate `.md` file)

> Note: Files are named and ordered so an AI code editor can dump them into a repo `docs/` folder. The front-end-first rule is respected: each module file describes the UI components, routes, states, mocked API contracts, test cases, and acceptance criteria the frontend must satisfy before backend work begins.

---

# I. Read 00_readme.md for project context & overview

---

# II. Database schema & RLS (for backend later — included so frontend can map fields)

> Use the exact SQL migration we produced earlier (run later in Supabase migration). Save as `migrations/001_create_hrims_schema.sql`.
> Frontend must use these shapes for mock objects (ids, field names, enums).

**Short schema summary (entities & relationships):**

* `organizations` 1 — \* `departments`
* `departments` 1 — \* `employees`
* `employees` 1 — \* `contracts`
* `employees` 1 — \* `documents`
* `contracts` 1 — 0/1 `documents` (contract file)
* `documents` has status lifecycle (pending\_scan → clean | infected | quarantined | archived)

(Full SQL file is the same as the migration we gave earlier; front-end teams should seed mock objects matching it.)

Cite RLS rationale: implement RLS early. ([Supabase][2])

---

# III. Folder structure (max depth) — copy this exactly into the repo root

This is tuned for SvelteKit + a modular design. Depth stops at functional levels so AI editor won't hallucinate infinite files.

```
/ (repo root)
├─ package.json
├─ svelte.config.js
├─ tailwind.config.js
├─ tsconfig.json
├─ vite.config.ts
├─ README.md
├─ app/
│  ├─ app.html
│  ├─ global.d.ts
│  ├─ lib/
│  │  ├─ api/
│  │  │  ├─ index.ts          # typed API adapter (MSW or real)
│  │  │  └─ contracts.ts
│  │  ├─ components/
│  │  │  ├─ AppShell.svelte
│  │  │  ├─ TopNav.svelte
│  │  │  ├─ SideNav.svelte
│  │  │  ├─ Card.svelte
│  │  │  └─ ui/               # small primitives (Button, Input, Modal)
│  │  ├─ stores/
│  │  │  ├─ authStore.ts
│  │  │  ├─ uiStore.ts
│  │  │  └─ mockStore.ts
│  │  ├─ types/
│  │  │  └─ hr.ts             # types that mirror DB schema
│  │  ├─ forms/
│  │  │  └─ schemas.ts        # zod schemas
│  │  ├─ styles/              # design system & RTG tokens
│  │  │  ├─ global.css
│  │  │  └─ *.json            # RTG design token files
│  │  └─ utils/
│  │     └─ date.ts
│  ├─ routes/
│  │  ├─ +layout.svelte
│  │  ├─ +page.svelte         # dashboard
│  │  ├─ auth/
│  │  │  ├─ login/+page.svelte
│  │  │  └─ forgot/+page.svelte
│  │  ├─ employees/
│  │  │  ├─ +page.svelte
│  │  │  └─ [id]/+page.svelte
│  │  ├─ requisitions/+page.svelte
│  │  ├─ leave/
│  │  │  ├─ apply/+page.svelte
│  │  │  └─ balance/+page.svelte
│  │  ├─ contracts/
│  │  │  ├─ +page.svelte
│  │  │  └─ create/+page.svelte
│  │  └─ ...                  # rest of module routes (follow module files)
│  └─ assets/
│     └─ images/
├─ static/                    # static assets served at root
├─ tests/
│  ├─ unit/
│  └─ e2e/
├─ mocks/
│  ├─ msw/
│  │  ├─ handlers.ts
│  │  └─ seed.ts
│  └─ README.md
└─ scripts/
   ├─ seed-mocks.ts
   └─ dev-setup.sh
```

Notes:

* Keep depth to 4–5 levels for predictability.
* All module routes under `app/routes/` map exactly to the module markdown files above.
* Design system tokens are stored in `app/lib/styles/` for easy import via `$lib/styles/`

SvelteKit uses `app/lib` for shareable code with custom configuration. ([Svelte][1])

---

# IV. API contract (frontend mocks → backend)

Place `app/lib/api/index.ts` with typed functions; example signatures:

```ts
// app/lib/api/index.ts (interface only)
export type Employee = { id: string; first_name: string; last_name: string; employee_number: string; status: string; department_id?: string; auth_user_id?: string; };
export async function getEmployees(): Promise<Employee[]>;
export async function getEmployee(id: string): Promise<Employee | null>;
export async function uploadDocument(file: File, metadata: any): Promise<{ storage_path: string, status: 'pending_scan' }>;
export async function requestSignedUrl(bucket: string, path: string): Promise<{ url: string }>;
```

MSW handlers should implement these endpoints; later these functions will call Supabase or Edge Functions.

---

# V. Frontend-first dev checklist (step-by-step for AI code editor to implement)

1. **Scaffold project**

   * `npm create svelte@latest my-hrims` (choose TypeScript) — follow SvelteKit docs. ([Svelte][1])
   * `npm install -D tailwindcss postcss autoprefixer`
   * `npx tailwindcss init -p` and follow Tailwind SvelteKit guide. ([Tailwind CSS][5])

2. **Add tooling**

   * Add `zod`, `sveltekit-superforms`, `msw`, `vitest`, `playwright`, `eslint`, `prettier`.
   * Configure `husky` & `lint-staged` for precommits.

3. **Create global UI primitives**

   * Implement `AppShell`, `TopNav`, `SideNav`, `Card`, `Modal`.
   * Create CSS variables in `tailwind.config.cjs` for brand colours; set default mobile-first breakpoints. ([UXPin][6])

4. **Implement stores & mocks**

   * `authStore` with role switcher.
   * `mockStore` seeded via `mocks/seed.ts`.
   * MSW handlers for all `api/*` routes.

5. **File upload widget**

   * Implement `FileUpload` component with:

     * file-size limits,
     * accept mime types,
     * client-side preview,
     * upload progress and temporary storage to `mock/uploads-temp`,
     * show "pending\_scan" status returned from mock API.
   * Validate with EICAR test in later backend stage.

6. **Implement Module UIs in order**

   * Use the sprint order in `99_frontend_plan.md`. For each module:

     * create routes,
     * implement forms and components,
     * wire to `api/*` functions,
     * add local tests and one end-to-end for the flow.

7. **Mock signed-url logic**

   * Implement `api.requestSignedUrl` to return a short-lived blob URL from the browser or a base64 mock for download.

8. **Accessibility & visual polish**

   * Run axe & Lighthouse, fix high priority a11y issues.

9. **CI & deploy**

   * Add GitHub Actions: test, lint, build.
   * Deploy site to Vercel/Cloudflare Pages for staging.

10. **Handoff for backend**

    * Produce an `openapi.json` or typed `src/lib/api/index.ts` to hand to backend devs (Edge Functions & Supabase) to implement endpoints.

---

# VI. AI code editor + model integration (GPT-5 / Claude 4 / Model-agnostic)

Goal: allow the AI code editor (GPT-5 or Claude 4) to generate files, tests, and PRs reliably. Design the system to be model-agnostic (so you can swap GPT-5, Claude 4, or others).

Best-practices & rationale:

* Build a **unified "LLM adapter" layer** which maps your higher-level “intent” calls into vendor-specific API calls (routing, token limits, cost-based routing). See model-agnostic architecture guidance. ([Pieces][7], [Towards AI][8])
* Keep instructions & prompts **deterministic** and include *code context windows*—file-level + repository index + `CLAUDE.md` or `README` style guidelines for model behavior (Anthropic recommends `CLAUDE.md` patterns). ([Reddit][9])
* Follow OpenAI production best practices for prompt safety, rate limits, retry & caching. ([OpenAI Platform][10])
* Provide the AI editor a single canonical API to make changes and run tests (e.g., a "workspace API"): `apply_patch(files: [{path, content}], run_tests: boolean)`.

Integration plan (for AI editor):

1. Provide the AI with the **full repository index** (file list + content or file heads).
2. Use **function-calling** or a controlled plugin to allow the AI to:

   * create files,
   * run local tests,
   * open PRs (git operations),
   * request clarifying prompts only when necessary.
3. For code generation tasks, use *few-shot examples* and incorporate `safety` instructions (never commit secrets, avoid external calls) — follow OpenAI/Anthropic guidance. ([OpenAI Help Center][11], [Anthropic][12])

Suggested minimal prompts for the AI editor to create a component:

* Role: "You are Lyra, an expert SvelteKit engineer. Create a Svelte component at `src/lib/components/FileUpload.svelte` that ..."
* Provide constraints: TypeScript, Tailwind classes only, 50–80 lines, add unit tests in `tests/unit/`.

Model routing:

* For heavy code-synthesis tasks use a high-capacity model (GPT-5 or Claude Opus/ Sonnet 4) and fallback to smaller cheaper models for linting or smaller edits. Use the adapter to route tasks by estimated token cost. See model-agnostic guides. ([Pieces][7])

Security & compliance:

* Do not provide the AI model with service keys or secrets. Provide only code context and test results.
* Add guard rails: static analysis, unit tests, and human-in-loop approvals for PR merge.

---

# VII. Example prompt templates (for AI code editor)

**Create FileUpload component — prompt**

```
You are an expert SvelteKit + Tailwind engineer.
Task: create `src/lib/components/FileUpload.svelte` (TypeScript) with:
- drag/drop,
- mime/type + size validation rules (5MB for images, 10MB for documents),
- accessible buttons, progress bar, and upload to `api.uploadDocument(file, metadata)` (mock),
- return uploaded file object with `storage_path` and `status`.
Output: full file content only. Add `tests/unit/FileUpload.test.ts` that asserts validation logic.
```

(Use this pattern for other components.)

**Generate route & page — prompt**

```
You are a SvelteKit dev.
Task: create route `/leave/apply` with page + server load that uses `LeaveForm.svelte`.
Mock API: `api.createLeave(payload)` returns `{id, status}`.
Add Playwright scenario: apply leave and expect success toast.
```

---

# VIII. Final checklist for the AI editor before handing to backend devs

* [ ] All pages + components exist and pass unit tests.
* [ ] All flows simulated end-to-end with Playwright.
* [ ] `src/lib/api/index.ts` typed functions documented.
* [ ] Mock server (MSW) seeded and `mocks/seed.ts` included.
* [ ] Accessibility audit done (axe) and major issues fixed.
* [ ] CI runs tests on PRs.
* [ ] README describes how to swap MSW to real Supabase endpoints and environment variable names.

---

# IX. Useful links & references (again)

* SvelteKit project structure & docs. ([Svelte][1])
* Supabase RLS & security guidance. ([Supabase][2])
* Supabase Storage createSignedUrl doc. ([Supabase][3])
* Supabase Edge Functions guide. ([Supabase][4])
* Tailwind + SvelteKit integration & best practices. ([Tailwind CSS][5], [UXPin][6])
* Drizzle vs Prisma (decision reference). ([Better Stack][13])
* ClamAV / serverless scanning examples & patterns. ([Amazon Web Services, Inc.][14], [DEV Community][15])
* Model-agnostic AI / Claude + OpenAI best-practices. ([Pieces][7], [OpenAI Platform][10], [Anthropic][12])

---

# X. Next steps — what I can generate right now (choose one)

* A. **Full starter SvelteKit repo** (files + msw + components skeleton) zipped ready to run.
* B. **One module scaffolded fully** (e.g., Employee Lifecycle) with components, pages, tests and MSW handlers.
* C. **AI Editor adapter example** (node service that proxies to OpenAI/Anthropic with routing logic + sample prompts).
* D. **Edge Function scaffolds** for `file-scan` and `signed-url` (ready for deploy) (we made examples earlier; I can produce ready-to-deploy zip).
