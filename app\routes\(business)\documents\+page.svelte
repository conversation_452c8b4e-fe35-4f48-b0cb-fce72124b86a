<script lang="ts">
	import { onMount } from 'svelte';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import { Download, Upload, FileText, Eye, Calendar, User, CheckCircle, Clock, XCircle } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';

	interface Document {
		id: string;
		name: string;
		type: string;
		category: 'personal' | 'contract' | 'payroll' | 'benefits' | 'training';
		uploadedBy: string;
		uploadedAt: string;
		size: string;
		status: 'approved' | 'pending' | 'rejected';
		downloadUrl?: string;
		description?: string;
	}

	// Mock document data
	let documents: Document[] = [
		{
			id: '1',
			name: 'Employment Contract.pdf',
			type: 'PDF',
			category: 'contract',
			uploadedBy: 'HR Department',
			uploadedAt: '2024-01-15',
			size: '2.4 MB',
			status: 'approved',
			downloadUrl: '/mock/employment-contract.pdf',
			description: 'Official employment contract document'
		},
		{
			id: '2',
			name: 'ID Copy.pdf',
			type: 'PDF',
			category: 'personal',
			uploadedBy: 'John Doe',
			uploadedAt: '2024-01-10',
			size: '1.2 MB',
			status: 'approved',
			downloadUrl: '/mock/id-copy.pdf',
			description: 'National ID document copy'
		},
		{
			id: '3',
			name: 'Qualification Certificate.pdf',
			type: 'PDF',
			category: 'personal',
			uploadedBy: 'John Doe',
			uploadedAt: '2024-02-01',
			size: '3.1 MB',
			status: 'pending',
			description: 'University degree certificate'
		},
		{
			id: '4',
			name: 'Medical Certificate.pdf',
			type: 'PDF',
			category: 'benefits',
			uploadedBy: 'John Doe',
			uploadedAt: '2024-02-15',
			size: '0.8 MB',
			status: 'approved',
			downloadUrl: '/mock/medical-cert.pdf',
			description: 'Annual medical examination certificate'
		},
		{
			id: '5',
			name: 'Training Certificate - Safety.pdf',
			type: 'PDF',
			category: 'training',
			uploadedBy: 'Training Department',
			uploadedAt: '2024-03-01',
			size: '1.5 MB',
			status: 'approved',
			downloadUrl: '/mock/safety-training.pdf',
			description: 'Workplace safety training completion certificate'
		}
	];

	let filteredDocuments = $state(documents);
	let selectedCategory = $state<string>('all');
	let searchQuery = $state('');
	let isUploadDialogOpen = $state(false);
	let uploadFile: File | null = $state(null);
	let uploadDescription = $state('');
	let uploadCategory = $state<Document['category']>('personal');

	// Filter documents based on category and search
	$effect(() => {
		filteredDocuments = documents.filter(doc => {
			const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory;
			const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
								doc.description?.toLowerCase().includes(searchQuery.toLowerCase());
			return matchesCategory && matchesSearch;
		});
	});

	const categories = [
		{ value: 'all', label: 'All Documents', count: documents.length },
		{ value: 'personal', label: 'Personal', count: documents.filter(d => d.category === 'personal').length },
		{ value: 'contract', label: 'Contract', count: documents.filter(d => d.category === 'contract').length },
		{ value: 'payroll', label: 'Payroll', count: documents.filter(d => d.category === 'payroll').length },
		{ value: 'benefits', label: 'Benefits', count: documents.filter(d => d.category === 'benefits').length },
		{ value: 'training', label: 'Training', count: documents.filter(d => d.category === 'training').length }
	];

	const getStatusIcon = (status: Document['status']) => {
		switch (status) {
			case 'approved': return CheckCircle;
			case 'pending': return Clock;
			case 'rejected': return XCircle;
		}
	};

	const getStatusColor = (status: Document['status']) => {
		switch (status) {
			case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
			case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
			case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
		}
	};

	const getCategoryColor = (category: Document['category']) => {
		switch (category) {
			case 'personal': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
			case 'contract': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
			case 'payroll': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
			case 'benefits': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
			case 'training': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
		}
	};

	const handleDownload = async (document: Document) => {
		if (!document.downloadUrl) {
			notificationStore.error('Download Error', 'This document is not available for download yet.');
			return;
		}

		try {
			// Mock download functionality
			notificationStore.success('Download Started', `Downloading ${document.name}...`);
			
			// In a real implementation, this would request a signed URL from the backend
			// For now, we'll just show a success message
			setTimeout(() => {
				notificationStore.info('Download Complete', `${document.name} has been downloaded successfully.`);
			}, 2000);
		} catch (error) {
			notificationStore.error('Download Failed', 'Failed to download the document. Please try again.');
		}
	};

	const handleFileUpload = (event: Event) => {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files[0]) {
			uploadFile = target.files[0];
		}
	};

	const handleUploadSubmit = async () => {
		if (!uploadFile) {
			notificationStore.warning('Upload Error', 'Please select a file to upload.');
			return;
		}

		try {
			// Mock upload functionality
			const newDocument: Document = {
				id: Date.now().toString(),
				name: uploadFile.name,
				type: uploadFile.type.split('/')[1].toUpperCase(),
				category: uploadCategory,
				uploadedBy: 'John Doe',
				uploadedAt: new Date().toISOString().split('T')[0],
				size: `${(uploadFile.size / 1024 / 1024).toFixed(1)} MB`,
				status: 'pending',
				description: uploadDescription
			};

			documents = [...documents, newDocument];
			
			notificationStore.success('Upload Successful', `${uploadFile.name} has been uploaded and is pending approval.`);
			
			// Reset form
			uploadFile = null;
			uploadDescription = '';
			uploadCategory = 'personal';
			isUploadDialogOpen = false;
		} catch (error) {
			notificationStore.error('Upload Failed', 'Failed to upload the document. Please try again.');
		}
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};
</script>

<svelte:head>
	<title>My Documents - HRIMS</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-2xl font-bold text-foreground">My Documents</h1>
			<p class="text-muted-foreground">Manage your personal and work-related documents</p>
		</div>
		<Button onclick={() => isUploadDialogOpen = true} class="flex items-center gap-2">
			<Upload class="w-4 h-4" />
			Upload Document
		</Button>
	</div>

	<!-- Filters and Search -->
	<div class="flex flex-col lg:flex-row gap-4">
		<!-- Category Filter -->
		<div class="flex flex-wrap gap-2">
			{#each categories as category}
				<Button
					variant={selectedCategory === category.value ? 'default' : 'outline'}
					size="sm"
					onclick={() => selectedCategory = category.value}
					class="flex items-center gap-2"
				>
					{category.label}
					<Badge variant="secondary" class="text-xs">
						{category.count}
					</Badge>
				</Button>
			{/each}
		</div>

		<!-- Search -->
		<div class="flex-1 max-w-md">
			<Input
				type="search"
				placeholder="Search documents..."
				bind:value={searchQuery}
				class="w-full"
			/>
		</div>
	</div>

	<!-- Documents Grid -->
	<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
		{#each filteredDocuments as document (document.id)}
			<Card class="hover:shadow-md transition-shadow">
				<CardHeader class="pb-3">
					<div class="flex items-start justify-between">
						<div class="flex items-center gap-2">
							<FileText class="w-5 h-5 text-muted-foreground" />
							<div class="flex flex-col">
								<CardTitle class="text-sm font-medium line-clamp-1">
									{document.name}
								</CardTitle>
								<CardDescription class="text-xs">
									{document.type} • {document.size}
								</CardDescription>
							</div>
						</div>
						<div class="flex flex-col gap-1">
							<Badge class={getStatusColor(document.status)} variant="secondary">
								{@const StatusIcon = getStatusIcon(document.status)}
								<StatusIcon class="w-3 h-3 mr-1" />
								{document.status}
							</Badge>
						</div>
					</div>
				</CardHeader>
				<CardContent class="space-y-3">
					<div class="flex items-center gap-2">
						<Badge class={getCategoryColor(document.category)} variant="secondary">
							{document.category}
						</Badge>
					</div>
					
					{#if document.description}
						<p class="text-sm text-muted-foreground line-clamp-2">
							{document.description}
						</p>
					{/if}

					<div class="flex items-center gap-2 text-xs text-muted-foreground">
						<User class="w-3 h-3" />
						<span>{document.uploadedBy}</span>
						<Calendar class="w-3 h-3 ml-2" />
						<span>{formatDate(document.uploadedAt)}</span>
					</div>

					<div class="flex gap-2 pt-2">
						{#if document.downloadUrl}
							<Button
								size="sm"
								variant="outline"
								onclick={() => handleDownload(document)}
								class="flex-1 flex items-center gap-2"
							>
								<Download class="w-3 h-3" />
								Download
							</Button>
						{:else}
							<Button
								size="sm"
								variant="outline"
								disabled
								class="flex-1 flex items-center gap-2"
							>
								<Clock class="w-3 h-3" />
								Pending
							</Button>
						{/if}
						<Button
							size="sm"
							variant="ghost"
							onclick={() => notificationStore.info('Preview', `Preview functionality for ${document.name} will be available soon.`)}
						>
							<Eye class="w-3 h-3" />
						</Button>
					</div>
				</CardContent>
			</Card>
		{/each}
	</div>

	{#if filteredDocuments.length === 0}
		<div class="text-center py-12">
			<FileText class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-medium text-muted-foreground mb-2">No documents found</h3>
			<p class="text-sm text-muted-foreground">
				{searchQuery || selectedCategory !== 'all' 
					? 'Try adjusting your search or filter criteria.' 
					: 'Upload your first document to get started.'}
			</p>
		</div>
	{/if}
</div>

<!-- Upload Dialog -->
<Dialog.Root bind:open={isUploadDialogOpen}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title>Upload Document</Dialog.Title>
			<Dialog.Description>
				Upload a new document to your personal document library.
			</Dialog.Description>
		</Dialog.Header>
		<div class="space-y-4">
			<div class="space-y-2">
				<Label for="file">Select File</Label>
				<Input
					id="file"
					type="file"
					accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
					onchange={handleFileUpload}
				/>
				{#if uploadFile}
					<p class="text-sm text-muted-foreground">
						Selected: {uploadFile.name} ({(uploadFile.size / 1024 / 1024).toFixed(1)} MB)
					</p>
				{/if}
			</div>
			
			<div class="space-y-2">
				<Label for="category">Category</Label>
				<select
					id="category"
					bind:value={uploadCategory}
					class="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
				>
					<option value="personal">Personal</option>
					<option value="contract">Contract</option>
					<option value="payroll">Payroll</option>
					<option value="benefits">Benefits</option>
					<option value="training">Training</option>
				</select>
			</div>

			<div class="space-y-2">
				<Label for="description">Description (Optional)</Label>
				<Input
					id="description"
					placeholder="Brief description of the document..."
					bind:value={uploadDescription}
				/>
			</div>
		</div>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => isUploadDialogOpen = false}>
				Cancel
			</Button>
			<Button onclick={handleUploadSubmit} disabled={!uploadFile}>
				Upload
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
	
	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
