# 01 — Foundation / Cross-cutting UI (Frontend-only)

Purpose:
- Create the UI shell, auth UI mocks, roles switcher (to simulate RBAC), document upload widget, and notification center.
- These components will be used across all modules.

Pages / routes (SvelteKit):
- `/` — Dashboard shell (role-aware placeholder widgets)
- `/auth/login` — Login UI (mocked; supports role selection)
- `/auth/forgot` — Forgot password (UI only)
- `/profile` — Profile view/edit (local changes only)
- `/admin/roles` — Role switcher & RBAC simulator (dev-only route)

Components:
- `AppShell.svelte` — header, nav, logout, role badge, notification bell.
- `TopNav.svelte`, `SideNav.svelte` — responsive.
- `Card.svelte` — base card used across dashboards.
- `FileUpload.svelte` — secure upload widget (accepts mime types, previews, drag/drop, client-side size checks).
- `NotificationList.svelte` — stores toast / in-app notifications.
- `Modal.svelte` — accessible modal with focus trap.

State management:
- Use Svelte stores in `app/lib/stores/`:
  - `authStore` — mocked user info `{id, email, user_role, org_id}` stored in localStorage for dev.
  - `uiStore` — theme, mobile nav open, toasts.
  - `mockApi` — simple adapter to route mocked API calls (see `mocks/` below).

Mocking strategy:
- Create `mocks/msw/handlers.ts` (MSW handlers). For each endpoint front-end uses (e.g., `/api/employees`, `/api/documents`), return deterministic mock payloads shaped to match the DB schema.
- Provide `mocks/seed.ts` with fake org, departments, 10 employees, sample contracts, sample documents (clean, pending_scan, quarantined).

Acceptance criteria (frontend-only):
- App shell renders and changes based on `authStore.user_role`.
- File upload widget validates files and submits to mock `/api/uploads` endpoint; shows progress and success/failure UI.
- Notification center receives mock push events (simulate via timeout).
- All components are responsive and accessible (a11y checks with axe during automated tests).

Dev notes:
- Use `zod` for client-side schema validation of important forms.
- Use `sveltekit-superforms` or custom validation for consistent UX and server-style errors.
