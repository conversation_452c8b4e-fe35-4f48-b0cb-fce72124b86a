<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { goto } from '$app/navigation';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { Calendar, ArrowLeft, Send } from '@lucide/svelte';

	const auth = $derived($authStore);
	const employee = $derived(auth.employee);

	// Form state
	let isSubmitting = $state(false);
	let formData = $state({
		leave_type: 'annual',
		start_date: '',
		end_date: '',
		reason: '',
		emergency_contact: ''
	});

	const leaveTypes = [
		{ value: 'annual', label: 'Annual Leave', description: 'Vacation and personal time off' },
		{ value: 'sick', label: 'Sick Leave', description: 'Medical appointments and illness' },
		{ value: 'personal', label: 'Personal Leave', description: 'Personal matters and emergencies' },
		{ value: 'maternity', label: 'Maternity Leave', description: 'Maternity and paternity leave' },
		{ value: 'bereavement', label: 'Bereavement Leave', description: 'Time off for family loss' }
	];

	const selectedLeaveType = $derived(
		leaveTypes.find(type => type.value === formData.leave_type) || leaveTypes[0]
	);

	// Calculate working days between dates
	const calculateWorkingDays = (startDate: string, endDate: string): number => {
		if (!startDate || !endDate) return 0;
		
		const start = new Date(startDate);
		const end = new Date(endDate);
		let workingDays = 0;
		
		for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
			const dayOfWeek = date.getDay();
			if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
				workingDays++;
			}
		}
		
		return workingDays;
	};

	const workingDays = $derived(calculateWorkingDays(formData.start_date, formData.end_date));

	const handleSubmit = async (event: Event) => {
		event.preventDefault();
		if (!formData.start_date || !formData.end_date || !formData.reason.trim()) {
			alert('Please fill in all required fields');
			return;
		}

		if (new Date(formData.start_date) > new Date(formData.end_date)) {
			alert('End date must be after start date');
			return;
		}

		isSubmitting = true;
		
		// Simulate API call
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		console.log('Submitting leave application:', {
			...formData,
			employee_id: employee?.id,
			working_days: workingDays
		});
		
		isSubmitting = false;
		
		// Redirect back to leave management
		goto('/leave');
	};

	const handleBack = () => {
		goto('/leave');
	};
</script>

<svelte:head>
	<title>Apply for Leave - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center gap-4">
		<Button variant="tertiary" size="sm" onclick={handleBack}>
			{#snippet children()}
				<ArrowLeft class="w-4 h-4" />
				Back
			{/snippet}
		</Button>
		<div>
			<h1 class="text-2xl font-bold text-foreground">Apply for Leave</h1>
			<p class="text-muted-foreground">Submit a new leave request</p>
		</div>
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Leave Application Form -->
		<div class="lg:col-span-2">
			<Card variant="default" padding="lg">
				{#snippet header()}
					<h2 class="text-xl font-semibold text-foreground">Leave Details</h2>
				{/snippet}
				{#snippet children()}
					<form class="space-y-6" onsubmit={handleSubmit}>
						<!-- Leave Type Selection -->
						<fieldset>
							<legend class="block text-sm font-medium text-foreground mb-3">Leave Type</legend>
							<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
								{#each leaveTypes as type}
									<label class="relative">
										<input
											type="radio"
											bind:group={formData.leave_type}
											value={type.value}
											class="sr-only"
										/>
										<div class="border border-border rounded-lg p-4 cursor-pointer transition-all hover:bg-muted/50 {formData.leave_type === type.value ? 'border-primary bg-primary/5' : ''}">
											<div class="font-medium text-foreground">{type.label}</div>
											<div class="text-sm text-muted-foreground mt-1">{type.description}</div>
										</div>
									</label>
								{/each}
							</div>
						</fieldset>

						<!-- Date Selection -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<Input
								label="Start Date"
								type="date"
								bind:value={formData.start_date}
								required
							/>
							<Input
								label="End Date"
								type="date"
								bind:value={formData.end_date}
								required
							/>
						</div>

						<!-- Working Days Display -->
						{#if workingDays > 0}
							<div class="bg-muted/50 rounded-lg p-4">
								<div class="flex items-center gap-2">
									<Calendar class="w-5 h-5 text-primary" />
									<span class="font-medium text-foreground">
										Total Working Days: {workingDays}
									</span>
								</div>
							</div>
						{/if}

						<!-- Reason -->
						<div>
							<label for="reason" class="block text-sm font-medium text-foreground mb-2">
								Reason for Leave *
							</label>
							<textarea
								id="reason"
								bind:value={formData.reason}
								rows="4"
								class="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary resize-none"
								placeholder="Please provide a brief reason for your leave request..."
								required
							></textarea>
						</div>

						<!-- Emergency Contact -->
						<Input
							label="Emergency Contact (Optional)"
							bind:value={formData.emergency_contact}
							placeholder="Name and phone number of emergency contact"
						/>

						<!-- Submit Button -->
						<div class="flex justify-end gap-3">
							<Button variant="secondary" onclick={handleBack}>
								{#snippet children()}
									Cancel
								{/snippet}
							</Button>
							<Button variant="primary" type="submit" loading={isSubmitting}>
								{#snippet children()}
									<Send class="w-4 h-4" />
									Submit Application
								{/snippet}
							</Button>
						</div>
					</form>
				{/snippet}
			</Card>
		</div>

		<!-- Leave Balance Sidebar -->
		<div class="lg:col-span-1">
			<Card variant="default" padding="lg">
				{#snippet header()}
					<h3 class="text-lg font-semibold text-foreground">Your Leave Balance</h3>
				{/snippet}
				{#snippet children()}
					<div class="space-y-4">
						<div class="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
							<div>
								<div class="font-medium text-foreground">Annual Leave</div>
								<div class="text-sm text-muted-foreground">Remaining</div>
							</div>
							<div class="text-2xl font-bold text-foreground">13</div>
						</div>
						<div class="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
							<div>
								<div class="font-medium text-foreground">Sick Leave</div>
								<div class="text-sm text-muted-foreground">Remaining</div>
							</div>
							<div class="text-2xl font-bold text-foreground">8</div>
						</div>
						<div class="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
							<div>
								<div class="font-medium text-foreground">Personal Leave</div>
								<div class="text-sm text-muted-foreground">Remaining</div>
							</div>
							<div class="text-2xl font-bold text-foreground">4</div>
						</div>
					</div>
				{/snippet}
			</Card>

			<!-- Leave Policy Info -->
			<Card variant="default" padding="md" class="mt-6">
				{#snippet header()}
					<h4 class="text-md font-semibold text-foreground">Leave Policy</h4>
				{/snippet}
				{#snippet children()}
					<div class="text-sm text-muted-foreground space-y-2">
						<p>• Leave requests should be submitted at least 2 weeks in advance</p>
						<p>• Annual leave must be approved by your line manager</p>
						<p>• Sick leave requires medical certificate for 3+ days</p>
						<p>• Emergency leave may be granted at manager's discretion</p>
					</div>
				{/snippet}
			</Card>
		</div>
	</div>
</div>
