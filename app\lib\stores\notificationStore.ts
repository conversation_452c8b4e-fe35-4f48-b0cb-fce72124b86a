import { writable } from 'svelte/store';

// Enhanced notification interface with all required features
export interface EnhancedNotification {
	id: string;
	type: 'success' | 'error' | 'warning' | 'info';
	title: string;
	message?: string;
	duration?: number; // in milliseconds, 0 for persistent
	priority?: 'low' | 'normal' | 'high' | 'critical';
	timestamp: number;
	persistent?: boolean; // Whether to persist across page reloads
	action?: {
		label: string;
		onClick: () => void;
	};
	richContent?: {
		html?: string;
		component?: any;
		props?: Record<string, any>;
	};
	position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
	dismissible?: boolean;
	showProgress?: boolean;
	onDismiss?: () => void;
	onShow?: () => void;
}

// Configuration for the notification system
interface NotificationConfig {
	maxNotifications: number;
	defaultDuration: number;
	defaultPosition: EnhancedNotification['position'];
	enablePersistence: boolean;
	enableSounds: boolean;
	enableAnimations: boolean;
}

// Default configuration
const defaultConfig: NotificationConfig = {
	maxNotifications: 5,
	defaultDuration: 5000,
	defaultPosition: 'bottom-right',
	enablePersistence: false,
	enableSounds: false,
	enableAnimations: true
};

// Store for notifications
const { subscribe, update, set } = writable<EnhancedNotification[]>([]);

// Store for configuration
const configStore = writable<NotificationConfig>(defaultConfig);

// Generate unique IDs
const generateId = (): string => {
	return `notification-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// Priority order for sorting (higher number = higher priority)
const priorityOrder = {
	low: 1,
	normal: 2,
	high: 3,
	critical: 4
};



// Enhanced notification store
export const notificationStore = {
	subscribe,
	config: configStore,

	// Add a new notification with enhanced features
	add: (notification: Omit<EnhancedNotification, 'id' | 'timestamp'>) => {
		const id = generateId();
		const timestamp = Date.now();

		update(notifications => {
			// Get current config
			let config: NotificationConfig;
			configStore.subscribe(c => config = c)();

			// Create new notification with defaults
			const newNotification: EnhancedNotification = {
				id,
				timestamp,
				duration: config.defaultDuration,
				priority: 'normal',
				position: config.defaultPosition,
				dismissible: true,
				showProgress: true,
				...notification
			};

			// Add to notifications array
			let updatedNotifications = [...notifications, newNotification];

			// Sort by priority (critical first) and timestamp (newest first)
			updatedNotifications.sort((a, b) => {
				const priorityDiff = priorityOrder[b.priority || 'normal'] - priorityOrder[a.priority || 'normal'];
				if (priorityDiff !== 0) return priorityDiff;
				return b.timestamp - a.timestamp;
			});

			// Limit to max notifications
			if (updatedNotifications.length > config.maxNotifications) {
				// Remove oldest low-priority notifications first
				const lowPriorityNotifications = updatedNotifications.filter(n => n.priority === 'low');
				if (lowPriorityNotifications.length > 0) {
					const toRemove = lowPriorityNotifications[lowPriorityNotifications.length - 1];
					updatedNotifications = updatedNotifications.filter(n => n.id !== toRemove.id);
				} else {
					updatedNotifications = updatedNotifications.slice(0, config.maxNotifications);
				}
			}

			// Call onShow callback if provided
			if (newNotification.onShow) {
				setTimeout(() => newNotification.onShow?.(), 0);
			}

			// Persist if enabled and notification is marked as persistent
			if (config.enablePersistence && newNotification.persistent) {
				try {
					const persistedNotifications = JSON.parse(localStorage.getItem('hrims-notifications') || '[]');
					persistedNotifications.push({
						...newNotification,
						// Remove functions for serialization
						action: newNotification.action ? { label: newNotification.action.label } : undefined,
						onDismiss: undefined,
						onShow: undefined
					});
					localStorage.setItem('hrims-notifications', JSON.stringify(persistedNotifications));
				} catch (error) {
					console.warn('Failed to persist notification:', error);
				}
			}

			return updatedNotifications;
		});

		return id;
	},

	// Remove a notification by ID
	remove: (id: string) => {
		update(notifications => {
			const notification = notifications.find(n => n.id === id);
			if (notification?.onDismiss) {
				notification.onDismiss();
			}
			return notifications.filter(n => n.id !== id);
		});
	},

	// Remove notifications by type
	removeByType: (type: EnhancedNotification['type']) => {
		update(notifications => notifications.filter(n => n.type !== type));
	},

	// Remove notifications by priority
	removeByPriority: (priority: EnhancedNotification['priority']) => {
		update(notifications => notifications.filter(n => n.priority !== priority));
	},

	// Clear all notifications
	clear: () => {
		update(notifications => {
			// Call onDismiss for all notifications
			notifications.forEach(n => n.onDismiss?.());
			return [];
		});
	},

	// Update notification configuration
	updateConfig: (newConfig: Partial<NotificationConfig>) => {
		configStore.update(config => ({ ...config, ...newConfig }));
	},

	// Get notification by ID
	getById: (id: string): EnhancedNotification | undefined => {
		let result: EnhancedNotification | undefined;
		subscribe(notifications => {
			result = notifications.find(n => n.id === id);
		})();
		return result;
	},

	// Get notifications by type
	getByType: (type: EnhancedNotification['type']): EnhancedNotification[] => {
		let result: EnhancedNotification[] = [];
		subscribe(notifications => {
			result = notifications.filter(n => n.type === type);
		})();
		return result;
	},

	// Get notifications by priority
	getByPriority: (priority: EnhancedNotification['priority']): EnhancedNotification[] => {
		let result: EnhancedNotification[] = [];
		subscribe(notifications => {
			result = notifications.filter(n => n.priority === priority);
		})();
		return result;
	},

	// Load persisted notifications
	loadPersisted: () => {
		try {
			const persistedNotifications = JSON.parse(localStorage.getItem('hrims-notifications') || '[]');
			if (persistedNotifications.length > 0) {
				set(persistedNotifications.map((n: any) => ({
					...n,
					timestamp: Date.now() // Update timestamp for loaded notifications
				})));
				// Clear persisted notifications after loading
				localStorage.removeItem('hrims-notifications');
			}
		} catch (error) {
			console.warn('Failed to load persisted notifications:', error);
		}
	},

	// Convenience methods for different types with enhanced options
	success: (title: string, message?: string, options?: Partial<EnhancedNotification>) => {
		return notificationStore.add({
			type: 'success',
			title,
			message,
			priority: 'normal',
			...options
		});
	},

	error: (title: string, message?: string, options?: Partial<EnhancedNotification>) => {
		return notificationStore.add({
			type: 'error',
			title,
			message,
			duration: 0, // Errors persist until manually closed
			priority: 'high',
			...options
		});
	},

	warning: (title: string, message?: string, options?: Partial<EnhancedNotification>) => {
		return notificationStore.add({
			type: 'warning',
			title,
			message,
			priority: 'normal',
			...options
		});
	},

	info: (title: string, message?: string, options?: Partial<EnhancedNotification>) => {
		return notificationStore.add({
			type: 'info',
			title,
			message,
			priority: 'low',
			...options
		});
	},

	// Critical notifications (always shown, high priority)
	critical: (title: string, message?: string, options?: Partial<EnhancedNotification>) => {
		return notificationStore.add({
			type: 'error',
			title,
			message,
			duration: 0,
			priority: 'critical',
			dismissible: false,
			persistent: true,
			...options
		});
	},

	// Quick notification methods
	quickSuccess: (message: string) => notificationStore.success('Success', message),
	quickError: (message: string) => notificationStore.error('Error', message),
	quickWarning: (message: string) => notificationStore.warning('Warning', message),
	quickInfo: (message: string) => notificationStore.info('Info', message),

	// Batch operations
	addMultiple: (notifications: Array<Omit<EnhancedNotification, 'id' | 'timestamp'>>) => {
		const ids: string[] = [];
		notifications.forEach(notification => {
			ids.push(notificationStore.add(notification));
		});
		return ids;
	},

	// Remove multiple notifications
	removeMultiple: (ids: string[]) => {
		ids.forEach(id => notificationStore.remove(id));
	}
};
