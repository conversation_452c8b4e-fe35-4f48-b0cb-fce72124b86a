<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { Calendar, Plus, Clock, CheckCircle, XCircle, AlertCircle } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const employee = $derived(auth.employee);

	// Mock leave data
	const leaveRequests = [
		{
			id: 1,
			employee_name: '<PERSON>',
			employee_id: 'RTG001',
			leave_type: 'Annual Leave',
			start_date: '2024-02-15',
			end_date: '2024-02-20',
			days: 6,
			status: 'Pending',
			reason: 'Family vacation',
			applied_date: '2024-01-20'
		},
		{
			id: 2,
			employee_name: '<PERSON>',
			employee_id: 'RTG002',
			leave_type: 'Sick Leave',
			start_date: '2024-01-25',
			end_date: '2024-01-26',
			days: 2,
			status: 'Approved',
			reason: 'Medical appointment',
			applied_date: '2024-01-24'
		},
		{
			id: 3,
			employee_name: '<PERSON>',
			employee_id: 'RTG003',
			leave_type: 'Personal Leave',
			start_date: '2024-03-01',
			end_date: '2024-03-03',
			days: 3,
			status: 'Rejected',
			reason: 'Personal matters',
			applied_date: '2024-02-10'
		}
	];

	// Leave balance data
	const leaveBalance = {
		annual: { total: 21, used: 8, remaining: 13 },
		sick: { total: 10, used: 2, remaining: 8 },
		personal: { total: 5, used: 1, remaining: 4 },
		maternity: { total: 90, used: 0, remaining: 90 }
	};

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'approved': return 'text-green-600 bg-green-100';
			case 'pending': return 'text-yellow-600 bg-yellow-100';
			case 'rejected': return 'text-red-600 bg-red-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	const getStatusIcon = (status: string) => {
		switch (status.toLowerCase()) {
			case 'approved': return CheckCircle;
			case 'pending': return AlertCircle;
			case 'rejected': return XCircle;
			default: return Clock;
		}
	};

	const handleApplyLeave = () => {
		// Navigate to leave application form
		console.log('Apply for leave');
	};

	const handleApproveLeave = (leaveId: number) => {
		console.log('Approve leave:', leaveId);
	};

	const handleRejectLeave = (leaveId: number) => {
		console.log('Reject leave:', leaveId);
	};

	// Filter requests based on user role
	const filteredRequests = $derived(
		user?.user_role === 'employee' 
			? leaveRequests.filter(req => req.employee_id === employee?.employee_number)
			: leaveRequests
	);
</script>

<svelte:head>
	<title>Leave Management - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Leave Management</h1>
			<p class="text-muted-foreground">
				{user?.user_role === 'employee' ? 'Manage your leave requests and balance' : 'Review and manage employee leave requests'}
			</p>
		</div>
		<Button variant="primary" onclick={handleApplyLeave}>
			{#snippet children()}
				<Plus class="w-4 h-4" />
				Apply for Leave
			{/snippet}
		</Button>
	</div>

	{#if user?.user_role === 'employee'}
		<!-- Leave Balance Cards -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">{leaveBalance.annual.remaining}</div>
						<div class="text-sm text-muted-foreground mb-2">Annual Leave</div>
						<div class="text-xs text-muted-foreground">
							{leaveBalance.annual.used} used of {leaveBalance.annual.total}
						</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">{leaveBalance.sick.remaining}</div>
						<div class="text-sm text-muted-foreground mb-2">Sick Leave</div>
						<div class="text-xs text-muted-foreground">
							{leaveBalance.sick.used} used of {leaveBalance.sick.total}
						</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">{leaveBalance.personal.remaining}</div>
						<div class="text-sm text-muted-foreground mb-2">Personal Leave</div>
						<div class="text-xs text-muted-foreground">
							{leaveBalance.personal.used} used of {leaveBalance.personal.total}
						</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">{leaveBalance.maternity.remaining}</div>
						<div class="text-sm text-muted-foreground mb-2">Maternity Leave</div>
						<div class="text-xs text-muted-foreground">
							{leaveBalance.maternity.used} used of {leaveBalance.maternity.total}
						</div>
					</div>
				{/snippet}
			</Card>
		</div>
	{:else}
		<!-- Leave Statistics for Managers -->
		<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-yellow-600 mb-1">
							{leaveRequests.filter(req => req.status === 'Pending').length}
						</div>
						<div class="text-sm text-muted-foreground">Pending Requests</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-green-600 mb-1">
							{leaveRequests.filter(req => req.status === 'Approved').length}
						</div>
						<div class="text-sm text-muted-foreground">Approved Today</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">
							{leaveRequests.reduce((sum, req) => sum + req.days, 0)}
						</div>
						<div class="text-sm text-muted-foreground">Total Days Requested</div>
					</div>
				{/snippet}
			</Card>
			<Card variant="kpi" padding="md">
				{#snippet children()}
					<div class="text-center">
						<div class="text-2xl font-bold text-foreground mb-1">12</div>
						<div class="text-sm text-muted-foreground">Employees on Leave</div>
					</div>
				{/snippet}
			</Card>
		</div>
	{/if}

	<!-- Leave Requests -->
	<Card variant="default" padding="lg">
		{#snippet header()}
			<h2 class="text-xl font-semibold text-foreground">
				{user?.user_role === 'employee' ? 'My Leave Requests' : 'Leave Requests'}
			</h2>
		{/snippet}
		{#snippet children()}
			<div class="space-y-4">
				{#each filteredRequests as request}
					{@const StatusIcon = getStatusIcon(request.status)}
					<div class="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center gap-3 mb-2">
									<StatusIcon class="w-5 h-5 text-muted-foreground" />
									<div>
										<h3 class="font-medium text-foreground">
											{request.leave_type}
											{#if user?.user_role !== 'employee'}
												- {request.employee_name}
											{/if}
										</h3>
										<p class="text-sm text-muted-foreground">
											{new Date(request.start_date).toLocaleDateString()} -
											{new Date(request.end_date).toLocaleDateString()}
											({request.days} days)
										</p>
									</div>
								</div>
								<p class="text-sm text-muted-foreground mb-2">{request.reason}</p>
								<p class="text-xs text-muted-foreground">
									Applied on {new Date(request.applied_date).toLocaleDateString()}
								</p>
							</div>
							<div class="flex items-center gap-3">
								<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(request.status)}">
									{request.status}
								</span>
								{#if user?.user_role !== 'employee' && request.status === 'Pending'}
									<div class="flex gap-2">
										<Button 
											variant="primary" 
											size="sm"
											onclick={() => handleApproveLeave(request.id)}
										>
											{#snippet children()}
												Approve
											{/snippet}
										</Button>
										<Button 
											variant="danger" 
											size="sm"
											onclick={() => handleRejectLeave(request.id)}
										>
											{#snippet children()}
												Reject
											{/snippet}
										</Button>
									</div>
								{/if}
							</div>
						</div>
					</div>
				{/each}

				{#if filteredRequests.length === 0}
					<div class="text-center py-8">
						<Calendar class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
						<h3 class="text-lg font-medium text-foreground mb-2">No leave requests</h3>
						<p class="text-muted-foreground">
							{user?.user_role === 'employee' 
								? "You haven't submitted any leave requests yet." 
								: "No leave requests to review at this time."}
						</p>
					</div>
				{/if}
			</div>
		{/snippet}
	</Card>
</div>
