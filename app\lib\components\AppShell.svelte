<script lang="ts">
	import type { User, Employee } from '$lib/types/hr';
	import TopNav from './TopNav.svelte';
	import SideNav from './SideNav.svelte';

	interface Props {
		user: User | null;
		employee: Employee | null;
		onLogout: () => void;
		children: import('svelte').Snippet;
	}

	let { user, employee, onLogout, children }: Props = $props();

	let sidebarOpen = $state(false);

	const toggleSidebar = () => {
		sidebarOpen = !sidebarOpen;
	};

	const closeSidebar = () => {
		sidebarOpen = false;
	};
</script>

<div class="bg-background flex flex-col">
	

	<!-- Main Layout Container -->
	<div class="flex flex-1 overflow-hidden h-screen">
		<!-- Side Navigation -->
		<SideNav isOpen={sidebarOpen} {user} onClose={closeSidebar} />

		<!-- Main Content -->
		<main class="flex-1 overflow-auto h-screen">
			<!-- Top Navigation -->
			<TopNav {user} {employee} onToggleSidebar={toggleSidebar} {onLogout} />
			<div class="p-4 lg:p-6">
				{@render children()}
			</div>
		</main>
	</div>
</div>
