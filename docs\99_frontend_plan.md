# Frontend Implementation Plan — Frontend-first (ordered)

## Goals for frontend-first:
- Build 100% of UI, components, routes, client validation, flows, and tests without a backend.
- Use MSW (Mock Service Worker) + seeded JSON to simulate backend responses.
- Prepare the UI to be swapped to real backend endpoints with minimal changes (API layer + env endpoints).

## Order of implementation (modules + sprints)
Sprint 0 — Setup:
- Project skeleton: SvelteKit + Tailwind + TypeScript + Vitest + Playwright.
- Install & configure tailwind per docs. :contentReference[oaicite:9]{index=9}
- Set up MSW mocks + seed data.
- Implement `AppShell`, `authStore`, `role switcher` (dev-only).

Sprint 1 — Core UX / Employee CRUD & Contracts:
- Implement Employee list/detail UI, Contract Wizard, Document Upload widget (with client-side validation).
- Build FileUpload UI & mock `/api/uploads` (simulate statuses: pending, clean, infected).

Sprint 2 — Leave & ESS:
- Implement Leave form, Calendar, ESS profile & payslips (download via mock signed-url).

Sprint 3 — Recruitment & Onboarding:
- Requisition flows, candidate list, interview scheduling, onboarding checklist.

Sprint 4 — Performance & Training:
- Goal setting, mid-cycle check-ins, 360 reviewer flows, training catalog & enrollment.

Sprint 5 — Talent, Wellness, IR, Resourcing, Analytics, Offboarding:
- Nine-box matrix, wellness signup, incident reports, headcount dashboards, offboarding.

Sprint 6 — Polish & Accessibility:
- Run axe checks, keyboard navigation, color contrast. Integrate Sentry in staging.

## Frontend API contract (example patterns)
- All API calls go through `app/lib/api/index.ts` which exports typed functions:
  - `api.getEmployees()`, `api.createLeave(payload)`, `api.uploadDocument(file)`, `api.getSignedUrl(bucket, path)`.
- MSW should match these endpoints, and later these functions will be re-pointed to your Supabase Edge Functions or Supabase REST endpoints.

## Testing plan
- Unit tests for every component with Vitest,
- Integration tests for module flows,
- End-to-end Playwright scenarios for the key user journeys listed in module docs.

## Deployment (staging)
- Deploy frontend to Vercel or Cloudflare Pages (both support SvelteKit) — choose Vercel for DX or Cloudflare for strict edge. (Both are common choices). :contentReference[oaicite:10]{index=10}

## Delivery checklist (before backend):
- All pages implemented (routes + components),
- Mock API fully seeded and documented (openapi.json not required but recommended),
- Tests passing,
- README with how to switch from mock to real backend.
