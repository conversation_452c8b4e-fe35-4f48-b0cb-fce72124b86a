{"meta": {"name": "Business Resourcing Style Guide", "file": "style-guide.business-resourcing.json", "version": "1.0.0", "description": "Headcount dashboard, vacancy forecasting, scenario planner and resourcing charts styled with RTG colors/gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"headcountDonut": "#C49A6C", "vacancyPill": "#FFF7ED", "forecastHire": "#8C6239", "forecastTransfer": "#C49A6C", "scenarioAccent": "#F5D6A1"}, "gradients": {"forecastBg": "linear-gradient(180deg,#F9F5F0 0%,#FFF7ED 100%)", "scenarioAccent": "linear-gradient(135deg,#F5D6A1 0%,#8C6239 100%)"}, "components": {"headcountCard": {"donutColors": ["#C49A6C", "#8C6239", "#F5D6A1"], "metaText": "#5C4024"}, "vacancyList": {"rowHeight": 64, "pill": {"bg": "#FFF7ED", "text": "#8C6239"}}, "scenarioPlanner": {"slider": {"fill": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}, "resultCard": {"bg": "#FFFFFF", "border": "1px solid #EDE0CF"}}}, "interactions": {"plannerUpdate": {"liveUpdate": true, "animateChart": true}}, "notes": {"mocks": ["/api/resourcing/headcount", "/api/resourcing/forecasts"], "acceptance": ["Scenario slider updates chart (mocked)", "Vacancy list connects to requisition flow"]}}