{"meta": {"name": "Employee Self-Service (ESS) Style Guide", "file": "style-guide.ess.json", "version": "1.0.0", "description": "ESS tokens for profile, payslips, policies and documents using RTG palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"primary": "#F5D6A1", "payslipBadge": "#C49A6C", "policyBg": "#FFF7ED", "docClean": "#10B981", "docPending": "#F59E0B", "docRisk": "#EF4444"}, "gradients": {"payslipGradient": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "policyCard": "linear-gradient(180deg,#FFF7ED 0%,#FCF8F2 100%)"}, "components": {"profileForm": {"avatar": {"size": 56, "border": "2px solid #FFFFFF"}, "editableFieldBadge": {"bg": "#FFF7ED", "text": "#8C6239"}, "saveButton": {"bg": "#8C6239", "text": "#FFFFFF"}}, "payslipList": {"row": {"bg": "#FFFFFF", "border": "1px solid #EDE0CF"}, "downloadBtn": {"bg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "text": "#1C1C1C"}}, "policyAcknowledgement": {"card": {"bg": "#FFF7ED", "radius": 10}, "ackButton": {"bg": "#C49A6C", "text": "#1C1C1C"}}, "documentList": {"statusPills": {"pending": "#F59E0B", "clean": "#10B981", "infected": "#EF4444"}}}, "interactions": {"signedUrlDownload": {"ttlSec": 60, "progressIndicator": true}, "profileChangeApproval": {"stateFlow": ["pending", "approved", "rejected"], "notify": true}}, "accessibility": {"documentPreview": "Provide alt text and keyboard nav"}, "notes": {"mocks": ["/api/profile", "/api/payslips", "/api/policies", "/api/documents"], "acceptance": ["Payslip downloads via mock signed url", "Profile edits create pending approval change"]}}