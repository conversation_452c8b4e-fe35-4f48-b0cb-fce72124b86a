<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import { CheckCircle, Clock, XCircle, AlertTriangle, User, Calendar, MessageSquare } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';

	interface ProfileChangeRequest {
		id: string;
		field: string;
		fieldLabel: string;
		currentValue: string;
		requestedValue: string;
		reason: string;
		status: 'pending' | 'approved' | 'rejected';
		requestedAt: string;
		reviewedAt?: string;
		reviewedBy?: string;
		reviewComments?: string;
	}

	interface Props {
		employee: {
			id: string;
			firstName: string;
			lastName: string;
			email: string;
			phone: string;
			address: string;
			emergencyContact: string;
			emergencyPhone: string;
		};
		isEditing: boolean;
		onSave: (data: any) => void;
		onCancel: () => void;
	}

	let { employee, isEditing, onSave, onCancel }: Props = $props();

	// Form data
	let formData = $state({
		firstName: employee.firstName,
		lastName: employee.lastName,
		email: employee.email,
		phone: employee.phone,
		address: employee.address,
		emergencyContact: employee.emergencyContact,
		emergencyPhone: employee.emergencyPhone
	});

	// Change requests (mock data)
	let changeRequests: ProfileChangeRequest[] = $state([
		{
			id: '1',
			field: 'phone',
			fieldLabel: 'Phone Number',
			currentValue: '+****************',
			requestedValue: '+****************',
			reason: 'Changed mobile provider and got new number',
			status: 'pending',
			requestedAt: '2024-03-15'
		},
		{
			id: '2',
			field: 'address',
			fieldLabel: 'Address',
			currentValue: '123 Main St, Anytown, ST 12345',
			requestedValue: '456 Oak Ave, Newtown, ST 67890',
			reason: 'Relocated to new apartment closer to office',
			status: 'approved',
			requestedAt: '2024-02-20',
			reviewedAt: '2024-02-22',
			reviewedBy: 'Sarah Johnson (HR)',
			reviewComments: 'Address change approved. Please update your tax withholding forms.'
		},
		{
			id: '3',
			field: 'emergencyContact',
			fieldLabel: 'Emergency Contact',
			currentValue: 'Jane Doe',
			requestedValue: 'Michael Smith',
			reason: 'Previous emergency contact is no longer available',
			status: 'rejected',
			requestedAt: '2024-01-10',
			reviewedAt: '2024-01-12',
			reviewedBy: 'Sarah Johnson (HR)',
			reviewComments: 'Please provide relationship and contact information for the new emergency contact.'
		}
	]);

	let isRequestDialogOpen = $state(false);
	let selectedField = $state('');
	let changeReason = $state('');
	let requestedValue = $state('');

	// Fields that require HR approval
	const approvalRequiredFields = ['email', 'phone', 'address', 'emergencyContact', 'emergencyPhone'];

	// Field labels
	const fieldLabels: Record<string, string> = {
		firstName: 'First Name',
		lastName: 'Last Name',
		email: 'Email Address',
		phone: 'Phone Number',
		address: 'Address',
		emergencyContact: 'Emergency Contact',
		emergencyPhone: 'Emergency Phone'
	};

	const getStatusColor = (status: ProfileChangeRequest['status']) => {
		switch (status) {
			case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
			case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
			case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
		}
	};

	const getStatusIcon = (status: ProfileChangeRequest['status']) => {
		switch (status) {
			case 'approved': return CheckCircle;
			case 'pending': return Clock;
			case 'rejected': return XCircle;
		}
	};

	const handleFieldChange = (field: string, value: string) => {
		if (approvalRequiredFields.includes(field) && value !== employee[field as keyof typeof employee]) {
			// Show request dialog for fields requiring approval
			selectedField = field;
			requestedValue = value;
			changeReason = '';
			isRequestDialogOpen = true;
		} else {
			// Direct update for fields not requiring approval
			formData[field as keyof typeof formData] = value;
		}
	};

	const handleSubmitRequest = async () => {
		if (!changeReason.trim()) {
			notificationStore.warning('Reason Required', 'Please provide a reason for this change request.');
			return;
		}

		try {
			const newRequest: ProfileChangeRequest = {
				id: Date.now().toString(),
				field: selectedField,
				fieldLabel: fieldLabels[selectedField],
				currentValue: employee[selectedField as keyof typeof employee],
				requestedValue: requestedValue,
				reason: changeReason,
				status: 'pending',
				requestedAt: new Date().toISOString().split('T')[0]
			};

			changeRequests = [newRequest, ...changeRequests];

			notificationStore.success(
				'Change Request Submitted',
				`Your request to change ${fieldLabels[selectedField]} has been submitted for HR approval.`,
				{
					action: {
						label: 'View Requests',
						onClick: () => notificationStore.info('Requests', 'Scroll down to view your pending change requests.')
					}
				}
			);

			isRequestDialogOpen = false;
			selectedField = '';
			requestedValue = '';
			changeReason = '';
		} catch (error) {
			notificationStore.error('Request Failed', 'Failed to submit change request. Please try again.');
		}
	};

	const handleSave = () => {
		// Only save fields that don't require approval or have been approved
		const updatedData = { ...formData };
		
		// Apply approved changes
		changeRequests.forEach(request => {
			if (request.status === 'approved') {
				updatedData[request.field as keyof typeof updatedData] = request.requestedValue;
			}
		});

		onSave(updatedData);
		
		notificationStore.success(
			'Profile Updated',
			'Your profile has been updated successfully. Changes requiring HR approval are still pending.'
		);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	// Count pending requests
	const pendingRequestsCount = changeRequests.filter(r => r.status === 'pending').length;
</script>

{#if isEditing}
	<div class="space-y-6">
		<!-- Profile Form -->
		<Card>
			<CardHeader>
				<CardTitle>Edit Profile Information</CardTitle>
				<CardDescription>
					Changes to certain fields require HR approval and may take 1-2 business days to process.
				</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div class="space-y-2">
						<Label for="firstName">First Name</Label>
						<Input
							id="firstName"
							bind:value={formData.firstName}
							placeholder="Enter first name"
						/>
					</div>
					<div class="space-y-2">
						<Label for="lastName">Last Name</Label>
						<Input
							id="lastName"
							bind:value={formData.lastName}
							placeholder="Enter last name"
						/>
					</div>
				</div>

				<div class="space-y-2">
					<Label for="email" class="flex items-center gap-2">
						Email Address
						<Badge variant="outline" class="text-xs">Requires Approval</Badge>
					</Label>
					<Input
						id="email"
						type="email"
						value={formData.email}
						placeholder="Enter email address"
						onchange={(e) => handleFieldChange('email', e.target.value)}
					/>
				</div>

				<div class="space-y-2">
					<Label for="phone" class="flex items-center gap-2">
						Phone Number
						<Badge variant="outline" class="text-xs">Requires Approval</Badge>
					</Label>
					<Input
						id="phone"
						type="tel"
						value={formData.phone}
						placeholder="Enter phone number"
						onchange={(e) => handleFieldChange('phone', e.target.value)}
					/>
				</div>

				<div class="space-y-2">
					<Label for="address" class="flex items-center gap-2">
						Address
						<Badge variant="outline" class="text-xs">Requires Approval</Badge>
					</Label>
					<Textarea
						id="address"
						value={formData.address}
						placeholder="Enter full address"
						onchange={(e) => handleFieldChange('address', e.target.value)}
					/>
				</div>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div class="space-y-2">
						<Label for="emergencyContact" class="flex items-center gap-2">
							Emergency Contact
							<Badge variant="outline" class="text-xs">Requires Approval</Badge>
						</Label>
						<Input
							id="emergencyContact"
							value={formData.emergencyContact}
							placeholder="Enter emergency contact name"
							onchange={(e) => handleFieldChange('emergencyContact', e.target.value)}
						/>
					</div>
					<div class="space-y-2">
						<Label for="emergencyPhone" class="flex items-center gap-2">
							Emergency Phone
							<Badge variant="outline" class="text-xs">Requires Approval</Badge>
						</Label>
						<Input
							id="emergencyPhone"
							type="tel"
							value={formData.emergencyPhone}
							placeholder="Enter emergency contact phone"
							onchange={(e) => handleFieldChange('emergencyPhone', e.target.value)}
						/>
					</div>
				</div>

				<div class="flex gap-2 pt-4">
					<Button onclick={handleSave}>Save Changes</Button>
					<Button variant="outline" onclick={onCancel}>Cancel</Button>
				</div>
			</CardContent>
		</Card>

		<!-- Change Requests -->
		{#if changeRequests.length > 0}
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						Change Requests
						{#if pendingRequestsCount > 0}
							<Badge variant="secondary">{pendingRequestsCount} Pending</Badge>
						{/if}
					</CardTitle>
					<CardDescription>
						Track the status of your profile change requests
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div class="space-y-4">
						{#each changeRequests as request (request.id)}
							<div class="border rounded-lg p-4">
								<div class="flex items-start justify-between gap-4">
									<div class="flex-1">
										<div class="flex items-center gap-2 mb-2">
											<h4 class="font-medium">{request.fieldLabel}</h4>
											<Badge class={getStatusColor(request.status)} variant="secondary">
												{@const StatusIcon = getStatusIcon(request.status)}
												<StatusIcon class="w-3 h-3 mr-1" />
												{request.status}
											</Badge>
										</div>
										
										<div class="space-y-2 text-sm">
											<div>
												<span class="text-muted-foreground">Current:</span>
												<span class="ml-2">{request.currentValue}</span>
											</div>
											<div>
												<span class="text-muted-foreground">Requested:</span>
												<span class="ml-2 font-medium">{request.requestedValue}</span>
											</div>
											<div>
												<span class="text-muted-foreground">Reason:</span>
												<span class="ml-2">{request.reason}</span>
											</div>
										</div>

										<div class="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
											<div class="flex items-center gap-1">
												<Calendar class="w-3 h-3" />
												<span>Requested: {formatDate(request.requestedAt)}</span>
											</div>
											{#if request.reviewedAt}
												<div class="flex items-center gap-1">
													<User class="w-3 h-3" />
													<span>Reviewed: {formatDate(request.reviewedAt)} by {request.reviewedBy}</span>
												</div>
											{/if}
										</div>

										{#if request.reviewComments}
											<div class="mt-3 p-3 bg-muted rounded-md">
												<div class="flex items-start gap-2">
													<MessageSquare class="w-4 h-4 text-muted-foreground mt-0.5" />
													<div>
														<p class="text-sm font-medium">Review Comments:</p>
														<p class="text-sm text-muted-foreground">{request.reviewComments}</p>
													</div>
												</div>
											</div>
										{/if}
									</div>
								</div>
							</div>
						{/each}
					</div>
				</CardContent>
			</Card>
		{/if}
	</div>
{/if}

<!-- Change Request Dialog -->
<Dialog.Root bind:open={isRequestDialogOpen}>
	<Dialog.Content class="sm:max-w-md">
		<Dialog.Header>
			<Dialog.Title>Request Profile Change</Dialog.Title>
			<Dialog.Description>
				This field requires HR approval. Please provide a reason for the change.
			</Dialog.Description>
		</Dialog.Header>
		<div class="space-y-4">
			<div class="space-y-2">
				<Label>Field</Label>
				<Input value={fieldLabels[selectedField]} disabled />
			</div>
			
			<div class="space-y-2">
				<Label>Current Value</Label>
				<Input value={employee[selectedField as keyof typeof employee]} disabled />
			</div>

			<div class="space-y-2">
				<Label>Requested Value</Label>
				<Input value={requestedValue} disabled />
			</div>

			<div class="space-y-2">
				<Label for="reason">Reason for Change *</Label>
				<Textarea
					id="reason"
					placeholder="Please explain why you need to make this change..."
					bind:value={changeReason}
					rows={3}
				/>
			</div>
		</div>
		<Dialog.Footer>
			<Button variant="outline" onclick={() => isRequestDialogOpen = false}>
				Cancel
			</Button>
			<Button onclick={handleSubmitRequest} disabled={!changeReason.trim()}>
				Submit Request
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>
