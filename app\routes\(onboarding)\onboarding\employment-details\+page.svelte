<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore, type EmploymentDetails } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const employmentDetails = $derived(store.data.employmentDetails || {});

	// Form state
	let formData = $state({
		jobTitle: employmentDetails.jobTitle || '',
		department: employmentDetails.department || '',
		manager: employmentDetails.manager || '',
		startDate: employmentDetails.startDate || '',
		employmentType: employmentDetails.employmentType || 'Permanent',
		workLocation: employmentDetails.workLocation || ''
	});

	// Validation state
	let errors = $state<Record<string, string>>({});

	// Options
	const employmentTypeOptions = [
		{ value: 'Permanent', label: 'Permanent' },
		{ value: 'Contractor', label: 'Contractor' },
		{ value: 'Temporary', label: 'Temporary' },
		{ value: 'Intern', label: 'Intern' }
	];

	const departmentOptions = [
		{ value: 'Human Resources', label: 'Human Resources' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Operations', label: 'Operations' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Sales', label: 'Sales' },
		{ value: 'IT', label: 'Information Technology' },
		{ value: 'Customer Service', label: 'Customer Service' },
		{ value: 'Administration', label: 'Administration' }
	];

	const managerOptions = [
		{ value: 'john.smith', label: 'John Smith - HR Manager' },
		{ value: 'sarah.jones', label: 'Sarah Jones - Finance Manager' },
		{ value: 'mike.wilson', label: 'Mike Wilson - Operations Manager' },
		{ value: 'lisa.brown', label: 'Lisa Brown - Marketing Manager' },
		{ value: 'david.taylor', label: 'David Taylor - Sales Manager' },
		{ value: 'emma.davis', label: 'Emma Davis - IT Manager' }
	];

	const locationOptions = [
		{ value: 'Harare Office', label: 'Harare Office' },
		{ value: 'Bulawayo Office', label: 'Bulawayo Office' },
		{ value: 'Victoria Falls Office', label: 'Victoria Falls Office' },
		{ value: 'Remote', label: 'Remote Work' },
		{ value: 'Hybrid', label: 'Hybrid (Office + Remote)' }
	];

	// Validation functions
	const validateStartDate = (date: string) => {
		const startDate = new Date(date);
		const today = new Date();
		const sevenDaysAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
		
		return startDate >= sevenDaysAgo;
	};

	// Form validation
	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		// Required fields
		if (!formData.jobTitle.trim()) newErrors.jobTitle = 'Job title is required';
		if (!formData.department.trim()) newErrors.department = 'Department is required';
		if (!formData.manager.trim()) newErrors.manager = 'Manager is required';
		if (!formData.startDate) newErrors.startDate = 'Start date is required';
		if (!formData.employmentType.trim()) newErrors.employmentType = 'Employment type is required';
		if (!formData.workLocation.trim()) newErrors.workLocation = 'Work location is required';

		// Date validation
		if (formData.startDate && !validateStartDate(formData.startDate)) {
			newErrors.startDate = 'Start date cannot be more than 7 days in the past';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Auto-save functionality
	let saveTimeout: NodeJS.Timeout;
	const autoSave = () => {
		clearTimeout(saveTimeout);
		saveTimeout = setTimeout(() => {
			onboardingStore.updateEmploymentDetails(formData);
		}, 1000);
	};

	// Handle input changes
	const handleInputChange = () => {
		autoSave();
	};

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		if (validateForm()) {
			onboardingStore.updateEmploymentDetails(formData);
			onboardingStore.completeStep('employment-details');
			onboardingStore.setCurrentStep('tax-details');
			notificationStore.add({
				type: 'success',
				message: 'Employment details saved successfully!'
			});
			goto('/onboarding/tax-details');
		} else {
			notificationStore.add({
				type: 'error',
				message: 'Please fix the errors below before continuing.'
			});
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/personal-details');
	};
</script>

<svelte:head>
	<title>Employment Details - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Employment Details"
		subtitle="Please provide your job information and work arrangements. This helps us set up your workspace and reporting structure."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		onBack={handleBack}
		onNext={handleNext}
	>
		<form class="space-y-8" on:submit|preventDefault={handleNext}>
			<!-- Job Information -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Job Information
				</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Job Title -->
					<div class="space-y-2">
						<Label for="jobTitle" class="text-sm font-medium text-[#5C4024]">
							Job Title <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="jobTitle"
							bind:value={formData.jobTitle}
							oninput={handleInputChange}
							placeholder="e.g. Marketing Specialist"
							class="onboarding-input {errors.jobTitle ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.jobTitle}
							aria-describedby={errors.jobTitle ? 'jobTitle-error' : undefined}
						/>
						{#if errors.jobTitle}
							<p id="jobTitle-error" class="text-xs text-[#EF4444]">{errors.jobTitle}</p>
						{/if}
					</div>

					<!-- Department -->
					<div class="space-y-2">
						<Label for="department" class="text-sm font-medium text-[#5C4024]">
							Department <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={departmentOptions}
							bind:value={formData.department}
							placeholder="Select your department"
							class="onboarding-input {errors.department ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.department}
							<p class="text-xs text-[#EF4444]">{errors.department}</p>
						{/if}
					</div>

					<!-- Manager -->
					<div class="space-y-2">
						<Label for="manager" class="text-sm font-medium text-[#5C4024]">
							Direct Manager <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={managerOptions}
							bind:value={formData.manager}
							placeholder="Select your manager"
							class="onboarding-input {errors.manager ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.manager}
							<p class="text-xs text-[#EF4444]">{errors.manager}</p>
						{/if}
					</div>

					<!-- Start Date -->
					<div class="space-y-2">
						<Label for="startDate" class="text-sm font-medium text-[#5C4024]">
							Start Date <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="startDate"
							type="date"
							bind:value={formData.startDate}
							oninput={handleInputChange}
							class="onboarding-input {errors.startDate ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.startDate}
							aria-describedby={errors.startDate ? 'startDate-error' : undefined}
						/>
						{#if errors.startDate}
							<p id="startDate-error" class="text-xs text-[#EF4444]">{errors.startDate}</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Employment Terms -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Employment Terms
				</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Employment Type -->
					<div class="space-y-2">
						<Label for="employmentType" class="text-sm font-medium text-[#5C4024]">
							Employment Type <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={employmentTypeOptions}
							bind:value={formData.employmentType}
							placeholder="Select employment type"
							class="onboarding-input {errors.employmentType ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.employmentType}
							<p class="text-xs text-[#EF4444]">{errors.employmentType}</p>
						{/if}
					</div>

					<!-- Work Location -->
					<div class="space-y-2">
						<Label for="workLocation" class="text-sm font-medium text-[#5C4024]">
							Work Location <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={locationOptions}
							bind:value={formData.workLocation}
							placeholder="Select work location"
							class="onboarding-input {errors.workLocation ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors.workLocation}
							<p class="text-xs text-[#EF4444]">{errors.workLocation}</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Auto-save indicator -->
			{#if store.lastSaved}
				<div class="text-xs text-[#8A6A52] text-center py-2">
					Last saved: {store.lastSaved.toLocaleTimeString()}
				</div>
			{/if}
		</form>
	</OnboardingCard>
</div>
