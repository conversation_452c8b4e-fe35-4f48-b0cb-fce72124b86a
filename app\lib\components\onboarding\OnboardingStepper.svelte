<script lang="ts">
	import { Check } from '@lucide/svelte';
	
	interface OnboardingStep {
		id: string;
		title: string;
		description?: string;
		status: 'upcoming' | 'active' | 'completed';
		required: boolean;
	}

	interface Props {
		steps: OnboardingStep[];
		currentStepId: string;
		onStepClick?: (stepId: string) => void;
	}

	let { steps, currentStepId, onStepClick }: Props = $props();

	const handleStepClick = (step: OnboardingStep) => {
		// Only allow navigation to completed steps or the current step
		if (step.status === 'completed' || step.id === currentStepId) {
			onStepClick?.(step.id);
		}
	};

	const getStepNumber = (index: number) => index + 1;
</script>

<div class="onboarding-stepper flex flex-col p-8">
	<!-- Header -->
	<div class="mb-8">
		<div class="flex items-center gap-3 mb-2">
			<div class="w-10 h-10 bg-gradient-to-br from-[#F5D6A1] to-[#C49A6C] rounded-2xl flex items-center justify-center shadow-lg">
				<span class="text-[#1C1C1C] font-bold text-lg">RTG</span>
			</div>
			<div>
				<h1 class="text-lg font-semibold text-[#1C1C1C]">Rainbow Tourism Group</h1>
				<p class="text-sm text-[#8A6A52]">Employee Onboarding</p>
			</div>
		</div>
	</div>

	<!-- Progress Overview -->
	<div class="mb-8">
		{#if steps.length > 0}
			{@const completedSteps = steps.filter(s => s.status === 'completed').length}
			{@const totalSteps = steps.length}
			{@const progressPercent = (completedSteps / totalSteps) * 100}

			<div class="mb-3">
				<div class="flex justify-between text-sm mb-1">
					<span class="text-[#5C4024] font-medium">Progress</span>
					<span class="text-[#8A6A52]">{completedSteps}/{totalSteps}</span>
				</div>
				<div class="w-full bg-[#F0E1CB] rounded-full h-2">
					<div
						class="bg-gradient-to-r from-[#F5D6A1] to-[#C49A6C] h-2 rounded-full transition-all duration-500 ease-out"
						style="width: {progressPercent}%"
					></div>
				</div>
			</div>
		{/if}
	</div>

	<!-- Steps List -->
	<div class="flex-1">
		<nav class="space-y-1" aria-label="Onboarding progress">
			{#each steps as step, index}
				{@const stepNumber = getStepNumber(index)}
				{@const isClickable = step.status === 'completed' || step.id === currentStepId}
				
				<button
					class="w-full text-left p-3 rounded-xl transition-all duration-220 ease-out group relative
						{step.status === 'active' ? 'bg-gradient-to-r from-[#F5D6A1]/10 to-[#C49A6C]/5 border-l-4 border-[#F5D6A1]' : ''}
						{isClickable ? 'hover:bg-[#FCF8F2] cursor-pointer' : 'cursor-default'}
					"
					onclick={() => handleStepClick(step)}
					disabled={!isClickable}
					aria-current={step.status === 'active' ? 'step' : undefined}
				>
					<div class="flex items-start gap-3">
						<!-- Step Icon -->
						<div class="flex-shrink-0 mt-0.5">
							{#if step.status === 'completed'}
								<div class="w-8 h-8 bg-[#10B981] rounded-full flex items-center justify-center">
									<Check class="w-5 h-5 text-white" />
								</div>
							{:else if step.status === 'active'}
								<div class="w-8 h-8 bg-[#F5D6A1] rounded-full flex items-center justify-center border-2 border-[#C49A6C]">
									<span class="text-[#1C1C1C] font-semibold text-sm">{stepNumber}</span>
								</div>
							{:else}
								<div class="w-8 h-8 bg-[#F0E1CB] rounded-full flex items-center justify-center border-2 border-[#EDE0CF]">
									<span class="text-[#8A6A52] font-medium text-sm">{stepNumber}</span>
								</div>
							{/if}
						</div>

						<!-- Step Content -->
						<div class="flex-1 min-w-0">
							<h3 class="font-medium text-sm
								{step.status === 'active' ? 'text-[#1C1C1C]' : 
								 step.status === 'completed' ? 'text-[#5C4024]' : 'text-[#8A6A52]'}
							">
								{step.title}
								{#if step.required}
									<span class="text-[#EF4444] ml-1">*</span>
								{/if}
							</h3>
							{#if step.description}
								<p class="text-xs text-[#8A6A52] mt-1 leading-relaxed">
									{step.description}
								</p>
							{/if}
						</div>
					</div>

					<!-- Connection Line -->
					{#if index < steps.length - 1}
						<div class="absolute left-6 top-11 w-0.5 h-6 
							{step.status === 'completed' ? 'bg-[#10B981]' : 'bg-[#EDE0CF]'}
						"></div>
					{/if}
				</button>
			{/each}
		</nav>
	</div>

	<!-- Footer -->
	<div class="mt-8 pt-6 border-t border-[#EDE0CF]">
		<p class="text-xs text-[#8A6A52] text-center">
			Need help? Contact HR at 
			<a href="mailto:<EMAIL>" class="text-[#C49A6C] hover:underline"><EMAIL></a>
		</p>
	</div>
</div>

<style>
	button:disabled {
		cursor: default;
	}
	
	button:disabled:hover {
		background: transparent;
	}
	
	button[aria-current="step"]:disabled:hover {
		background: linear-gradient(to right, rgba(245, 214, 161, 0.1), rgba(196, 154, 108, 0.05));
	}
</style>
