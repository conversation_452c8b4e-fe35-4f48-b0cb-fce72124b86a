<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import Modal from '$lib/components/ui/Modal.svelte';
	import { 
		ArrowLeft, 
		Edit, 
		Trash2, 
		Mail, 
		Phone, 
		Calendar, 
		MapPin, 
		Briefcase,
		User,
		Clock,
		Award,
		FileText
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const employeeId = page.params.id;

	// Mock employee data - in real app, this would be fetched from API
	const employee = {
		id: employeeId,
		employee_number: 'RTG001',
		first_name: '<PERSON>',
		last_name: '<PERSON><PERSON>',
		email: '<EMAIL>',
		phone: '+263 77 123 4567',
		position: 'Senior Software Developer',
		department: 'Information Technology',
		manager: '<PERSON>',
		hire_date: '2022-01-15',
		status: 'Active',
		salary: '$75,000',
		address: '123 Main Street, Harare, Zimbabwe',
		emergency_contact: '<PERSON> - +263 77 987 6543',
		skills: ['JavaScript', 'TypeScript', 'Svelte', 'Node.js', 'Python'],
		performance_rating: 'Excellent',
		last_review: '2024-01-15',
		next_review: '2025-01-15'
	};

	// Leave balance data
	const leaveBalance = {
		annual: { total: 21, used: 8, remaining: 13 },
		sick: { total: 10, used: 2, remaining: 8 },
		personal: { total: 5, used: 1, remaining: 4 }
	};

	// Recent activities
	const recentActivities = [
		{
			date: '2024-02-01',
			type: 'Leave Request',
			description: 'Applied for 3 days annual leave',
			status: 'Approved'
		},
		{
			date: '2024-01-28',
			type: 'Performance Review',
			description: 'Quarterly performance review completed',
			status: 'Completed'
		},
		{
			date: '2024-01-15',
			type: 'Training',
			description: 'Completed Advanced TypeScript course',
			status: 'Completed'
		}
	];

	let showDeleteModal = $state(false);
	let isDeleting = $state(false);

	const handleBack = () => {
		goto('/employees');
	};

	const handleEdit = () => {
		goto(`/employees/${employeeId}/edit`);
	};

	const handleDelete = async () => {
		isDeleting = true;
		// Simulate API call
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		console.log('Deleting employee:', employeeId);
		showDeleteModal = false;
		isDeleting = false;
		
		// Redirect back to employees list
		goto('/employees');
	};

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'active': return 'text-green-600 bg-green-100';
			case 'inactive': return 'text-red-600 bg-red-100';
			case 'on leave': return 'text-yellow-600 bg-yellow-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	const canEdit = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin';
</script>

<svelte:head>
	<title>{employee.first_name} {employee.last_name} - Employee Details - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleBack}>
				<ArrowLeft class="w-4 h-4" />
				Back to Employees
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">
					{employee.first_name} {employee.last_name}
				</h1>
				<p class="text-muted-foreground">{employee.position} • {employee.employee_number}</p>
			</div>
		</div>
		
		{#if canEdit}
			<div class="flex gap-2">
				<Button variant="default" onclick={handleEdit}>
					<Edit class="w-4 h-4" />
					Edit Employee
				</Button>
				<Button variant="destructive" onclick={() => showDeleteModal = true}>
					<Trash2 class="w-4 h-4" />
					Delete
				</Button>
			</div>
		{/if}
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Employee Profile -->
		<div class="lg:col-span-1">
			<Card class="p-6">
				<div class="text-center">
					<div class="w-24 h-24 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-2xl font-bold text-primary-foreground">
							{employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
						</span>
					</div>
					<h2 class="text-xl font-semibold text-foreground mb-1">
						{employee.first_name} {employee.last_name}
					</h2>
					<p class="text-muted-foreground mb-3">{employee.position}</p>
					<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(employee.status)}">
						{employee.status}
					</span>
				</div>

				<div class="mt-6 space-y-4">
					<div class="flex items-center gap-3">
						<Mail class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{employee.email}</span>
					</div>
					<div class="flex items-center gap-3">
						<Phone class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{employee.phone}</span>
					</div>
					<div class="flex items-center gap-3">
						<Briefcase class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{employee.department}</span>
					</div>
					<div class="flex items-center gap-3">
						<User class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">Manager: {employee.manager}</span>
					</div>
					<div class="flex items-center gap-3">
						<Calendar class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">
							Hired: {new Date(employee.hire_date).toLocaleDateString()}
						</span>
					</div>
					<div class="flex items-center gap-3">
						<MapPin class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{employee.address}</span>
					</div>
				</div>
			</Card>

			<!-- Leave Balance -->
			<Card class="mt-6 p-4">
				<h3 class="text-lg font-semibold text-foreground mb-4">Leave Balance</h3>
				<div class="space-y-3">
					{#each Object.entries(leaveBalance) as [type, balance]}
						<div class="flex justify-between items-center">
							<span class="text-sm text-muted-foreground capitalize">{type} Leave</span>
							<span class="text-sm font-medium text-foreground">
								{balance.remaining}/{balance.total}
							</span>
						</div>
					{/each}
				</div>
			</Card>
		</div>

		<!-- Employee Details -->
		<div class="lg:col-span-2 space-y-6">
			<!-- Personal Information -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-6">Personal Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Employee Number</div>
						<p class="text-foreground">{employee.employee_number}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Department</div>
						<p class="text-foreground">{employee.department}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Position</div>
						<p class="text-foreground">{employee.position}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Salary</div>
						<p class="text-foreground">{employee.salary}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Emergency Contact</div>
						<p class="text-foreground">{employee.emergency_contact}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Performance Rating</div>
						<div class="flex items-center gap-2">
							<Award class="w-4 h-4 text-yellow-500" />
							<span class="text-foreground">{employee.performance_rating}</span>
						</div>
					</div>
				</div>
			</Card>

			<!-- Skills -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Skills & Expertise</h3>
				<div class="flex flex-wrap gap-2">
					{#each employee.skills as skill}
						<span class="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full">
							{skill}
						</span>
					{/each}
				</div>
			</Card>

			<!-- Recent Activities -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Recent Activities</h3>
				<div class="space-y-4">
					{#each recentActivities as activity}
						<div class="flex items-start gap-3 p-3 border border-border rounded-lg">
							<div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
							<div class="flex-1">
								<div class="flex items-center justify-between mb-1">
									<h4 class="text-sm font-medium text-foreground">{activity.type}</h4>
									<span class="text-xs text-muted-foreground">
										{new Date(activity.date).toLocaleDateString()}
									</span>
								</div>
								<p class="text-sm text-muted-foreground mb-1">{activity.description}</p>
								<span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full {getStatusColor(activity.status)}">
									{activity.status}
								</span>
							</div>
						</div>
					{/each}
				</div>
			</Card>
		</div>
	</div>
</div>

<!-- Delete Confirmation Modal -->
<Modal bind:open={showDeleteModal} title="Delete Employee" size="md">
	{#snippet children()}
		<div class="space-y-4">
			<p class="text-muted-foreground">
				Are you sure you want to delete <strong>{employee.first_name} {employee.last_name}</strong>? 
				This action cannot be undone and will permanently remove all employee data.
			</p>
			<div class="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
				<p class="text-sm text-destructive">
					<strong>Warning:</strong> This will also remove all associated leave records, 
					performance reviews, and other employee data.
				</p>
			</div>
		</div>
	{/snippet}
	{#snippet footer()}
		<Button variant="secondary" onclick={() => showDeleteModal = false} disabled={isDeleting}>
			Cancel
		</Button>
		<Button variant="destructive" onclick={handleDelete} disabled={isDeleting}>
			<Trash2 class="w-4 h-4" />
			Delete Employee
		</Button>
	{/snippet}
</Modal>
