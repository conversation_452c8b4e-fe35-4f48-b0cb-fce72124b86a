{"meta": {"name": "Leave Management Style Guide", "file": "style-guide.leave-management.json", "version": "1.0.0", "description": "Tokens for leave application, balances, manager calendar, approval modals using RTG palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"primary": "#F5D6A1", "approved": "#8C6239", "pending": "#F59E0B", "rejected": "#EF4444", "balanceBg": "#FFF7ED", "calendarBg": "#FCF8F2"}, "gradients": {"leaveDonut": "linear-gradient(135deg,#F5D6A1 0%,#8C6239 100%)", "approvedGradient": "linear-gradient(90deg,#C49A6C,#8C6239)"}, "components": {"leaveForm": {"card": {"bg": "#FFFFFF", "radius": 12}, "submitBtn": {"bg": "#8C6239", "text": "#FFFFFF", "hover": "#5C4024"}, "attachment": {"dropzoneBg": "#FCF8F2"}}, "leaveBalanceCard": {"donutSize": 88, "bg": "#FFFFFF", "labelColor": "#5C4024"}, "managerCalendar": {"eventColors": {"approved": "#8C6239", "pending": "#F59E0B", "rejected": "#EF4444"}, "overlay": {"bg": "#FFFFFF", "border": "1px solid #EAD9BF"}}, "approvalModal": {"bg": "#FFFFFF", "confirmBtn": {"bg": "#8C6239", "text": "#FFFFFF"}, "rejectBtn": {"bg": "#EF4444", "text": "#FFFFFF"}}}, "interactions": {"validation": {"dateOrder": "to >= from", "balanceCheckBtn": "show remaining balance tooltip"}, "conflict": {"uiCue": "red toast + inline conflict icon"}}, "accessibility": {"colorBlind": "Supplement status by icon or text label for approved/pending/rejected"}, "tailwindSnippet": {"code": ".leave-approved { @apply bg-rtg-secondary text-white rounded px-2 py-1; }"}, "notes": {"mocks": ["/api/leaves"], "acceptance": ["Apply form validates dates and balance in client-side mock", "Manager can approve/reject from overlay"]}}