{"meta": {"name": "Performance Management Style Guide", "file": "style-guide.performance.json", "version": "1.0.0", "description": "Goals, reviews, 360 flow and calibration board tokens with RTG brand colors and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"goalPrimary": "#F5D6A1", "goalProgress": "#C49A6C", "reviewAnonBg": "#FCF8F2", "scoreHigh": "#10B981", "scoreLow": "#EF4444"}, "gradients": {"goalFill": "linear-gradient(90deg,#F5D6A1 0%,#C49A6C 100%)", "calibrationAccent": "linear-gradient(135deg,#C49A6C 0%,#8C6239 100%)"}, "components": {"goalEditor": {"input": {"bg": "#FFFFFF", "border": "1px solid #EAD9BF"}, "progressWidget": {"barHeight": 12, "fill": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}}, "reviewForm": {"ratingScale": {"active": "#C49A6C", "inactive": "#FCF8F2"}, "anonymizeToggle": {"bg": "#FFF7ED", "text": "#8C6239"}}, "calibrationBoard": {"card": {"bg": "#FFFFFF", "border": "1px solid #EDE0CF", "dragHandle": "#EAD9BF"}, "nineBoxCell": {"bg": "#FFF7ED", "border": "1px solid #EAD9BF"}}}, "interactions": {"checkins": {"inlineComment": true, "reminderDot": "#F59E0B"}, "360": {"anonymizeSubmission": true, "status": ["not-started", "in-progress", "complete"]}}, "accessibility": {"dragDropAccessible": "Provide keyboard alternatives for mobile/keyboard navigation"}, "notes": {"mocks": ["/api/goals", "/api/reviews", "/api/360"], "acceptance": ["Goal creation & progress updates persist in mock store", "Reviewers can submit and aggregated view hides identities for anonymous review"]}}