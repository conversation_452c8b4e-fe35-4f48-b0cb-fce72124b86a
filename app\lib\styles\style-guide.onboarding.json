{"meta": {"name": "Onboarding - Complete Process & Style Guide", "file": "style-guide.onboarding.json", "version": "1.0.0", "description": "Complete onboarding workflow, UI screens, form schemas, accessibility, motion tokens and component tokens for Rainbow Tourism Group HRIMS. Frontend-first (mock endpoints included). Uses RTG brand palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json", "note": "Dashboard tokens are canonical for colors/gradients/typography. This onboarding file keeps module-specific overrides and motion tokens."}, "colors": {"brand": {"primary": "#F5D6A1", "primary-400": "#E9C584", "accent": "#C49A6C", "secondary": "#8C6239", "neutralDark": "#3B2A1A", "neutralLight": "#F9F5F0", "black": "#1C1C1C", "white": "#FFFFFF"}, "surface": {"pageBg": "#F9F5F0", "cardBg": "#FFFFFF", "mutedSurface": "#FCF8F2", "border": "#EAD9BF"}, "semantic": {"success": "#10B981", "warning": "#F59E0B", "danger": "#EF4444", "info": "#0EA5E9"}, "text": {"primary": "#1C1C1C", "muted": "#8A6A52", "onPrimary": "#1C1C1C", "onDark": "#FFFFFF"}}, "gradients": {"primary": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "accent": "linear-gradient(135deg,#C49A6C 0%,#F5D6A1 100%)", "softBeige": "linear-gradient(180deg, rgba(245,214,161,0.08), rgba(245,214,161,0.02))", "hero": "linear-gradient(120deg,#F5D6A1 0%,#8C6239 50%,#3B2A1A 100%)"}, "motion": {"tokens": {"duration-xxs": 80, "duration-xs": 120, "duration-sm": 180, "duration-md": 280, "duration-lg": 420, "duration-xl": 600, "easing-quick": "cubic-bezier(.2,.9,.3,1)", "easing-standard": "cubic-bezier(.2,.8,.2,1)", "easing-smooth": "cubic-bezier(.22,.95,.26,1)", "easing-pop": "cubic-bezier(.175,.885,.32,1.275)"}, "keyframes": {"onboard-pop": {"0%": {"transform": "scale(.98)", "opacity": 0}, "60%": {"transform": "scale(1.04)", "opacity": 1}, "100%": {"transform": "scale(1)", "opacity": 1}}, "slide-up-fade": {"0%": {"transform": "translateY(12px)", "opacity": 0}, "100%": {"transform": "translateY(0)", "opacity": 1}}, "fade": {"0%": {"opacity": 0}, "100%": {"opacity": 1}}}, "componentAnimations": {"stepper-step-entrance": {"animation": "onboard-pop", "duration": "duration-md", "easing": "easing-pop"}, "card-entrance": {"animation": "slide-up-fade", "duration": "duration-md", "easing": "easing-smooth"}, "mini-toast": {"animation": "fade", "duration": "duration-sm", "easing": "easing-quick"}, "progress-fill": {"transition": "width duration-lg easing-standard"}, "avatar-pop": {"animation": "onboard-pop", "duration": "duration-sm", "easing": "easing-pop"}}, "accessibility": {"prefers-reduced-motion": "All onboarding animations must be disabled or reduced to 0 duration when OS-level prefers-reduced-motion is set."}}, "workflow": {"flowName": "New Employee Onboarding (Frontend-first)", "order": ["invite-and-account-creation", "company-intro-and-welcome", "personal-details", "employment-details", "tax-and-payroll", "bank-details", "documents-upload-and-scan", "contracts-and-e-sign", "policy-acknowledgement", "training-and-first-week-plan", "equipment-and-assets", "manager-approval-and-checklist", "final-confirmation-and-welcome-pack"], "detailedSteps": {"invite-and-account-creation": {"purpose": "HR or system sends an invitation email (Outlook company email) with one-time link to create account. UI must accept only outlook/company email domains.", "frontendOnly": true, "uiScreens": ["invite-landing", "set-password"], "keyActions": ["Accept invite token from URL (mocked)", "Show company logo, short welcome, 'Start onboarding' CTA", "Accept password & create profile - client-side validation"], "validations": {"emailDomain": ["@company.com", "@outlook.com", "optional-domain-list"], "passwordMinLength": 12, "passwordStrength": "must include letters+numbers"}, "mocks": ["/api/onboarding/invite/validate", "/api/onboarding/invite/accept"]}, "company-intro-and-welcome": {"purpose": "Show company intro, hero gradients, welcome video thumbnail. Ask for confirmation to proceed.", "uiScreens": ["welcome-hero", "tour-intro"], "animations": ["hero fade-in", "thumbnail soft pop"], "actions": ["Play intro video (mock)", "Start guided tour"]}, "personal-details": {"purpose": "Collect full legal name, preferred name, DOB, gender, nationality, contact numbers, emergency contact.", "uiScreens": ["personal-info-form"], "fields": {"firstName": {"required": true, "type": "string", "maxLength": 60}, "lastName": {"required": true, "type": "string", "maxLength": 60}, "preferredName": {"required": false, "type": "string", "maxLength": 40}, "dateOfBirth": {"required": true, "type": "date", "validation": ">= 18 years old"}, "gender": {"required": false, "type": "enum", "options": ["Female", "Male", "Non-binary", "Prefer not to say"]}, "phone": {"required": true, "type": "tel", "pattern": "E.164 recommended"}, "emergencyContact": {"required": true, "type": "object", "fields": ["name", "relation", "phone"]}}, "validations": {"clientSide": true, "errorHandling": "inline with ARIA alerts"}, "uiHints": {"labelPlacement": "top", "helpText": "Use official documents for legal names"}, "mocks": ["/api/onboarding/personal/save"]}, "employment-details": {"purpose": "Collect job title, department, manager, start date, location, employment type.", "uiScreens": ["employment-info-form"], "fields": {"jobTitle": {"required": true, "type": "string"}, "department": {"type": "string", "required": true}, "manager": {"type": "select", "required": true, "source": "/api/staff/managers (mock)"}, "startDate": {"required": true, "type": "date", "validation": "cannot be in past beyond 7 days unless flagged"}, "employmentType": {"required": true, "type": "enum", "options": ["Permanent", "Contractor", "Temporary", "Intern"]}, "workLocation": {"required": true, "type": "string"}}, "interactions": {"managerLookup": "autocomplete", "startDatePicker": "calendar component"}, "mocks": ["/api/onboarding/employment/save", "/api/staff/managers"]}, "tax-and-payroll": {"purpose": "Collect tax identifiers and payroll preferences. Provide payslip preview UI.", "uiScreens": ["tax-form", "payslip-preview"], "fields": {"taxId": {"required": true, "type": "string"}, "taxStatus": {"type": "select", "options": ["Resident", "Non-Resident", "Exempt"]}, "preferredPayslipFormat": {"type": "enum", "options": ["PDF", "Email"], "default": "PDF"}, "payFrequency": {"type": "select", "options": ["Monthly", "Bi-weekly"]}}, "validations": {"formatChecks": true, "serverSideCheckMock": true}, "mocks": ["/api/onboarding/tax/save", "/api/onboarding/payslip/preview"]}, "bank-details": {"purpose": "Collect and verify bank account details for payroll.", "uiScreens": ["bank-form", "bank-verify"], "fields": {"bankName": {"required": true}, "accountName": {"required": true}, "accountNumber": {"required": true, "validation": "numeric, country-specific lengths"}, "swiftCode": {"required": false}}, "interactions": {"verifyAccount": "mock micro-deposit flow (frontend-only show 'pending verification')"}, "mocks": ["/api/onboarding/bank/save", "/api/onboarding/bank/verify"]}, "documents-upload-and-scan": {"purpose": "Collect ID documents, certificates, and signed forms. Run client-side file-scan flow and show status.", "uiScreens": ["documents-upload", "scan-status"], "requirements": ["Allow multiple documents: ID front, ID back, degree/certificates, regulatory permits", "Max file size: 10MB", "Allowed types: PDF, PNG, JPG"], "interactions": {"upload": "drag-and-drop or file picker", "pre-scan": "client-side MIME + extension check", "edgeFunction-scan": "issue signed-URL -> upload -> trigger file-scan -> poll status (all mocked)"}, "documentBadges": {"pending_scan": "#F59E0B", "clean": "#10B981", "quarantine": "#EF4444"}, "motion": {"thumbPop": "avatar-pop", "scanPulse": "soft-beige shimmer"}, "mocks": ["/api/onboarding/documents/upload-url", "/api/onboarding/documents/scan-status", "/api/onboarding/documents/list"]}, "contracts-and-e-sign": {"purpose": "Present contract, allow negotiation flags (frontend-only), collect e-signature.", "uiScreens": ["contract-preview", "esign-capture", "contract-complete"], "interactions": {"preview": "PDF viewer with gradient header", "sign": "typed name + drawn signature canvas (capture dataUri)", "consentFlow": "capture timestamp and IP (mocked) and show signed badge"}, "animations": {"pdfHeader": "previewAccent gradient slide-in"}, "mocks": ["/api/onboarding/contracts/generate", "/api/onboarding/contracts/sign"]}, "policy-acknowledgement": {"purpose": "Present company policies and collect mandatory acknowledgments.", "uiScreens": ["policy-list", "policy-read", "policy-acknowledge"], "interactions": {"readTracking": "time-spent & scroll-percent (frontend-only)", "acknowledge": "checkbox + signature"}, "accessibility": {"policyTextReadability": "min font-size 14px on beige", "contrastNote": "Ensure link color contrasts"}, "mocks": ["/api/onboarding/policies/list", "/api/onboarding/policies/ack"]}, "training-and-first-week-plan": {"purpose": "Show required training, enroll user in mandatory sessions, present first-week agenda.", "uiScreens": ["training-catalog", "enroll-modal", "first-week-plan"], "components": {"trainingCardGradient": "trainingCard gradient token"}, "interactions": {"auto-enroll": "one-click enroll for mandatory items", "calendar-add": "add to user's calendar (mocked)"}, "mocks": ["/api/training/catalog", "/api/enrollments"]}, "equipment-and-assets": {"purpose": "Request and confirm equipment (laptop, phone), shipping address for physical items.", "uiScreens": ["asset-request", "asset-confirmation"], "fields": {"preferredLaptop": {"options": ["MacBook Air", "Windows 14"], "required": true}, "deliveryAddress": {"required": true}}, "interactions": {"managerApprovalFlag": true}, "mocks": ["/api/assets/request"]}, "manager-approval-and-checklist": {"purpose": "Show manager the pending approvals (manager <PERSON><PERSON> mocked) and checklist tasks auto-generated.", "uiScreens": ["manager-approval-view", "checklist-dashboard"], "interactions": {"approveAll": "manager action (mock)", "taskAssign": "assign owner & due dates"}, "mocks": ["/api/onboarding/approvals", "/api/onboarding/tasks"]}, "final-confirmation-and-welcome-pack": {"purpose": "Show completion screen, offer welcome pack (PDF/email), optional team intro scheduling.", "uiScreens": ["final-summary", "welcome-pack"], "components": {"confetti": "light confetti limited for reduced-motion", "summaryCard": {"bg": "#FFFFFF"}}, "deliverables": ["signed documents list", "training schedule", "equipment status", "payroll confirmation"], "mocks": ["/api/onboarding/complete"]}}}, "uiScreens": {"invite-landing": {"title": "You're invited to join RTG", "hero": {"bg": "gradients.hero", "textColor": "colors.text.onPrimary"}, "cta": {"label": "Accept Invite", "style": "primary"}, "animation": "fade"}, "set-password": {"title": "Create your password", "fields": ["password", "confirmPassword"], "passwordRules": ["min 12 characters", "at least 1 number", "at least 1 uppercase", "no common passwords"], "progress": "show strength meter (animated width)", "animations": ["password-strength fill transition"]}, "welcome-hero": {"title": "Welcome to Rainbow Tourism Group", "subtitle": "Quick tour & next steps", "videoThumb": {"size": "16:9", "overlayGradient": "gradients.softBeige"}, "ctaPrimary": "Start onboarding", "ctaSecondary": "Take a guided tour"}, "personal-info-form": {"title": "Personal Details", "layout": "two-column (responsive)", "cardStyle": {"bg": "surface.cardBg", "radius": 12, "shadow": "shadows.elevation-1"}, "animations": ["card-entrance", "avatar-pop on upload"]}, "documents-upload": {"title": "Upload Documents", "dropzone": {"bg": "surface.mutedSurface", "border": "colors.surface.border", "radius": 12}, "fileListStyle": {"thumbSize": 88, "statusBadgeColors": "documentBadges"}, "scanStatusUI": {"progress": "striped progress with animation", "statusChip": true}}, "contract-preview": {"title": "Your Contract", "viewer": {"toolbarBg": "surface.mutedSurface", "headerGradient": "gradients.previewAccent"}, "ctaSign": {"label": "Sign contract", "style": "primary"}, "animations": ["pdfHeader slide-in", "cta pop"]}, "final-summary": {"title": "You're Good to Go", "summaryCards": {"layout": "grid", "cardAccent": "gradients.primary"}, "cta": {"label": "Download Welcome Pack", "style": "accent"}, "animations": ["card-entrance stagger"]}}, "forms": {"validation": {"clientSide": {"useZodOrYup": "recommended zod for TS-first codebase", "errorDisplay": "inline + aria-live polite for screen readers", "debounceValidation": 300}, "rules": {"nameFields": {"noNumbers": true}, "dateOfBirth": {"minAge": 18}, "phone": {"pattern": "E.164 encouraged"}, "bankAccount": {"numericOnly": true, "maxLength": 34}}}, "savingStrategy": {"autosave": {"intervalMs": 5000, "onBlur": true}, "drafts": "save partials to localStorage and upload to /api/onboarding/draft (mock)", "finalSubmit": "bundle all sections and post to /api/onboarding/complete (mock)"}}, "components": {"stepper": {"style": {"orientation": "horizontal", "stepRadius": 8, "activeBg": "colors.brand.primary", "inactiveBg": "colors.surface.mutedSurface"}, "animation": {"stepChange": "stepper-step-entrance", "staggerMs": 70}, "a11y": {"role": "progressbar", "aria-valuemin": 0, "aria-valuemax": 100}}, "profileAvatarUploader": {"size": 88, "border": "2px solid #FFFFFF", "shadow": "shadows.elevation-1", "animations": {"onAdd": "avatar-pop"}, "fallbackInitials": {"bg": "#F9F5F0", "text": "#8C6239"}}, "fileUpload": {"dropzone": {"height": 140, "bg": "surface.mutedSurface", "borderStyle": "dashed", "borderColor": "colors.surface.border", "radius": 12}, "thumbnail": {"size": 88, "popAnimation": "avatar-pop"}, "progress": {"height": 6, "fillGradient": "gradients.primary", "animated": true}}, "pdfViewer": {"toolbar": {"bg": "surface.mutedSurface", "buttonStyle": "ghost"}, "pageBg": "surface.cardBg"}, "signaturePad": {"canvasStyle": {"border": "1px solid colors.surface.border", "height": 200, "radius": 8}, "controls": {"saveBtn": {"bg": "colors.brand.secondary", "text": "colors.text.onDark"}, "clearBtn": {"bg": "transparent"}}, "accessibility": "Allow typed signature alternative"}, "checklist": {"taskRow": {"height": 64, "badge": {"bg": "colors.brand.primary"}, "animation": "card-entrance"}, "progress": {"barHeight": 12, "fill": "gradients.primary"}}, "floatingHelp": {"position": "bottom-right", "style": {"bg": "colors.brand.primary", "text": "colors.text.onPrimary", "radius": 9999, "shadow": "shadows.elevation-2"}, "animation": {"in": "fade", "duration": "duration-sm"}}}, "interactions": {"autosave": {"behavior": "debounced autosave to local + mock endpoint; show autosave toast with mini-toast animation", "uiCue": "small saving indicator near header with spinner"}, "document-scan-flow": {"flow": ["request_upload_url -> display signed-url (mock)", "upload file to signed-url -> show 'uploaded' state (client)", "trigger scan (mock call) -> poll /api/onboarding/documents/scan-status until clean/quarantine -> show badge"], "ui": {"uploadAnimation": "thumbnail pop", "scanPulse": "soft shimmer on pending"}}, "managerApproval": {"flow": ["notify manager (mock) -> manager approves -> onboarding proceeds -> show manager badge and timestamp on final summary"], "ui": {"approveButtonStyle": "secondary", "bulkApprove": true}}, "tour": {"behavior": "guided tooltip tour with step highlight, can skip, accessible via keyboard, obeys prefers-reduced-motion", "animation": "subtle fade+scale per step"}}, "accessibility": {"keyPoints": ["All interactive elements must be keyboard reachable and have visible focus states (use states.focus token).", "Use aria-live polite for autosave and scan-status updates so screen readers receive progress updates.", "Provide text alternatives for video; transcripts for training; typed alternative for e-sign.", "Respect prefers-reduced-motion: provide immediate state changes and no decorative animation."], "contrastNotes": ["Primary beige (#F5D6A1) with dark text (#1C1C1C) meets AA for large text; use bold/size for small text or use darker accent for small labels.", "Do not use small body text on beige backgrounds without sufficient contrast; prefer darker brown (#3B2A1A) for small labels."]}, "mocks": {"endpoints": {"/api/onboarding/invite/validate": {"method": "GET", "response": "{ valid: true, email: '<EMAIL>' }"}, "/api/onboarding/invite/accept": {"method": "POST", "response": "{ success: true, onboardingId: 'onb_abc123' }"}, "/api/onboarding/personal/save": {"method": "POST", "response": "{ success: true }"}, "/api/onboarding/documents/upload-url": {"method": "POST", "response": "{ uploadUrl: 'https://mock.signed.url/upload', fileId: 'file_1' }"}, "/api/onboarding/documents/scan-status": {"method": "GET", "response": "{ fileId: 'file_1', status: 'pending' } -> after delay -> status: 'clean' }"}, "/api/onboarding/contracts/generate": {"method": "POST", "response": "{ pdfUrl: 'https://mock.pdf/contract.pdf' }"}, "/api/onboarding/contracts/sign": {"method": "POST", "response": "{ signed: true, signedAt: '2025-08-28T10:00:00Z' }"}, "/api/onboarding/complete": {"method": "POST", "response": "{ status: 'complete', welcomePackUrl: 'https://mock.welcome/pack.pdf' }"}}, "notes": "MSW handlers should simulate delays: brief upload latency (300-800ms), file-scan (2-6s), contract generation (1-2s)."}, "acceptanceCriteria": {"frontendDeliverables": ["Complete UI for each listed onboarding step implemented", "Autosave works (localStorage + mock endpoint) and shows toast feedback", "Document upload flow returns signed URL (mock) and shows scan status updates until 'clean' or 'quarantine'", "Contract preview and signature capture work (typed + drawn), and sign triggers 'signed' state", "Manager approval mock flow exists and toggles onboarding to 'awaiting-manager' and 'approved' states", "All forms validate client-side and show inline ARIA-friendly errors", "Animations are subtle, accessible, and disabled under prefers-reduced-motion"], "visual": ["Onboarding screens use RTG colors & gradients and match spacing/typography tokens", "Stepper shows progress and is interactive (can navigate to previous completed steps)", "Welcome hero uses hero gradient, and final summary uses primary gradient accents"], "performance": ["Pages lazy-load heavy assets (video, pdf viewer).", "JS bundle for onboarding pages kept minimal (use code-splitting)."]}, "tokensToCssVars": {"prefix": "--rtg-onb", "mapping": {"color-primary": "colors.brand.primary", "color-accent": "colors.brand.accent", "bg-page": "colors.surface.pageBg", "card-bg": "colors.surface.cardBg", "border": "colors.surface.border", "gradient-primary": "gradients.primary", "gradient-hero": "gradients.hero", "motion-duration-sm": "motion.tokens.duration-sm", "motion-easing-pop": "motion.tokens.easing-pop", "radius-card": "radius.card"}, "exampleOutput": {"--rtg-onb-color-primary": "#F5D6A1", "--rtg-onb-gradient-primary": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "--rtg-onb-motion-duration-sm": "180ms"}}, "tailwindSnippet": {"explanation": "Add these into your tailwind.config.cjs under theme.extend so classes like bg-rtg-onb-primary and bg-gradient-rtg-onb-hero are available.", "code": "module.exports = { theme: { extend: { colors: { 'rtg-onb-primary': 'var(--rtg-onb-color-primary)', 'rtg-onb-accent': 'var(--rtg-onb-color-accent)', 'rtg-onb-card': 'var(--rtg-onb-card-bg)' }, backgroundImage: { 'rtg-onb-hero': \"var(--rtg-onb-gradient-hero)\" }, transitionTimingFunction: { 'rtg-pop': 'cubic-bezier(.175,.885,.32,1.275)' }, transitionDuration: { 'rtg-sm': '180ms' }, borderRadius: { 'rtg-card': '12px' } } } }"}, "implementationNotes": {"frontendFirst": "Implement onboarding UI with SvelteKit pages in /routes/onboarding/. Use MSW to mock backend endpoints. Use localStorage drafts as single-source until backend implemented.", "animationIntegration": "Use Motion One (recommended) for element animations (entrance, pop, progress). Respect prefers-reduced-motion.", "fileScanEdgeFn": "Edge function flow is mocked in frontend: obtain signed-URL from /api/onboarding/documents/upload-url, upload directly, then poll /api/onboarding/documents/scan-status. When backend implemented, replace mocks with real endpoints and keep UI logic intact.", "testing": "Write unit tests for form validation (zod) and e2e flows (Playwright) for key happy/unhappy paths (document quarantine, manager rejection).", "folderPlacement": "Save as app/lib/styles/style-guide.onboarding.json. Wire into tokens generation & _variables.css build step."}}