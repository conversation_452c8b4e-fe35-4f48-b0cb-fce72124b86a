<script lang="ts">
	// Test page for demonstrating the enhanced notification system
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';

	// Test different notification types
	const testSuccess = () => {
		notificationStore.success(
			'Success!', 
			'This is a success notification with auto-dismiss.',
			{ duration: 3000 }
		);
	};

	const testError = () => {
		notificationStore.error(
			'Error Occurred', 
			'This is an error notification that persists until dismissed.',
			{ dismissible: true }
		);
	};

	const testWarning = () => {
		notificationStore.warning(
			'Warning', 
			'This is a warning notification with medium priority.',
			{ priority: 'normal', duration: 4000 }
		);
	};

	const testInfo = () => {
		notificationStore.info(
			'Information', 
			'This is an info notification with low priority.',
			{ priority: 'low', duration: 2000 }
		);
	};

	const testCritical = () => {
		notificationStore.critical(
			'Critical Alert!', 
			'This is a critical notification that requires immediate attention.',
			{ persistent: true }
		);
	};

	const testWithAction = () => {
		notificationStore.add({
			type: 'info',
			title: 'Action Required',
			message: 'Click the action button to proceed.',
			duration: 0,
			action: {
				label: 'Take Action',
				onClick: () => {
					notificationStore.success('Action Completed', 'You clicked the action button!');
				}
			}
		});
	};

	const testMultiple = () => {
		notificationStore.addMultiple([
			{
				type: 'success',
				title: 'First Notification',
				message: 'This is the first notification.',
				priority: 'low'
			},
			{
				type: 'warning',
				title: 'Second Notification',
				message: 'This is the second notification.',
				priority: 'normal'
			},
			{
				type: 'error',
				title: 'Third Notification',
				message: 'This is the third notification.',
				priority: 'high'
			}
		]);
	};

	const testPositions = () => {
		const positions = ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'] as const;
		
		positions.forEach((position, index) => {
			setTimeout(() => {
				notificationStore.add({
					type: 'info',
					title: `${position} Position`,
					message: `This notification appears in ${position}`,
					position,
					duration: 3000
				});
			}, index * 500);
		});
	};

	const testProgress = () => {
		notificationStore.add({
			type: 'info',
			title: 'Progress Notification',
			message: 'Watch the progress bar as this notification auto-dismisses.',
			duration: 5000,
			showProgress: true
		});
	};

	const clearAll = () => {
		notificationStore.clear();
	};

	// Test configuration changes
	const enableSounds = () => {
		notificationStore.updateConfig({ enableSounds: true });
		notificationStore.success('Sounds Enabled', 'Notification sounds are now enabled.');
	};

	const disableSounds = () => {
		notificationStore.updateConfig({ enableSounds: false });
		notificationStore.success('Sounds Disabled', 'Notification sounds are now disabled.');
	};

	const setMaxNotifications = (max: number) => {
		notificationStore.updateConfig({ maxNotifications: max });
		notificationStore.info('Max Notifications Updated', `Maximum notifications set to ${max}.`);
	};
</script>

<svelte:head>
	<title>Test Notifications - HRIMS</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<div class="text-center">
		<h1 class="text-3xl font-bold text-foreground mb-2">Notification System Test</h1>
		<p class="text-muted-foreground">Test the enhanced notification system with various types and configurations</p>
	</div>

	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		<!-- Basic Notification Types -->
		<Card>
			<CardHeader>
				<CardTitle>Basic Types</CardTitle>
				<CardDescription>Test different notification types</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={testSuccess} variant="default" class="w-full">
					Success Notification
				</Button>
				<Button onclick={testError} variant="destructive" class="w-full">
					Error Notification
				</Button>
				<Button onclick={testWarning} variant="secondary" class="w-full">
					Warning Notification
				</Button>
				<Button onclick={testInfo} variant="outline" class="w-full">
					Info Notification
				</Button>
			</CardContent>
		</Card>

		<!-- Priority and Special Types -->
		<Card>
			<CardHeader>
				<CardTitle>Priority & Special</CardTitle>
				<CardDescription>Test priority levels and special notifications</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={testCritical} variant="destructive" class="w-full">
					Critical Alert
				</Button>
				<Button onclick={testWithAction} variant="default" class="w-full">
					With Action Button
				</Button>
				<Button onclick={testProgress} variant="outline" class="w-full">
					With Progress Bar
				</Button>
				<Button onclick={testMultiple} variant="secondary" class="w-full">
					Multiple Notifications
				</Button>
			</CardContent>
		</Card>

		<!-- Position Testing -->
		<Card>
			<CardHeader>
				<CardTitle>Positions</CardTitle>
				<CardDescription>Test different notification positions</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={testPositions} variant="default" class="w-full">
					Test All Positions
				</Button>
				<Button onclick={() => notificationStore.add({
					type: 'success',
					title: 'Top Right',
					message: 'Default position',
					position: 'top-right'
				})} variant="outline" class="w-full">
					Top Right
				</Button>
				<Button onclick={() => notificationStore.add({
					type: 'info',
					title: 'Bottom Center',
					message: 'Bottom center position',
					position: 'bottom-center'
				})} variant="outline" class="w-full">
					Bottom Center
				</Button>
			</CardContent>
		</Card>

		<!-- Configuration -->
		<Card>
			<CardHeader>
				<CardTitle>Configuration</CardTitle>
				<CardDescription>Test configuration changes</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={enableSounds} variant="default" class="w-full">
					Enable Sounds
				</Button>
				<Button onclick={disableSounds} variant="secondary" class="w-full">
					Disable Sounds
				</Button>
				<Button onclick={() => setMaxNotifications(3)} variant="outline" class="w-full">
					Max 3 Notifications
				</Button>
				<Button onclick={() => setMaxNotifications(10)} variant="outline" class="w-full">
					Max 10 Notifications
				</Button>
			</CardContent>
		</Card>

		<!-- Quick Actions -->
		<Card>
			<CardHeader>
				<CardTitle>Quick Actions</CardTitle>
				<CardDescription>Quick notification methods</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={() => notificationStore.quickSuccess('Quick success message!')} variant="default" class="w-full">
					Quick Success
				</Button>
				<Button onclick={() => notificationStore.quickError('Quick error message!')} variant="destructive" class="w-full">
					Quick Error
				</Button>
				<Button onclick={() => notificationStore.quickWarning('Quick warning message!')} variant="secondary" class="w-full">
					Quick Warning
				</Button>
				<Button onclick={() => notificationStore.quickInfo('Quick info message!')} variant="outline" class="w-full">
					Quick Info
				</Button>
			</CardContent>
		</Card>

		<!-- Management -->
		<Card>
			<CardHeader>
				<CardTitle>Management</CardTitle>
				<CardDescription>Manage notifications</CardDescription>
			</CardHeader>
			<CardContent class="space-y-2">
				<Button onclick={clearAll} variant="destructive" class="w-full">
					Clear All Notifications
				</Button>
				<Button onclick={() => notificationStore.removeByType('error')} variant="secondary" class="w-full">
					Remove All Errors
				</Button>
				<Button onclick={() => notificationStore.removeByPriority('low')} variant="outline" class="w-full">
					Remove Low Priority
				</Button>
			</CardContent>
		</Card>
	</div>

	<!-- Instructions -->
	<Card>
		<CardHeader>
			<CardTitle>Instructions</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="space-y-2 text-sm text-muted-foreground">
				<p><strong>Features demonstrated:</strong></p>
				<ul class="list-disc list-inside space-y-1">
					<li>Multiple notification types (success, error, warning, info, critical)</li>
					<li>Priority levels (low, normal, high, critical)</li>
					<li>Auto-dismiss with configurable timeout</li>
					<li>Progress bars showing remaining time</li>
					<li>Action buttons within notifications</li>
					<li>Different positioning options</li>
					<li>Queue management and limits</li>
					<li>Accessibility features (ARIA labels, keyboard navigation)</li>
					<li>RTG branding and theming</li>
					<li>Smooth animations and transitions</li>
				</ul>
				<p><strong>Keyboard shortcuts:</strong></p>
				<ul class="list-disc list-inside space-y-1">
					<li>Escape: Dismiss most recent notification</li>
					<li>Ctrl/Cmd + Shift + X: Clear all notifications</li>
				</ul>
			</div>
		</CardContent>
	</Card>
</div>
