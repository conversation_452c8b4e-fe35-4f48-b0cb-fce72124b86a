import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Onboarding step definitions
export interface OnboardingStep {
	id: string;
	title: string;
	description?: string;
	status: 'upcoming' | 'active' | 'completed';
	required: boolean;
	route: string;
}

// Form data interfaces based on JSON schemas from documentation
export interface PersonalDetails {
	firstName: string;
	lastName: string;
	preferredName?: string;
	dateOfBirth: string;
	nationalId?: string;
	email: string;
	phone: string;
	address: {
		street: string;
		street2?: string;
		city: string;
		postalCode: string;
		country: string;
	};
	emergencyContact: {
		name: string;
		relationship: string;
		phone: string;
	};
}

export interface EmploymentDetails {
	jobTitle: string;
	department: string;
	manager: string;
	startDate: string;
	employmentType: 'Permanent' | 'Contractor' | 'Temporary' | 'Intern';
	workLocation: string;
}

export interface TaxInfo {
	taxId: string;
	taxStatus: 'Resident' | 'Non-Resident' | 'Exempt';
	preferredPayslipFormat: 'PDF' | 'Email';
	payFrequency: 'Monthly' | 'Bi-weekly';
}

export interface BankDetails {
	bankName: string;
	accountName: string;
	accountNumber: string;
	swiftCode?: string;
}

export interface OnboardingData {
	personalDetails?: Partial<PersonalDetails>;
	employmentDetails?: Partial<EmploymentDetails>;
	taxInfo?: Partial<TaxInfo>;
	bankDetails?: Partial<BankDetails>;
	documents?: Array<{
		id: string;
		name: string;
		type: string;
		status: 'pending' | 'uploaded' | 'verified' | 'rejected';
	}>;
	agreements?: {
		nda: boolean;
		policies: boolean;
		contract: boolean;
	};
}

export interface OnboardingState {
	currentStepId: string;
	steps: OnboardingStep[];
	data: OnboardingData;
	isLoading: boolean;
	error: string | null;
	lastSaved: Date | null;
}

// Default onboarding steps - Complete 13-step flow per checklist
const defaultSteps: OnboardingStep[] = [
	{
		id: 'invite-account',
		title: 'Invite & Account',
		description: 'Accept invite token, create password or SSO with Microsoft/Azure',
		status: 'completed',
		required: true,
		route: '/onboarding/invite'
	},
	{
		id: 'company-intro',
		title: 'Company Intro',
		description: 'Welcome video and company overview',
		status: 'completed',
		required: true,
		route: '/onboarding/welcome'
	},
	{
		id: 'personal-details',
		title: 'Personal Details',
		description: 'Provide personal information and emergency contacts',
		status: 'active',
		required: true,
		route: '/onboarding/personal-details'
	},
	{
		id: 'employment-details',
		title: 'Employment Details',
		description: 'Job title, department, manager, and start date',
		status: 'upcoming',
		required: true,
		route: '/onboarding/employment-details'
	},
	{
		id: 'tax-details',
		title: 'Tax Details',
		description: 'Tax information and payroll setup',
		status: 'upcoming',
		required: true,
		route: '/onboarding/tax-details'
	},
	{
		id: 'bank-details',
		title: 'Bank Details',
		description: 'Banking information for direct deposit',
		status: 'upcoming',
		required: true,
		route: '/onboarding/bank-details'
	},
	{
		id: 'documents',
		title: 'Documents',
		description: 'Upload required documents for verification',
		status: 'upcoming',
		required: true,
		route: '/onboarding/documents'
	},
	{
		id: 'contracts',
		title: 'Contracts',
		description: 'Review and sign employment contract',
		status: 'upcoming',
		required: true,
		route: '/onboarding/contracts'
	},
	{
		id: 'policies',
		title: 'Policies',
		description: 'Read and acknowledge company policies',
		status: 'upcoming',
		required: true,
		route: '/onboarding/policies'
	},
	{
		id: 'training',
		title: 'Training',
		description: 'Enroll in training and review first week agenda',
		status: 'upcoming',
		required: true,
		route: '/onboarding/training'
	},
	{
		id: 'assets',
		title: 'Assets',
		description: 'Request necessary equipment and assets',
		status: 'upcoming',
		required: true,
		route: '/onboarding/assets'
	},
	{
		id: 'approvals',
		title: 'Approvals',
		description: 'Manager review and approval of onboarding',
		status: 'upcoming',
		required: true,
		route: '/onboarding/approvals'
	},
	{
		id: 'complete',
		title: 'Complete',
		description: 'Complete onboarding and receive welcome materials',
		status: 'upcoming',
		required: true,
		route: '/onboarding/complete'
	}
];

// Initial state
const initialState: OnboardingState = {
	currentStepId: 'personal-details',
	steps: defaultSteps,
	data: {},
	isLoading: false,
	error: null,
	lastSaved: null
};

// Load state from localStorage if available
function loadState(): OnboardingState {
	if (!browser) return initialState;
	
	try {
		const saved = localStorage.getItem('hrims-onboarding-state');
		if (saved) {
			const parsed = JSON.parse(saved);
			return {
				...initialState,
				...parsed,
				lastSaved: parsed.lastSaved ? new Date(parsed.lastSaved) : null
			};
		}
	} catch (error) {
		console.warn('Failed to load onboarding state from localStorage:', error);
	}
	
	return initialState;
}

// Create the store
function createOnboardingStore() {
	const { subscribe, set, update } = writable<OnboardingState>(loadState());

	// Save to localStorage whenever state changes
	if (browser) {
		subscribe((state) => {
			try {
				localStorage.setItem('hrims-onboarding-state', JSON.stringify(state));
			} catch (error) {
				console.warn('Failed to save onboarding state to localStorage:', error);
			}
		});
	}

	return {
		subscribe,
		
		// Navigation methods
		setCurrentStep: (stepId: string) => {
			update(state => ({
				...state,
				currentStepId: stepId
			}));
		},

		// Step management
		completeStep: (stepId: string) => {
			update(state => {
				const steps = state.steps.map(step => {
					if (step.id === stepId) {
						return { ...step, status: 'completed' as const };
					}
					return step;
				});
				
				// Find next step and make it active
				const currentIndex = steps.findIndex(s => s.id === stepId);
				if (currentIndex >= 0 && currentIndex < steps.length - 1) {
					steps[currentIndex + 1].status = 'active';
				}
				
				return { ...state, steps };
			});
		},

		// Data management
		updatePersonalDetails: (data: Partial<PersonalDetails>) => {
			update(state => ({
				...state,
				data: {
					...state.data,
					personalDetails: { ...state.data.personalDetails, ...data }
				},
				lastSaved: new Date()
			}));
		},

		updateEmploymentDetails: (data: Partial<EmploymentDetails>) => {
			update(state => ({
				...state,
				data: {
					...state.data,
					employmentDetails: { ...state.data.employmentDetails, ...data }
				},
				lastSaved: new Date()
			}));
		},

		updateTaxInfo: (data: Partial<TaxInfo>) => {
			update(state => ({
				...state,
				data: {
					...state.data,
					taxInfo: { ...state.data.taxInfo, ...data }
				},
				lastSaved: new Date()
			}));
		},

		updateBankDetails: (data: Partial<BankDetails>) => {
			update(state => ({
				...state,
				data: {
					...state.data,
					bankDetails: { ...state.data.bankDetails, ...data }
				},
				lastSaved: new Date()
			}));
		},

		// Loading and error states
		setLoading: (loading: boolean) => {
			update(state => ({ ...state, isLoading: loading }));
		},

		setError: (error: string | null) => {
			update(state => ({ ...state, error }));
		},

		// Reset store
		reset: () => {
			set(initialState);
			if (browser) {
				localStorage.removeItem('hrims-onboarding-state');
			}
		}
	};
}

export const onboardingStore = createOnboardingStore();

// Derived stores for convenience
export const currentStep = derived(
	onboardingStore,
	($store) => $store.steps.find(step => step.id === $store.currentStepId)
);

export const completedSteps = derived(
	onboardingStore,
	($store) => $store.steps.filter(step => step.status === 'completed')
);

export const progressPercentage = derived(
	[onboardingStore, completedSteps],
	([$store, $completed]) => ($completed.length / $store.steps.length) * 100
);
