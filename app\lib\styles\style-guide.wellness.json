{"meta": {"name": "Wellness Style Guide", "file": "style-guide.wellness.json", "version": "1.0.0", "description": "Wellness programs, registration, anonymous feedback and participation stats styled with RTG palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"programCardBg": "#FFFFFF", "registerBtn": "#8C6239", "consentBg": "#FFF7ED", "feedbackAnon": "#FCF8F2", "statPositive": "#10B981"}, "gradients": {"programHero": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)"}, "components": {"programCard": {"height": 200, "thumbnailOverlay": "linear-gradient(180deg, rgba(245,214,161,0.12), rgba(196,154,108,0.12))", "registerBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}, "feedbackForm": {"anonymousToggle": {"bg": "#FFF7ED", "text": "#8C6239"}, "urgencyFlag": {"high": "#EF4444", "low": "#10B981"}}, "statsCard": {"bg": "#FFFFFF", "chartFill": "#C49A6C"}}, "interactions": {"registerFlow": {"waitlistToast": true, "autoConfirmEmail": true}}, "accessibility": {"sensitiveConsent": "Record consent choices; ensure accessible form labels"}, "notes": {"mocks": ["/api/wellness/programs", "/api/wellness/registrations"], "acceptance": ["Registration with consent recorded in mock", "Anonymous feedback is stored in mock analytics"]}}