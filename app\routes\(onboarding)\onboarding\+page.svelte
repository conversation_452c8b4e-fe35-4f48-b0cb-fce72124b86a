<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { onboardingStore, currentStep } from '$lib/stores/onboardingStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const current = $derived($currentStep);

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step - go to first onboarding step
	const handleNext = () => {
		const firstStep = store.steps.find(step => step.id === 'personal-details');
		if (firstStep && firstStep.route) {
			onboardingStore.setCurrentStep(firstStep.id);
			goto(firstStep.route);
		}
	};

	// Handle back step
	const handleBack = () => {
		if (current) {
			const currentIndex = store.steps.findIndex(s => s.id === current.id);
			if (currentIndex > 0) {
				const prevStep = store.steps[currentIndex - 1];
				onboardingStore.setCurrentStep(prevStep.id);
				goto(prevStep.route);
			}
		}
	};

	// Redirect to first step on mount
	onMount(() => {
		if (current && current.route) {
			goto(current.route);
		} else {
			// Fallback to first step if current is undefined
			const firstStep = store.steps[0];
			if (firstStep && firstStep.route) {
				onboardingStore.setCurrentStep(firstStep.id);
				goto(firstStep.route);
			}
		}
	});
</script>

<svelte:head>
	<title>Onboarding - Rainbow Tourism Group</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Welcome to Rainbow Tourism Group"
		subtitle="Let's get you set up with everything you need to start your journey with us. This process will take about 10-15 minutes to complete."
		showBackButton={false}
		showNextButton={true}
		nextButtonText="Get Started"
		onNext={handleNext}
	>
		<!-- Welcome Content -->
		<div class="space-y-6">
			<!-- Welcome Message -->
			<div class="bg-gradient-to-r from-[#F5D6A1]/10 to-[#C49A6C]/5 rounded-2xl p-6 border border-[#F5D6A1]/20">
				<h3 class="text-lg font-semibold text-[#1C1C1C] mb-3">
					🎉 Congratulations on joining our team!
				</h3>
				<p class="text-[#5C4024] leading-relaxed mb-4">
					We're excited to have you as part of the Rainbow Tourism Group family. This onboarding process will help us gather the necessary information to ensure a smooth start to your employment.
				</p>
				<div class="flex items-center gap-2 text-sm text-[#8A6A52]">
					<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
					</svg>
					Your information is secure and encrypted
				</div>
			</div>

			<!-- What to Expect -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">What to expect:</h3>
				<div class="grid gap-4">
					<div class="flex items-start gap-3">
						<div class="w-8 h-8 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
							<span class="text-[#1C1C1C] font-semibold text-sm">1</span>
						</div>
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Personal Information</h4>
							<p class="text-sm text-[#8A6A52]">Basic details and emergency contact information</p>
						</div>
					</div>
					
					<div class="flex items-start gap-3">
						<div class="w-8 h-8 bg-[#F0E1CB] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
							<span class="text-[#8A6A52] font-medium text-sm">2</span>
						</div>
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Employment & Tax Details</h4>
							<p class="text-sm text-[#8A6A52]">Job information and tax setup for payroll</p>
						</div>
					</div>
					
					<div class="flex items-start gap-3">
						<div class="w-8 h-8 bg-[#F0E1CB] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
							<span class="text-[#8A6A52] font-medium text-sm">3</span>
						</div>
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Documents & Agreements</h4>
							<p class="text-sm text-[#8A6A52]">Upload required documents and sign agreements</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Tips -->
			<div class="bg-[#FCF8F2] rounded-xl p-4 border border-[#EDE0CF]">
				<h4 class="font-medium text-[#1C1C1C] mb-2">💡 Tips for a smooth process:</h4>
				<ul class="text-sm text-[#8A6A52] space-y-1">
					<li>• Have your ID document and bank details ready</li>
					<li>• Your progress is automatically saved as you go</li>
					<li>• You can return to previous steps to make changes</li>
					<li>• Contact HR if you need any assistance</li>
				</ul>
			</div>
		</div>
	</OnboardingCard>
</div>
