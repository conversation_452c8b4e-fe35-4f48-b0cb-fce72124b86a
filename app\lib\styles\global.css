@import "tailwindcss";
@import "tw-animate-css";
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem; /* 10px - RTG standard radius */

  /* RTG Light Theme Colors */
  --background: #F9F5F0; /* RTG neutral-light */
  --foreground: #1C1C1C; /* RTG black */
  --card: #FFFFFF; /* RTG white */
  --card-foreground: #1C1C1C; /* RTG black */
  --popover: #FFFFFF; /* RTG white */
  --popover-foreground: #1C1C1C; /* RTG black */
  --primary: #F5D6A1; /* RTG primary */
  --primary-foreground: #1C1C1C; /* RTG black */
  --secondary: #FCF8F2; /* RTG muted surface */
  --secondary-foreground: #8C6239; /* RTG secondary */
  --muted: #FCF8F2; /* RTG muted surface */
  --muted-foreground: #8A6A52; /* RTG muted text */
  --accent: #C49A6C; /* RTG accent */
  --accent-foreground: #1C1C1C; /* RTG black */
  --destructive: #EF4444; /* RTG danger */
  --destructive-foreground: #FFFFFF; /* RTG white */
  --border: #EAD9BF; /* RTG card border */
  --input: #FCF8F2; /* RTG input background */
  --ring: #C49A6C; /* RTG accent for focus rings */

  /* RTG Chart colors */
  --chart-1: #F5D6A1; /* RTG primary */
  --chart-2: #C49A6C; /* RTG accent */
  --chart-3: #8C6239; /* RTG secondary */
  --chart-4: #10B981; /* RTG success */
  --chart-5: #F59E0B; /* RTG warning */

  /* RTG Sidebar colors */
  --sidebar: #FFFFFF; /* RTG neutral-dark */
  --sidebar-foreground: #FFFFFF; /* RTG white */
  --sidebar-primary: #F5D6A1; /* RTG primary */
  --sidebar-primary-foreground: #1C1C1C; /* RTG black */
  --sidebar-accent: #C49A6C; /* RTG accent */
  --sidebar-accent-foreground: #1C1C1C; /* RTG black */
  --sidebar-border: #5C4024; /* RTG secondary dark */
  --sidebar-ring: #C49A6C; /* RTG accent */

  /* RTG Legacy Variables (for backward compatibility) */
  --rtg-color-primary: #F5D6A1;
  --rtg-color-accent: #C49A6C;
  --rtg-color-secondary: #8C6239;
  --rtg-color-neutral-dark: #3B2A1A;
  --rtg-color-neutral-light: #F9F5F0;
  --rtg-color-black: #1C1C1C;
  --rtg-color-white: #FFFFFF;
  --rtg-font-family: 'Poppins', system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.dark {
  /* RTG Dark Theme Colors */
  --background: #1C1C1C; /* RTG black */
  --foreground: #F9F5F0; /* RTG neutral-light */
  --card: #2F2416; /* RTG sidebar bg-2 */
  --card-foreground: #F9F5F0; /* RTG neutral-light */
  --popover: #2F2416; /* RTG sidebar bg-2 */
  --popover-foreground: #F9F5F0; /* RTG neutral-light */
  --primary: #F5D6A1; /* RTG primary (same in dark) */
  --primary-foreground: #1C1C1C; /* RTG black */
  --secondary: #3B2A1A; /* RTG neutral-dark */
  --secondary-foreground: #F9F5F0; /* RTG neutral-light */
  --muted: #3B2A1A; /* RTG neutral-dark */
  --muted-foreground: #BFA88A; /* RTG sidebar muted */
  --accent: #C49A6C; /* RTG accent (same in dark) */
  --accent-foreground: #1C1C1C; /* RTG black */
  --destructive: #EF4444; /* RTG danger (same in dark) */
  --destructive-foreground: #FFFFFF; /* RTG white */
  --border: #5C4024; /* RTG secondary dark */
  --input: #3B2A1A; /* RTG neutral-dark */
  --ring: #C49A6C; /* RTG accent */

  /* RTG Dark Chart colors */
  --chart-1: #F5D6A1; /* RTG primary */
  --chart-2: #C49A6C; /* RTG accent */
  --chart-3: #8C6239; /* RTG secondary */
  --chart-4: #10B981; /* RTG success */
  --chart-5: #F59E0B; /* RTG warning */

  /* RTG Dark Sidebar colors */
  --sidebar: #2F2416; /* RTG sidebar bg-2 (darker) */
  --sidebar-foreground: #FFFFFF; /* RTG white */
  --sidebar-primary: #F5D6A1; /* RTG primary */
  --sidebar-primary-foreground: #1C1C1C; /* RTG black */
  --sidebar-accent: #C49A6C; /* RTG accent */
  --sidebar-accent-foreground: #1C1C1C; /* RTG black */
  --sidebar-border: #5C4024; /* RTG secondary dark */
  --sidebar-ring: #C49A6C; /* RTG accent */
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--rtg-font-family);
  }
}