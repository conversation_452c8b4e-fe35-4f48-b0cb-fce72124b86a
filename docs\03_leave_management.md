# 03 — Leave Management (Frontend-only)

Pages:
- `/leave/apply` — leave request form (type, from, to, reason, attach doc)
- `/leave/balance` — balance matrix per leave type
- `/team/leave-calendar` — calendar view for managers (show mock leaves)

Components:
- `LeaveForm.svelte` — pickers, validation, attachment
- `LeaveBalanceCard.svelte`
- `ApprovalModal.svelte` — manager action (approve/reject) with comments
- `Calendar.svelte` — month/agenda view (use fullcalendar or lightweight alternative)

Client rules & mock validations:
- Validate that `to >= from` and that requested days <= mock `remaining_balance`.
- Attachments optional for 'medical' type only.
- On submit, add to mock DB with `status='pending'`.

Workflow (frontend):
1. Employee opens `/leave/apply`, fills form, attaches file (optional) -> submit.
2. Manager (using role switcher) opens `/team/leave-calendar`, clicks request -> `ApprovalModal` -> Approve/Reject.
3. On approve, leave shows on calendars and `LeaveBalanceCard` decrements (mock logic).

Acceptance:
- calendar shows leave events in color-coded states,
- balance logic computed client-side in mock and validated.

Testing:
- Unit tests for `LeaveForm.svelte` and `Calendar.svelte`.
- End-to-end acceptance test: "apply leave" -> "manager approves" -> "balance updates".
