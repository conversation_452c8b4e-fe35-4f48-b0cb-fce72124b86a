<script lang="ts">
	// RTG-themed Input component using shadcn-svelte
	import { Input as ShadcnInput } from "./input/index.js";
	import { Label } from "./label/index.js";

	// Legacy props interface for backward compatibility
	interface LegacyProps {
		label?: string;
		error?: string;
		hint?: string;
		required?: boolean;
		fullWidth?: boolean;
		disabled?: boolean;
		class?: string;
		type?: string;
		placeholder?: string;
		value?: string;
		id?: string;
		onkeypress?: (event: KeyboardEvent) => void;
		leftIcon?: import('svelte').Snippet;
		rightIcon?: import('svelte').Snippet;
	}

	let {
		label,
		error,
		hint,
		required = false,
		fullWidth = true,
		disabled = false,
		class: className = '',
		type = 'text',
		placeholder,
		leftIcon,
		rightIcon,
		value = $bindable(),
		id,
		onkeypress
	}: LegacyProps = $props();

	const widthClass = fullWidth ? 'w-full' : '';
	const inputId = id || `input-${Math.random().toString(36).substring(2, 11)}`;

	// Add padding for icons
	const paddingClasses = leftIcon ? 'pl-10' : rightIcon ? 'pr-10' : '';
	const finalClassName = `${widthClass} ${paddingClasses} ${className}`;
</script>

<div class={fullWidth ? 'w-full' : ''}>
	{#if label}
		<Label for={inputId} class="block text-sm font-medium text-secondary-foreground mb-2">
			{label}
			{#if required}
				<span class="text-destructive ml-1">*</span>
			{/if}
		</Label>
	{/if}
	
	<div class="relative">
		{#if leftIcon}
			<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
				<div class="text-muted-foreground">
					{@render leftIcon()}
				</div>
			</div>
		{/if}
		
		<ShadcnInput
			bind:value
			class={finalClassName}
			{disabled}
			{type}
			{placeholder}
			{onkeypress}
			id={inputId}
			aria-invalid={error ? 'true' : 'false'}
			aria-describedby={error ? `${inputId}-error` : hint ? `${inputId}-hint` : undefined}
		/>
		
		{#if rightIcon}
			<div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
				<div class="text-muted-foreground">
					{@render rightIcon()}
				</div>
			</div>
		{/if}
	</div>
	
	{#if error}
		<p id="{inputId}-error" class="mt-2 text-sm text-destructive">
			{error}
		</p>
	{:else if hint}
		<p id="{inputId}-hint" class="mt-2 text-sm text-muted-foreground">
			{hint}
		</p>
	{/if}
</div>
