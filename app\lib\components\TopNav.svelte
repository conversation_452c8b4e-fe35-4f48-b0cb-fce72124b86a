<script lang="ts">
	import type { User, Employee } from '$lib/types/hr';
	import { Button } from './ui/button/index.js';
	import * as DropdownMenu from './ui/dropdown-menu/index.js';
	import ThemeToggle from './ThemeToggle.svelte';
	import { Bell, Menu, ChevronDown } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';
	import NotificationSidebar from './ui/NotificationSidebar.svelte';

	interface Props {
		user: User | null;
		employee: Employee | null;
		onToggleSidebar: () => void;
		onLogout: () => void;
	}

	let { user, employee, onToggleSidebar, onLogout }: Props = $props();

	// Notification sidebar state
	let isNotificationSidebarOpen = $state(false);

	// Get notification count
	let notificationCount = $state(0);
	$effect(() => {
		const unsubscribe = notificationStore.subscribe(notifications => {
			notificationCount = notifications.length;
		});
		return unsubscribe;
	});

	// Toggle notification sidebar
	const toggleNotificationSidebar = () => {
		isNotificationSidebarOpen = !isNotificationSidebarOpen;
	};

	// Close notification sidebar
	const closeNotificationSidebar = () => {
		isNotificationSidebarOpen = false;
	};

	const userDisplayName = employee ? `${employee.first_name} ${employee.last_name}` : user?.email || 'User';
	const userRole = user?.user_role?.replace('_', ' ').toUpperCase() || 'EMPLOYEE';
</script>

<header class="bg-card border-b border-border shadow-sm sticky top-0 z-40 rounded-bl-2xl">
	<div class="flex items-center justify-between h-16 px-4 lg:px-6">
		<!-- Left side: Logo and mobile menu button -->
		<div class="flex items-center gap-4">
			<Button
				variant="ghost"
				size="icon"
				onclick={onToggleSidebar}
				class="lg:hidden"
				aria-label="Toggle sidebar"
			>
				<Menu class="h-6 w-6" />
			</Button>

			<div class="flex items-center gap-3">
				<div class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
					<span class="text-primary-foreground font-bold text-sm">RTG</span>
				</div>
				<div class="hidden sm:block">
					<h1 class="text-lg font-semibold text-foreground">HRIMS</h1>
					<p class="text-xs text-muted-foreground -mt-1">Human Resource Information Management System</p>
				</div>
			</div>
		</div>

		<!-- Right side: User menu -->
		<div class="flex items-center gap-2">
			<!-- Theme Toggle -->
			<ThemeToggle />

			<!-- Notifications -->
			<Button
				variant="ghost"
				size="icon"
				class="relative"
				aria-label="Notifications"
				onclick={toggleNotificationSidebar}
			>
				<Bell class="h-5 w-5" />
				<!-- Notification badge with count -->
				{#if notificationCount > 0}
					<span class="absolute -top-1 -right-1 min-w-5 h-5 bg-destructive text-destructive-foreground rounded-full flex items-center justify-center text-xs font-medium px-1">
						{notificationCount > 99 ? '99+' : notificationCount}
					</span>
				{/if}
			</Button>

			<!-- User dropdown -->
			<DropdownMenu.Root>
				<DropdownMenu.Trigger>
					<Button variant="ghost" class="flex items-center gap-3 h-auto p-2">
						<div class="w-8 h-8 bg-gradient-to-br from-accent to-secondary rounded-full flex items-center justify-center">
							<span class="text-primary-foreground font-medium text-sm">
								{userDisplayName.charAt(0).toUpperCase()}
							</span>
						</div>
						<div class="hidden md:block text-left">
							<p class="text-sm font-medium text-foreground">{userDisplayName}</p>
							<p class="text-xs text-muted-foreground">{userRole}</p>
						</div>
						<ChevronDown class="w-4 h-4 text-muted-foreground" />
					</Button>
				</DropdownMenu.Trigger>
				<DropdownMenu.Content align="end" class="w-48">
					<DropdownMenu.Label>My Account</DropdownMenu.Label>
					<DropdownMenu.Separator />
					<DropdownMenu.Item>
						<a href="/profile" class="w-full">Profile Settings</a>
					</DropdownMenu.Item>
					<DropdownMenu.Item>
						<a href="/preferences" class="w-full">Preferences</a>
					</DropdownMenu.Item>
					<DropdownMenu.Separator />
					<DropdownMenu.Item onSelect={onLogout} class="text-destructive focus:text-destructive">
						Sign Out
					</DropdownMenu.Item>
				</DropdownMenu.Content>
			</DropdownMenu.Root>
		</div>
	</div>
</header>

<!-- Notification Sidebar -->
<NotificationSidebar
	isOpen={isNotificationSidebarOpen}
	onClose={closeNotificationSidebar}
/>
