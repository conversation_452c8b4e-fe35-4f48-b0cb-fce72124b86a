<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Play, Users, MapPin, Award, ChevronRight } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	let videoPlaying = false;
	let tourCompleted = false;

	const handlePlayVideo = () => {
		videoPlaying = true;
		// Simulate video completion after 3 seconds
		setTimeout(() => {
			videoPlaying = false;
			tourCompleted = true;
			uiStore.showSuccess('Welcome Video', 'Welcome video completed successfully!');
		}, 3000);
	};

	const handleNext = () => {
		// Mark step as completed and move to next
		onboardingStore.completeStep('company-intro');
		onboardingStore.setCurrentStep('personal-details');
		
		uiStore.showSuccess('Welcome Complete', 'Ready to start your onboarding journey!');
		goto('/onboarding/personal-details');
	};

	const handleBack = () => {
		onboardingStore.setCurrentStep('invite-account');
		goto('/onboarding/invite');
	};
</script>

<svelte:head>
	<title>Welcome - Rainbow Tourism Group</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && step.status === 'completed') {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Welcome to Rainbow Tourism Group!"
		subtitle="We're thrilled to have you join our team. Let's start with a quick introduction to our company and what makes us special."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Start Onboarding"
		nextButtonDisabled={!tourCompleted && !videoPlaying}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-8">
			<!-- Welcome Video Section -->
			<div class="relative">
				<div class="aspect-video bg-gradient-to-br from-[#F5D6A1] to-[#C49A6C] rounded-2xl overflow-hidden shadow-lg">
					{#if !videoPlaying}
						<div class="flex items-center justify-center h-full">
							<div class="text-center">
								<div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 backdrop-blur-sm">
									<Play class="w-10 h-10 text-white ml-1" />
								</div>
								<h3 class="text-xl font-semibold text-white mb-2">Welcome to RTG</h3>
								<p class="text-white/90 mb-4">Watch our welcome message from the CEO</p>
								<Button 
									onclick={handlePlayVideo}
									class="bg-white text-[#C49A6C] hover:bg-white/90"
								>
									<Play class="w-4 h-4 mr-2" />
									Play Welcome Video
								</Button>
							</div>
						</div>
					{:else}
						<div class="flex items-center justify-center h-full">
							<div class="text-center">
								<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
								<p class="text-white">Playing welcome video...</p>
							</div>
						</div>
					{/if}
				</div>
			</div>

			<!-- Company Overview -->
			<div class="grid md:grid-cols-2 gap-6">
				<div class="space-y-4">
					<div class="flex items-start gap-3">
						<div class="w-10 h-10 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0">
							<Users class="w-5 h-5 text-[#1C1C1C]" />
						</div>
						<div>
							<h4 class="font-semibold text-[#1C1C1C]">Our Team</h4>
							<p class="text-sm text-[#8A6A52]">Over 500+ passionate professionals across Zimbabwe, creating unforgettable experiences for our guests.</p>
						</div>
					</div>

					<div class="flex items-start gap-3">
						<div class="w-10 h-10 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0">
							<MapPin class="w-5 h-5 text-[#1C1C1C]" />
						</div>
						<div>
							<h4 class="font-semibold text-[#1C1C1C]">Our Locations</h4>
							<p class="text-sm text-[#8A6A52]">Premium hotels and lodges in Victoria Falls, Harare, Bulawayo, and the Eastern Highlands.</p>
						</div>
					</div>

					<div class="flex items-start gap-3">
						<div class="w-10 h-10 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0">
							<Award class="w-5 h-5 text-[#1C1C1C]" />
						</div>
						<div>
							<h4 class="font-semibold text-[#1C1C1C]">Our Excellence</h4>
							<p class="text-sm text-[#8A6A52]">Award-winning hospitality with a commitment to sustainable tourism and community development.</p>
						</div>
					</div>
				</div>

				<div class="bg-gradient-to-br from-[#FCF8F2] to-[#F5D6A1]/20 rounded-xl p-6 border border-[#EDE0CF]">
					<h4 class="font-semibold text-[#1C1C1C] mb-3">Our Values</h4>
					<ul class="space-y-2 text-sm text-[#8A6A52]">
						<li class="flex items-center gap-2">
							<ChevronRight class="w-4 h-4 text-[#C49A6C]" />
							Excellence in hospitality
						</li>
						<li class="flex items-center gap-2">
							<ChevronRight class="w-4 h-4 text-[#C49A6C]" />
							Respect for our heritage
						</li>
						<li class="flex items-center gap-2">
							<ChevronRight class="w-4 h-4 text-[#C49A6C]" />
							Sustainable tourism
						</li>
						<li class="flex items-center gap-2">
							<ChevronRight class="w-4 h-4 text-[#C49A6C]" />
							Community empowerment
						</li>
						<li class="flex items-center gap-2">
							<ChevronRight class="w-4 h-4 text-[#C49A6C]" />
							Innovation and growth
						</li>
					</ul>
				</div>
			</div>

			<!-- Quick Tour -->
			<div class="bg-[#FCF8F2] rounded-xl p-6 border border-[#EDE0CF]">
				<h4 class="font-semibold text-[#1C1C1C] mb-3">What's Next?</h4>
				<p class="text-sm text-[#8A6A52] mb-4">
					Your onboarding journey will guide you through setting up your profile, understanding your role, 
					and getting familiar with our systems and policies. The entire process takes about 15-20 minutes.
				</p>
				<div class="flex items-center gap-2 text-sm text-[#C49A6C]">
					<span>Ready to get started?</span>
					<ChevronRight class="w-4 h-4" />
				</div>
			</div>

			{#if tourCompleted}
				<div class="text-center p-4 bg-green-50 rounded-lg border border-green-200">
					<p class="text-green-800 font-medium">✓ Welcome tour completed!</p>
					<p class="text-sm text-green-600">You're ready to continue with your onboarding.</p>
				</div>
			{/if}
		</div>
	</OnboardingCard>
</div>
