<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import Card from '$lib/components/ui/Card.svelte';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import { Users, Plus, Search, Filter, MoreVertical, Edit, Trash2, Eye } from '@lucide/svelte';
	import { goto } from '$app/navigation';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Search and filter state
	let searchQuery = $state('');
	let selectedDepartment = $state('all');
	let selectedStatus = $state('all');

	// Mock employee data
	const employees = [
		{
			id: 1,
			employee_number: 'RTG001',
			first_name: '<PERSON>',
			last_name: 'Do<PERSON>',
			email: '<EMAIL>',
			position: 'Software Developer',
			department: 'IT',
			status: 'Active',
			hire_date: '2022-01-15',
			phone: '+263 77 123 4567'
		},
		{
			id: 2,
			employee_number: 'RTG002',
			first_name: '<PERSON>',
			last_name: '<PERSON>',
			email: '<EMAIL>',
			position: 'HR Manager',
			department: 'Human Resources',
			status: 'Active',
			hire_date: '2021-03-10',
			phone: '+263 77 234 5678'
		},
		{
			id: 3,
			employee_number: 'RTG003',
			first_name: 'Mike',
			last_name: 'Johnson',
			email: '<EMAIL>',
			position: 'Marketing Specialist',
			department: 'Marketing',
			status: 'Active',
			hire_date: '2023-06-20',
			phone: '+263 77 345 6789'
		},
		{
			id: 4,
			employee_number: 'RTG004',
			first_name: 'Emily',
			last_name: 'Brown',
			email: '<EMAIL>',
			position: 'Accountant',
			department: 'Finance',
			status: 'On Leave',
			hire_date: '2022-09-05',
			phone: '+263 77 456 7890'
		}
	];

	const departments = ['All', 'IT', 'Human Resources', 'Marketing', 'Finance', 'Operations'];
	const statuses = ['All', 'Active', 'On Leave', 'Inactive'];

	// Filtered employees
	const filteredEmployees = $derived(
		employees.filter(employee => {
			const matchesSearch = searchQuery === '' || 
				employee.first_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				employee.last_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				employee.employee_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
				employee.email.toLowerCase().includes(searchQuery.toLowerCase());
			
			const matchesDepartment = selectedDepartment === 'all' || 
				employee.department.toLowerCase() === selectedDepartment.toLowerCase();
			
			const matchesStatus = selectedStatus === 'all' || 
				employee.status.toLowerCase() === selectedStatus.toLowerCase();
			
			return matchesSearch && matchesDepartment && matchesStatus;
		})
	);

	const getStatusColor = (status: string) => {
		switch (status.toLowerCase()) {
			case 'active': return 'text-green-600 bg-green-100';
			case 'on leave': return 'text-yellow-600 bg-yellow-100';
			case 'inactive': return 'text-red-600 bg-red-100';
			default: return 'text-gray-600 bg-gray-100';
		}
	};

	const handleAddEmployee = () => {
		goto('/employees/create');
	};

	const handleViewEmployee = (employeeId: number) => {
		goto(`/employees/${employeeId}`);
	};

	const handleEditEmployee = (employeeId: number) => {
		goto(`/employees/${employeeId}/edit`);
	};

	const handleDeleteEmployee = (employeeId: number) => {
		// Show confirmation dialog and delete
		console.log('Delete employee:', employeeId);
	};
</script>

<svelte:head>
	<title>Employees - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Employees</h1>
			<p class="text-muted-foreground">Manage your organization's employees</p>
		</div>
		{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
			<Button variant="primary" onclick={handleAddEmployee}>
				{#snippet children()}
					<Plus class="w-4 h-4" />
					Add Employee
				{/snippet}
			</Button>
		{/if}
	</div>

	<!-- Filters and Search -->
	<Card variant="default" padding="md">
		{#snippet children()}
			<div class="flex flex-col md:flex-row gap-4">
				<div class="flex-1">
					<Input
						placeholder="Search employees..."
						bind:value={searchQuery}
						class="w-full"
					>
						{#snippet leftIcon()}
							<Search class="w-4 h-4 text-muted-foreground" />
						{/snippet}
					</Input>
				</div>
				<div class="flex gap-2">
					<select 
						bind:value={selectedDepartment}
						class="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
					>
						{#each departments as dept}
							<option value={dept.toLowerCase()}>{dept}</option>
						{/each}
					</select>
					<select 
						bind:value={selectedStatus}
						class="px-3 py-2 border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
					>
						{#each statuses as status}
							<option value={status.toLowerCase()}>{status}</option>
						{/each}
					</select>
				</div>
			</div>
		{/snippet}
	</Card>

	<!-- Employee Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">{employees.length}</div>
					<div class="text-sm text-muted-foreground">Total Employees</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-green-600 mb-1">
						{employees.filter(e => e.status === 'Active').length}
					</div>
					<div class="text-sm text-muted-foreground">Active</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-yellow-600 mb-1">
						{employees.filter(e => e.status === 'On Leave').length}
					</div>
					<div class="text-sm text-muted-foreground">On Leave</div>
				</div>
			{/snippet}
		</Card>
		<Card variant="kpi" padding="md">
			{#snippet children()}
				<div class="text-center">
					<div class="text-2xl font-bold text-foreground mb-1">
						{new Set(employees.map(e => e.department)).size}
					</div>
					<div class="text-sm text-muted-foreground">Departments</div>
				</div>
			{/snippet}
		</Card>
	</div>

	<!-- Employee List -->
	<Card variant="default" padding="none">
		{#snippet children()}
			<div class="overflow-x-auto">
				<table class="w-full">
					<thead class="bg-muted">
						<tr>
							<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Employee
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Position
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Department
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Status
							</th>
							<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Hire Date
							</th>
							{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
								<th class="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
									Actions
								</th>
							{/if}
						</tr>
					</thead>
					<tbody class="bg-background divide-y divide-border">
						{#each filteredEmployees as employee}
							<tr class="hover:bg-muted/50 transition-colors">
								<td class="px-6 py-4 whitespace-nowrap">
									<div class="flex items-center">
										<div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mr-3">
											<span class="text-sm font-medium text-primary-foreground">
												{employee.first_name.charAt(0)}{employee.last_name.charAt(0)}
											</span>
										</div>
										<div>
											<div class="text-sm font-medium text-foreground">
												{employee.first_name} {employee.last_name}
											</div>
											<div class="text-sm text-muted-foreground">
												{employee.employee_number}
											</div>
										</div>
									</div>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-foreground">
									{employee.position}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-foreground">
									{employee.department}
								</td>
								<td class="px-6 py-4 whitespace-nowrap">
									<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {getStatusColor(employee.status)}">
										{employee.status}
									</span>
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
									{new Date(employee.hire_date).toLocaleDateString()}
								</td>
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex items-center justify-end gap-2">
										<button
											onclick={() => handleViewEmployee(employee.id)}
											class="text-muted-foreground hover:text-foreground transition-colors"
											title="View Employee"
										>
											<Eye class="w-4 h-4" />
										</button>
										{#if user?.user_role === 'hr_admin' || user?.user_role === 'super_admin'}
											<button
												onclick={() => handleEditEmployee(employee.id)}
												class="text-primary hover:text-primary/80 transition-colors"
												title="Edit Employee"
											>
												<Edit class="w-4 h-4" />
											</button>
											<button
												onclick={() => handleDeleteEmployee(employee.id)}
												class="text-destructive hover:text-destructive/80 transition-colors"
												title="Delete Employee"
											>
												<Trash2 class="w-4 h-4" />
											</button>
										{/if}
									</div>
								</td>
							</tr>
						{/each}
					</tbody>
				</table>
			</div>
		{/snippet}
	</Card>
</div>
