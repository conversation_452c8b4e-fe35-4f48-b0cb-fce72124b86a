{"meta": {"name": "Employee Lifecycle Style Guide", "file": "style-guide.employee-lifecycle.json", "version": "1.0.0", "description": "Design tokens specific to requisitions, candidates, interview scheduler and onboarding checklist using RTG palette and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"primary": "#F5D6A1", "accent": "#C49A6C", "approveGreen": "#10B981", "rejectRed": "#EF4444", "rowHover": "#FCF8F2", "badgeShortlist": "#FFF7ED"}, "gradients": {"requisitionHero": "linear-gradient(135deg,#F5D6A1 0%, #C49A6C 100%)", "interviewSelected": "linear-gradient(90deg,#C49A6C 0%, #8C6239 100%)"}, "layout": {"twoColumn": {"left": "66%", "right": "34%", "gap": 20}, "maxListWidth": 840}, "components": {"requisitionForm": {"card": {"bg": "#FFFFFF", "border": "1px solid #EAD9BF", "radius": 12}, "stepper": {"activeColor": "#C49A6C", "inactiveColor": "#FFF7ED"}, "primaryBtn": {"bg": "linear-gradient(90deg,#F5D6A1,#C49A6C)", "text": "#1C1C1C"}}, "candidateRow": {"bg": "#FFFFFF", "avatar": {"size": 56, "border": "2px solid #FFFFFF"}, "metaText": {"color": "#5C4024"}, "shortlistBadge": {"bg": "#FFF7ED", "text": "#8C6239"}}, "cvViewer": {"toolbarBg": "#FCF8F2", "downloadBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}, "onboardingChecklist": {"taskRow": {"checkbox": "#C49A6C", "assignedBadge": "#F5D6A1"}, "progressBar": {"bg": "#FCF8F2", "fill": "linear-gradient(90deg,#F5D6A1,#C49A6C)"}}}, "interactions": {"scheduler": {"slotHover": {"bg": "rgba(196,154,108,0.06)", "cursor": "pointer"}, "confirmationToast": {"bg": "#E8F9EE"}}, "shortlistAnimation": {"type": "slide-right", "durationMs": 180}}, "accessibility": {"aria": {"cvViewerRole": "document", "schedulerRole": "application"}, "keyboard": "Calendar slots reachable via arrow keys; Enter selects a slot"}, "tailwindSnippet": {"code": ".candidate-badge { @apply bg-rtg-primary text-rtg-secondary-dark rounded px-2 py-1; }"}, "notes": {"mocks": ["/api/requisitions", "/api/candidates", "/api/interviews", "/api/onboarding"], "acceptance": ["Requisition wizard & validations implemented", "Candidate CV preview uses preview gradient header", "Onboarding checklist persists in mock store and shows progress"]}}