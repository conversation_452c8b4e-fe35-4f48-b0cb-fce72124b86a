<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { authStore } from '$lib/stores/authStore';
	import { uiStore } from '$lib/stores/uiStore';
	import AppShell from '$lib/components/AppShell.svelte';

	let { children } = $props();

	// Subscribe to auth state
	const auth = $derived($authStore);

	// Handle authentication for business routes
	$effect(() => {
		if (typeof window !== 'undefined' && !auth.isLoading) {
			if (!auth.isAuthenticated) {
				goto('/auth/login');
			}
		}
	});

	const handleLogout = () => {
		authStore.logout();
		uiStore.showInfo('Logged Out', 'You have been successfully logged out.');
		goto('/auth/login');
	};
</script>

<svelte:head>
	<title>Dashboard - HRIMS</title>
	<meta name="description" content="Rainbow Tourism Group HRIMS Dashboard - Comprehensive HR management solution" />
</svelte:head>

{#if auth.isLoading}
	<!-- Loading state while checking authentication -->
	<div class="min-h-screen bg-background flex items-center justify-center">
		<div class="text-center">
			<div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
				<span class="text-primary-foreground font-bold text-2xl">RTG</span>
			</div>
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
			<p class="text-muted-foreground mt-4">Loading...</p>
		</div>
	</div>
{:else if auth.isAuthenticated}
	<AppShell user={auth.user} employee={auth.employee} onLogout={handleLogout}>
		{#snippet children()}
			{@render children?.()}
		{/snippet}
	</AppShell>
{:else}
	<!-- This should not be reached due to the redirect effect, but just in case -->
	<div class="min-h-screen bg-background flex items-center justify-center">
		<div class="text-center">
			<p class="text-muted-foreground">Redirecting to login...</p>
		</div>
	</div>
{/if}
