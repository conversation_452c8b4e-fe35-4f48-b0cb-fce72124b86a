<script lang="ts">
	// RTG-themed Button component using shadcn-svelte
	import { Button as ShadcnButton } from "./button/index.js";
	import type { ButtonProps } from "./button/button.svelte";

	// Legacy props interface for backward compatibility
	interface LegacyProps {
		variant?: 'primary' | 'secondary' | 'tertiary' | 'danger';
		size?: 'sm' | 'md' | 'lg';
		loading?: boolean;
		fullWidth?: boolean;
		disabled?: boolean;
		class?: string;
		type?: 'button' | 'submit' | 'reset';
		onclick?: () => void;
		children: import('svelte').Snippet;
	}

	let {
		variant = 'primary',
		size = 'md',
		loading = false,
		fullWidth = false,
		disabled = false,
		class: className = '',
		type = 'button',
		onclick,
		children
	}: LegacyProps = $props();

	// Map legacy variants to shadcn variants
	const variantMap = {
		primary: 'default',
		secondary: 'outline',
		tertiary: 'ghost',
		danger: 'destructive'
	} as const;

	// Map legacy sizes to shadcn sizes
	const sizeMap = {
		sm: 'sm',
		md: 'default',
		lg: 'lg'
	} as const;

	const mappedVariant = variantMap[variant] as ButtonProps['variant'];
	const mappedSize = sizeMap[size] as ButtonProps['size'];
	const widthClass = fullWidth ? 'w-full' : '';
	const finalClassName = `${widthClass} ${className}`;
</script>

<ShadcnButton
	variant={mappedVariant}
	size={mappedSize}
	disabled={disabled || loading}
	class={finalClassName}
	{type}
	{onclick}
>
	{#if loading}
		<svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
			<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
			<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
		</svg>
	{/if}
	{@render children()}
</ShadcnButton>
