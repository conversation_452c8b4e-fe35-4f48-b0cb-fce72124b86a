<script lang="ts">
	// Enhanced Toast component with accessibility and animations
	import { fly, scale } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { CheckCircle, AlertCircle, XCircle, Info, X, Clock } from '@lucide/svelte';
	import { Button } from './button/index.js';
	import type { EnhancedNotification } from '$lib/stores/notificationStore';
	import { onMount } from 'svelte';

	interface Props {
		notification: EnhancedNotification;
		onRemove: (id: string) => void;
		position?: EnhancedNotification['position'];
		enableAnimations?: boolean;
	}

	let {
		notification,
		onRemove,
		position = 'top-right',
		enableAnimations = true
	}: Props = $props();

	let progressElement = $state<HTMLDivElement>();
	let toastElement = $state<HTMLDivElement>();
	let timeoutId: number | undefined;
	let progressTimeoutId: number | undefined;
	let isHovered = $state(false);
	let isPaused = $state(false);
	let remainingTime = $state(notification.duration || 0);
	let startTime = $state(Date.now());

	// Icon mapping
	const getIcon = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return CheckCircle;
			case 'error': return XCircle;
			case 'warning': return AlertCircle;
			case 'info': return Info;
		}
	};

	// RTG-themed styling
	const getTypeClasses = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success':
				return 'bg-green-50 border-green-200 text-green-900 dark:bg-green-900/20 dark:border-green-700 dark:text-green-100';
			case 'error':
				return 'bg-red-50 border-red-200 text-red-900 dark:bg-red-900/20 dark:border-red-700 dark:text-red-100';
			case 'warning':
				return 'bg-yellow-50 border-yellow-200 text-yellow-900 dark:bg-yellow-900/20 dark:border-yellow-700 dark:text-yellow-100';
			case 'info':
				return 'bg-blue-50 border-blue-200 text-blue-900 dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-100';
		}
	};

	const getIconClasses = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return 'text-green-600 dark:text-green-400';
			case 'error': return 'text-red-600 dark:text-red-400';
			case 'warning': return 'text-yellow-600 dark:text-yellow-400';
			case 'info': return 'text-blue-600 dark:text-blue-400';
		}
	};

	const getProgressClasses = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return 'bg-green-500';
			case 'error': return 'bg-red-500';
			case 'warning': return 'bg-yellow-500';
			case 'info': return 'bg-blue-500';
		}
	};

	// Animation configuration based on position
	const getAnimationConfig = () => {
		if (!enableAnimations) return { x: 0, y: 0, duration: 0 };
		
		switch (position) {
			case 'top-right':
				return { x: 300, y: -50, duration: 400 };
			case 'top-left':
				return { x: -300, y: -50, duration: 400 };
			case 'bottom-right':
				return { x: 300, y: 50, duration: 400 };
			case 'bottom-left':
				return { x: -300, y: 50, duration: 400 };
			case 'top-center':
				return { x: 0, y: -100, duration: 400 };
			case 'bottom-center':
				return { x: 0, y: 100, duration: 400 };
			default:
				return { x: 300, y: -50, duration: 400 };
		}
	};

	// Auto-dismiss logic with pause/resume
	const startTimer = () => {
		if (!notification.duration || notification.duration <= 0) return;
		
		startTime = Date.now();
		timeoutId = setTimeout(() => {
			if (!isPaused) {
				onRemove(notification.id);
			}
		}, remainingTime);
	};

	const pauseTimer = () => {
		if (timeoutId) {
			clearTimeout(timeoutId);
			const elapsed = Date.now() - startTime;
			remainingTime = Math.max(0, remainingTime - elapsed);
			isPaused = true;
		}
	};

	const resumeTimer = () => {
		if (isPaused && remainingTime > 0) {
			isPaused = false;
			startTimer();
		}
	};

	// Progress bar animation
	const startProgressAnimation = () => {
		if (!notification.showProgress || !notification.duration || notification.duration <= 0) return;
		
		if (progressElement) {
			progressElement.style.transition = `width ${remainingTime}ms linear`;
			progressElement.style.width = '0%';
		}
	};

	// Handle mouse interactions
	const handleMouseEnter = () => {
		isHovered = true;
		pauseTimer();
	};

	const handleMouseLeave = () => {
		isHovered = false;
		resumeTimer();
		startProgressAnimation();
	};

	// Handle keyboard interactions
	const handleKeydown = (event: KeyboardEvent) => {
		if (event.key === 'Escape' && notification.dismissible) {
			onRemove(notification.id);
		}
	};

	// Handle dismiss
	const handleDismiss = () => {
		if (notification.dismissible) {
			onRemove(notification.id);
		}
	};

	// Initialize timer and progress on mount
	onMount(() => {
		if (notification.duration && notification.duration > 0) {
			startTimer();
			startProgressAnimation();
		}
		
		// Focus management for accessibility
		if (toastElement && notification.priority === 'critical') {
			toastElement.focus();
		}
		
		return () => {
			if (timeoutId) clearTimeout(timeoutId);
			if (progressTimeoutId) clearTimeout(progressTimeoutId);
		};
	});

	// Priority-based styling
	const getPriorityClasses = () => {
		switch (notification.priority) {
			case 'critical':
				return 'ring-2 ring-red-500 ring-opacity-50 shadow-lg';
			case 'high':
				return 'shadow-md';
			case 'normal':
				return 'shadow-sm';
			case 'low':
				return 'shadow-sm opacity-90';
			default:
				return 'shadow-sm';
		}
	};

	const IconComponent = getIcon(notification.type);
</script>

<!-- Toast Container -->
<div
	bind:this={toastElement}
	class="relative pointer-events-auto border rounded-lg p-4 max-w-sm w-full {getTypeClasses(notification.type)} {getPriorityClasses()}"
	transition:fly={getAnimationConfig()}
	role="status"
	aria-live={notification.priority === 'critical' ? 'assertive' : 'polite'}
	aria-atomic="true"
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
>
	<!-- Progress Bar -->
	{#if notification.showProgress && notification.duration && notification.duration > 0}
		<div class="absolute top-0 left-0 right-0 h-1 bg-black/10 rounded-t-lg overflow-hidden">
			<div
				bind:this={progressElement}
				class="h-full {getProgressClasses(notification.type)} transition-all"
				style="width: 100%"
			></div>
		</div>
	{/if}

	<div class="flex items-start gap-3">
		<!-- Icon -->
		<IconComponent class="w-5 h-5 {getIconClasses(notification.type)} flex-shrink-0 mt-0.5" />

		<!-- Content -->
		<div class="flex-1 min-w-0">
			<h4 class="text-sm font-semibold">
				{notification.title}
			</h4>
			
			{#if notification.message}
				<p class="text-sm opacity-90 mt-1">
					{notification.message}
				</p>
			{/if}

			<!-- Rich Content -->
			{#if notification.richContent}
				<div class="mt-2">
					{#if notification.richContent.html}
						{@html notification.richContent.html}
					{:else if notification.richContent.component}
						{@const Component = notification.richContent.component}
						<Component {...notification.richContent.props || {}} />
					{/if}
				</div>
			{/if}

			<!-- Action Button -->
			{#if notification.action}
				<div class="mt-3">
					<Button
						variant="ghost"
						size="sm"
						onclick={notification.action.onClick}
						class="text-current hover:bg-current/10 p-2 h-auto text-xs font-medium"
					>
						{notification.action.label}
					</Button>
				</div>
			{/if}

			<!-- Timestamp for critical notifications -->
			{#if notification.priority === 'critical'}
				<div class="flex items-center gap-1 mt-2 text-xs opacity-70">
					<Clock class="w-3 h-3" />
					<span>{new Date(notification.timestamp).toLocaleTimeString()}</span>
				</div>
			{/if}
		</div>

		<!-- Close Button -->
		{#if notification.dismissible}
			<Button
				variant="ghost"
				size="icon"
				onclick={handleDismiss}
				class="h-6 w-6 text-current hover:bg-current/10 flex-shrink-0"
				aria-label="Close notification"
			>
				<X class="h-3 w-3" />
			</Button>
		{/if}
	</div>

	<!-- Pause indicator -->
	{#if isPaused && isHovered}
		<div class="absolute top-2 right-8 text-xs opacity-60">
			Paused
		</div>
	{/if}
</div>
