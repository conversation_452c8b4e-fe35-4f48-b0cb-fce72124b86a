Love it — Drizzle it is. ✅

Short answer first: **Yes — we’ll use <PERSON><PERSON><PERSON>** as your TypeScript ORM (you preferred it) and I’ll show exactly how to wire it into the project (migrations, schema files, migration workflow, and example DB connection code). Also: **you don’t have to use `src/`** — SvelteKit lets you change the app folders via `svelte.config` so I’ll give a safe config that uses `app/` instead of `src/`. I checked the official docs and community guidance for both Drizzle and SvelteKit while preparing this. ([Drizzle ORM][1], [Svelte][2])

Below you’ll find everything you asked for (concise, copy/paste-ready):

1. quick rationale + citations
2. exact install & config commands
3. Drizzle config & `drizzle-kit` migration setup (files + commands)
4. a ready-to-drop `db/schema.ts` (Drizzle schema) for Employees / Contracts / Documents + indices & relations (complete & optimized)
5. example DB connection util and usage in Edge Functions / server code
6. notes about RLS & where to keep RLS SQL (how to apply them with Drizzle migrations)
7. SvelteKit config to avoid `src/` and use `app/` instead (plus caveats) with exact file example
8. updated folder structure (no `src/`) and where to put `db/` files (so no `src/` at all)
9. an immediate next-step checklist you can run now

---

## 1) Rationale (short)

* **Drizzle** is a modern, TypeScript-first ORM with great compile-time typing and a `drizzle-kit` CLI for migrations — ideal for a Postgres-backed Supabase project. I used the official Drizzle + Supabase docs to build these artifacts. ([Drizzle ORM][1])
* **SvelteKit** supports changing the default folders (so you can avoid `src/`); we’ll show how to set `kit.files` so your app uses `app/` instead. ([Svelte][2])

---

## 2) Install & quick setup (run in repo root)

```bash
# 1) add drizzle & tools
npm install drizzle-orm pg
# or (preferred) the postgres driver:
npm install drizzle-orm postgres

# drizzle-kit for migrations (dev)
npm i -D drizzle-kit tsx

# also dotenv to manage envs
npm i dotenv
```

(Drizzle official setup + drizzle-kit commands are documented). ([Drizzle ORM][3])

Create `.env` with your DB string (for local dev / migration runs):

```
DATABASE_URL="postgres://user:pass@localhost:5432/your_db"
```

---

## 3) `drizzle.config.ts` (root of repo) — required by drizzle-kit

Create `drizzle.config.ts` at repo root:

```ts
// drizzle.config.ts
import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  schema: './db/schema.ts',          // our Drizzle schema (TypeScript)
  out: './drizzle',                  // generated SQL migration files
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL
  }
});
```

(You can also use `drizzle-kit migrate`, `generate`, `push`, `pull` commands). ([Drizzle ORM][4])

---

## 4) Drizzle schema file (put at `db/schema.ts` — **no** `src/` required)

This `db/schema.ts` covers `organizations`, `departments`, `employees`, `contracts`, `documents`, `audit_logs`. It uses UUID PKs, appropriate indexes, not-null constraints and foreign key actions matching the SQL we designed earlier.

Create `db/schema.ts`:

```ts
// db/schema.ts
import {
  pgTable,
  text,
  uuid,
  timestamp,
  integer,
  numeric,
  boolean,
  json,
  varchar,
  index,
  foreignKey
} from 'drizzle-orm/pg-core';

// organizations
export const organizations = pgTable('hr_organizations', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull(),
  slug: text('slug'),
  metadata: json('metadata'),
  created_at: timestamp('created_at').defaultNow()
});

// departments
export const departments = pgTable(
  'hr_departments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    organization_id: uuid('organization_id').notNull(),
    name: text('name').notNull(),
    code: text('code'),
    manager_employee_id: uuid('manager_employee_id'),
    created_at: timestamp('created_at').defaultNow()
  },
  (table) => ({
    org_idx: index('idx_departments_org').on(table.organization_id)
  })
);

// employees
export const employees = pgTable(
  'hr_employees',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    organization_id: uuid('organization_id').notNull(),
    auth_user_id: uuid('auth_user_id'),
    employee_number: text('employee_number'),
    first_name: text('first_name').notNull(),
    last_name: text('last_name').notNull(),
    email: text('email').notNull(),
    phone: text('phone'),
    job_title: text('job_title'),
    grade: text('grade'),
    department_id: uuid('department_id'),
    manager_id: uuid('manager_id'),
    hire_date: timestamp('hire_date'),
    termination_date: timestamp('termination_date'),
    status: text('status'),           // you can map to enum at SQL level with custom migration
    probation_end_date: timestamp('probation_end_date'),
    date_of_birth: timestamp('date_of_birth'),
    gender: text('gender'),
    metadata: json('metadata'),
    created_by: uuid('created_by'),
    created_at: timestamp('created_at').defaultNow(),
    updated_at: timestamp('updated_at').defaultNow()
  },
  (table) => ({
    org_idx: index('idx_employees_org').on(table.organization_id),
    auth_idx: index('idx_employees_auth').on(table.auth_user_id)
  })
);

// contracts
export const contracts = pgTable(
  'hr_contracts',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    organization_id: uuid('organization_id').notNull(),
    employee_id: uuid('employee_id').notNull(),
    contract_type: text('contract_type').notNull(),
    start_date: timestamp('start_date').notNull(),
    end_date: timestamp('end_date'),
    probation_days: integer('probation_days').default(90),
    salary: numeric('salary'),
    currency: text('currency').default('USD'),
    signed: boolean('signed').default(false),
    signed_at: timestamp('signed_at'),
    signed_by: uuid('signed_by'),
    storage_path: text('storage_path'),
    storage_bucket: text('storage_bucket'),
    file_size: integer('file_size'),
    file_checksum: text('file_checksum'),
    version: integer('version').default(1),
    is_active: boolean('is_active').default(true),
    retention_until: timestamp('retention_until'),
    created_by: uuid('created_by'),
    created_at: timestamp('created_at').defaultNow(),
    updated_at: timestamp('updated_at').defaultNow()
  },
  (table) => ({
    emp_idx: index('idx_contracts_emp').on(table.employee_id)
  })
);

// documents
export const documents = pgTable(
  'hr_documents',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    organization_id: uuid('organization_id').notNull(),
    employee_id: uuid('employee_id'),
    contract_id: uuid('contract_id'),
    type: text('type').notNull(),
    title: text('title'),
    storage_bucket: text('storage_bucket').notNull(),
    storage_path: text('storage_path').notNull(),
    mime_type: text('mime_type'),
    file_size: integer('file_size'),
    uploaded_by: uuid('uploaded_by'),
    uploaded_at: timestamp('uploaded_at').defaultNow(),
    status: text('status').default('pending_scan'),
    scanned_at: timestamp('scanned_at'),
    quarantine_reason: text('quarantine_reason'),
    tags: json('tags'),
    expires_at: timestamp('expires_at'),
    retention_until: timestamp('retention_until'),
    access_level: text('access_level').default('private'),
    metadata: json('metadata'),
    created_at: timestamp('created_at').defaultNow(),
    updated_at: timestamp('updated_at').defaultNow()
  },
  (table) => ({
    emp_idx: index('idx_documents_emp').on(table.employee_id),
    status_idx: index('idx_documents_status').on(table.status)
  })
);

// audit logs
export const audit_logs = pgTable('hr_audit_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  actor_id: uuid('actor_id'),
  actor_role: text('actor_role'),
  action: text('action').notNull(),
  target_table: text('target_table'),
  target_id: text('target_id'),
  payload: json('payload'),
  ip: text('ip'),
  user_agent: text('user_agent'),
  created_at: timestamp('created_at').defaultNow()
});
```

**Notes on the schema file above**

* I used `uuid(...).defaultRandom().primaryKey()` style (Drizzle supports `uuid()` and `.defaultRandom()` for `gen_random_uuid()`). If your DB requires `pgcrypto` extension, enable it via a SQL migration (explained below). ([GitHub][5], [Stack Overflow][6])
* This file is placed at `db/schema.ts` so you avoid `src/`. Drizzle and drizzle-kit accept relative paths to the schema. ([Drizzle ORM][4])

---

## 5) Generating migrations & running them

Basic workflow (after creating `drizzle.config.ts` and `db/schema.ts`):

```bash
# generate SQL migration from TS schema
npx drizzle-kit generate --name init_hrims

# run migrations against DATABASE_URL
npx drizzle-kit migrate
```

If you need to write SQL for RLS policies (next section), create a custom migration file in the `drizzle/` output folder (drizzle-kit supports `--custom`) and place the RLS SQL there (you can commit it along with generated migrations). ([Drizzle ORM][4])

---

## 6) DB connection util (example) — keep under `db/` (no `src`)

Create `db/client.ts`:

```ts
// db/client.ts
import 'dotenv/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// use postgres.js driver
const sql = postgres(process.env.DATABASE_URL || '', { ssl: 'require' });

// export typed Drizzle DB
export const db = drizzle(sql);
```

Use `db` in Edge Functions or server code to perform queries:

```ts
import { db } from '../../db/client';
import { employees } from '../../db/schema';

// example
const rows = await db.select().from(employees).limit(10);
```

Drizzle docs show how to connect using `postgres` or `node-postgres`. Pick `postgres` for compatibility with serverless environments. ([Drizzle ORM][7])

---

## 7) RLS (Row Level Security) — where to keep the SQL policies

* **Why**: RLS must be created with SQL on the database side (Drizzle does not auto-generate RLS policies).
* **How**: Create a custom SQL migration using drizzle-kit (or a plain `.sql` migration) that runs the `ALTER TABLE ... ENABLE ROW LEVEL SECURITY` and `CREATE POLICY` statements we designed earlier.
* **Where**: Put those SQL statements in a migration file in `drizzle/` (drizzle-kit `out` folder) — either auto-generated plus a manual `.sql` or create a `drizzle/0002_rls.sql` with the policy SQL.

Example custom migration `drizzle/0002_rls.sql` (snippet):

```sql
-- enable rls
ALTER TABLE hr_employees ENABLE ROW LEVEL SECURITY;
-- create policy examples (see your previous SQL for full policies)
CREATE POLICY employees_select_hr_admin ON hr_employees FOR SELECT USING (
  (current_setting('request.jwt.claims', true)::jsonb ->> 'user_role') in ('hr','admin')
);
```

**Important:** Drizzle migrations can include custom SQL files — use `drizzle-kit generate --custom --name rls_policies` if you want an empty migration ready to paste SQL into. ([Drizzle ORM][8])

---

## 8) SvelteKit: avoid `src/` — use `app/` instead

You said you don't prefer `src/`. SvelteKit **allows changing the directories** via `config.kit.files` — you can map `routes`, `lib`, `assets`, etc. to a different folder. Example `svelte.config.ts` (repo root):

```ts
// svelte.config.ts
import adapter from '@sveltejs/adapter-auto';
import preprocess from 'svelte-preprocess';
/** @type {import('@sveltejs/kit').Config} */
const config = {
  preprocess: preprocess(),
  kit: {
    adapter: adapter(),
    files: {
      routes: 'app/routes',   // instead of src/routes
      lib: 'app/lib',        // instead of src/lib
      assets: 'app/assets',  // static-like directory
      appTemplate: 'app/app.html', // optional - template location
    }
  }
};
export default config;
```

**Caveats & notes**

* Some tooling assumes `src/` (editor snippets, examples). Run `npm run dev` to let SvelteKit generate aliases (it writes to `jsconfig.json/tsconfig.json` as needed). ([Svelte][2])
* Keep consistent naming (I used `app/` as top-level folder in examples). If you prefer `client/` or `frontend/`, substitute the names above.
* If you later use third-party templates or starters they might assume `src/`; you’ll need to adapt them (usually a simple path config change).

---

## 9) Updated recommended folder structure (no `src/`)

```
/ (repo root)
├─ package.json
├─ svelte.config.ts
├─ drizzle.config.ts
├─ .env
├─ app/                      # replacing src/
│  ├─ app.html
│  ├─ lib/
│  │  ├─ components/
│  │  ├─ stores/
│  │  └─ styles/
│  └─ routes/
│     ├─ +layout.svelte
│     ├─ +page.svelte
│     └─ employees/
│        └─ +page.svelte
├─ db/
│  ├─ schema.ts              # Drizzle schema
│  ├─ client.ts              # drizzle client (connection)
│  └─ migrations/            # optional local SQL migrations
├─ drizzle/                  # drizzle-kit output (SQL migration files)
├─ mocks/
├─ tests/
└─ README.md
```

This keeps the repo free of `src/` while staying fully compatible with SvelteKit configuration. ([Svelte][9], [Joy of Code][10])

---

## 10) Using Drizzle in Edge Functions (example pattern)

Edge functions / server code should import the same `db/client.ts` to run safe server-side queries.

Example `functions/some-edge-function.ts` (deploy later):

```ts
import { db } from '../../db/client';
import { documents } from '../../db/schema';

export async function handler(event) {
  // server-side queries with Drizzle
  const doc = await db.select().from(documents).where(documents.storage_path.eq('some/path')).limit(1);
  // ...
}
```

Keep `SUPABASE_SERVICE_ROLE_KEY` and DATABASE\_URL in function secrets — do not put them in client bundles.

---

## 11) Notes, gotchas & best-practices (actionable)

* **Enable `pgcrypto`** extension if you use `gen_random_uuid()` (create this in an early migration). Example SQL in a migration:

```sql
create extension if not exists "pgcrypto";
```

This makes `defaultRandom()` behave as expected. ([Stack Overflow][6])

* **Foreign keys / circular refs**: Drizzle supports `references(() => otherTable.id)` and also standalone `foreignKey` helper for mutual/circular relationships. If you define manager\_id -> employees.id (self-reference), use the documented pattern. ([Stack Overflow][11], [Drizzle ORM][12])

* **SQL for RLS**: Keep RLS policies in SQL migrations (drizzle-kit custom migrations). Drizzle currently doesn’t auto-generate RLS policies — they’re DB-level constructs. ([Drizzle ORM][4])

* **Migrations strategy**: Use `drizzle-kit generate` frequently, commit SQL output to `drizzle/` and use `npx drizzle-kit migrate` in CI. There’s also `drizzle-kit push` & `pull` workflows if you prefer DB-first. ([Drizzle ORM][13])

* **Type-safety**: Once `db/schema.ts` exists, Drizzle generates strong types for queries — use them in Edge Functions for safe refactors.

---

## 12) Immediate checklist you can run now (one-shot)

1. `npm install drizzle-orm postgres drizzle-kit tsx dotenv`
2. create `.env` with `DATABASE_URL` pointing to a dev Postgres
3. add `drizzle.config.ts` (copy above)
4. create `db/schema.ts` (copy above)
5. run `npx drizzle-kit generate --name init_hrims` → inspect `drizzle/0001_init_hrims.sql`
6. if OK, run `npx drizzle-kit migrate` to apply to DB
7. add SvelteKit config (`svelte.config.ts`) mapping `app/` as shown, then scaffold `app/routes` and run `npm run dev` (SvelteKit will generate aliases)
8. wire frontend mocks to match the Drizzle schema shape (fields & enums) so later backend swap is seamless

---

If you want I can do any of these next, immediately in this chat (pick one):

* A — **Produce the exact `db/schema.ts` file** (I already gave it — confirm if you want minor field name tweaks or enum types converted to SQL enums in a custom migration).
* B — **Create a ready `drizzle/0001_init_hrims.sql` migration file** (I can convert schema -> SQL migration we wrote earlier, or run a codegen-style export) — note: I’ll output the SQL ready to paste.
* C — **Create the SvelteKit config + skeleton folder layout using `app/`** (I’ll generate `svelte.config.ts`, `package.json` scripts, and an initial `app/routes/+page.svelte` template).
* D — **Add Drizzle-based example Edge Function** that queries `hr_documents` (for your file-scan or signed-url flows).

Which would you like me to produce right now?

[1]: https://orm.drizzle.team/docs/tutorials/drizzle-with-supabase?utm_source=chatgpt.com "Drizzle with Supabase Database"
[2]: https://svelte.dev/docs/kit/configuration?utm_source=chatgpt.com "Configuration • Docs"
[3]: https://orm.drizzle.team/docs/get-started/supabase-new?utm_source=chatgpt.com "Get Started with Drizzle and Supabase"
[4]: https://orm.drizzle.team/docs/kit-overview?utm_source=chatgpt.com "Migrations with Drizzle Kit"
[5]: https://github.com/drizzle-team/drizzle-orm/discussions/568?utm_source=chatgpt.com "Is is possible to auto-generate UUID primary key field? #568"
[6]: https://stackoverflow.com/questions/35959265/postgresql-function-gen-random-uuid-not-working?utm_source=chatgpt.com "PostgreSQL function gen_random_uuid() not working"
[7]: https://orm.drizzle.team/docs/get-started-postgresql?utm_source=chatgpt.com "PostgreSQL - Drizzle ORM"
[8]: https://orm.drizzle.team/docs/kit-custom-migrations?utm_source=chatgpt.com "Custom migrations - Drizzle ORM"
[9]: https://svelte.dev/docs/kit/project-structure?utm_source=chatgpt.com "Project structure • Docs"
[10]: https://joyofcode.xyz/sveltekit-project-structure?utm_source=chatgpt.com "SvelteKit Project Structure Explained"
[11]: https://stackoverflow.com/questions/78329576/how-to-declare-self-referencing-foreign-key-with-drizzle-orm?utm_source=chatgpt.com "How to declare self-referencing foreign key with Drizzle ORM"
[12]: https://orm.drizzle.team/docs/indexes-constraints?utm_source=chatgpt.com "Indexes & Constraints - Drizzle ORM"
[13]: https://orm.drizzle.team/docs/migrations?utm_source=chatgpt.com "Migrations - Drizzle ORM"
