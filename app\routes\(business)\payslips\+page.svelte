<script lang="ts">
	import { onMount } from 'svelte';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import { Download, Calendar, DollarSign, FileText, Eye, Search } from '@lucide/svelte';
	import { notificationStore } from '$lib/stores/notificationStore';

	interface Payslip {
		id: string;
		month: string;
		year: number;
		payPeriod: string;
		grossSalary: number;
		netSalary: number;
		deductions: number;
		status: 'available' | 'processing' | 'pending';
		generatedAt: string;
		downloadUrl?: string;
	}

	// Mock payslip data
	let payslips: Payslip[] = [
		{
			id: '2024-03',
			month: 'March',
			year: 2024,
			payPeriod: '01 Mar 2024 - 31 Mar 2024',
			grossSalary: 5500.00,
			netSalary: 4235.50,
			deductions: 1264.50,
			status: 'available',
			generatedAt: '2024-04-01',
			downloadUrl: '/mock/payslip-2024-03.pdf'
		},
		{
			id: '2024-02',
			month: 'February',
			year: 2024,
			payPeriod: '01 Feb 2024 - 29 Feb 2024',
			grossSalary: 5500.00,
			netSalary: 4235.50,
			deductions: 1264.50,
			status: 'available',
			generatedAt: '2024-03-01',
			downloadUrl: '/mock/payslip-2024-02.pdf'
		},
		{
			id: '2024-01',
			month: 'January',
			year: 2024,
			payPeriod: '01 Jan 2024 - 31 Jan 2024',
			grossSalary: 5500.00,
			netSalary: 4235.50,
			deductions: 1264.50,
			status: 'available',
			generatedAt: '2024-02-01',
			downloadUrl: '/mock/payslip-2024-01.pdf'
		},
		{
			id: '2023-12',
			month: 'December',
			year: 2023,
			payPeriod: '01 Dec 2023 - 31 Dec 2023',
			grossSalary: 5500.00,
			netSalary: 4735.50,
			deductions: 764.50,
			status: 'available',
			generatedAt: '2024-01-01',
			downloadUrl: '/mock/payslip-2023-12.pdf'
		},
		{
			id: '2023-11',
			month: 'November',
			year: 2023,
			payPeriod: '01 Nov 2023 - 30 Nov 2023',
			grossSalary: 5500.00,
			netSalary: 4235.50,
			deductions: 1264.50,
			status: 'available',
			generatedAt: '2023-12-01',
			downloadUrl: '/mock/payslip-2023-11.pdf'
		},
		{
			id: '2024-04',
			month: 'April',
			year: 2024,
			payPeriod: '01 Apr 2024 - 30 Apr 2024',
			grossSalary: 5500.00,
			netSalary: 0,
			deductions: 0,
			status: 'processing',
			generatedAt: '2024-05-01'
		}
	];

	let filteredPayslips = $state(payslips);
	let selectedYear = $state<number | 'all'>('all');
	let searchQuery = $state('');

	// Get available years
	const availableYears = [...new Set(payslips.map(p => p.year))].sort((a, b) => b - a);

	// Filter payslips based on year and search
	$effect(() => {
		filteredPayslips = payslips.filter(payslip => {
			const matchesYear = selectedYear === 'all' || payslip.year === selectedYear;
			const matchesSearch = payslip.month.toLowerCase().includes(searchQuery.toLowerCase()) ||
								payslip.payPeriod.toLowerCase().includes(searchQuery.toLowerCase());
			return matchesYear && matchesSearch;
		});
	});

	const getStatusColor = (status: Payslip['status']) => {
		switch (status) {
			case 'available': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
			case 'processing': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
			case 'pending': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
		}
	};

	const handleDownload = async (payslip: Payslip) => {
		if (!payslip.downloadUrl) {
			notificationStore.warning('Download Unavailable', 'This payslip is not yet available for download.');
			return;
		}

		try {
			notificationStore.success('Download Started', `Downloading payslip for ${payslip.month} ${payslip.year}...`);
			
			// Mock download functionality with signed URL
			setTimeout(() => {
				notificationStore.info('Download Complete', `Payslip for ${payslip.month} ${payslip.year} has been downloaded successfully.`);
			}, 2000);
		} catch (error) {
			notificationStore.error('Download Failed', 'Failed to download the payslip. Please try again.');
		}
	};

	const handlePreview = (payslip: Payslip) => {
		if (!payslip.downloadUrl) {
			notificationStore.warning('Preview Unavailable', 'This payslip is not yet available for preview.');
			return;
		}
		notificationStore.info('Preview', `Preview functionality for ${payslip.month} ${payslip.year} payslip will be available soon.`);
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	};

	// Calculate year-to-date totals
	const currentYear = new Date().getFullYear();
	const ytdPayslips = payslips.filter(p => p.year === currentYear && p.status === 'available');
	const ytdGross = ytdPayslips.reduce((sum, p) => sum + p.grossSalary, 0);
	const ytdNet = ytdPayslips.reduce((sum, p) => sum + p.netSalary, 0);
	const ytdDeductions = ytdPayslips.reduce((sum, p) => sum + p.deductions, 0);
</script>

<svelte:head>
	<title>My Payslips - HRIMS</title>
</svelte:head>

<div class="container mx-auto p-6 space-y-6">
	<!-- Header -->
	<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
		<div>
			<h1 class="text-2xl font-bold text-foreground">My Payslips</h1>
			<p class="text-muted-foreground">View and download your salary statements</p>
		</div>
	</div>

	<!-- Year-to-Date Summary -->
	<div class="grid gap-4 md:grid-cols-3">
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">YTD Gross Salary</CardTitle>
				<DollarSign class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-green-600">{formatCurrency(ytdGross)}</div>
				<p class="text-xs text-muted-foreground">
					{ytdPayslips.length} payslips in {currentYear}
				</p>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">YTD Net Salary</CardTitle>
				<DollarSign class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-blue-600">{formatCurrency(ytdNet)}</div>
				<p class="text-xs text-muted-foreground">
					After deductions
				</p>
			</CardContent>
		</Card>
		<Card>
			<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle class="text-sm font-medium">YTD Deductions</CardTitle>
				<DollarSign class="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div class="text-2xl font-bold text-orange-600">{formatCurrency(ytdDeductions)}</div>
				<p class="text-xs text-muted-foreground">
					Total deductions
				</p>
			</CardContent>
		</Card>
	</div>

	<!-- Filters -->
	<div class="flex flex-col sm:flex-row gap-4">
		<div class="flex items-center gap-2">
			<label for="year-select" class="text-sm font-medium">Year:</label>
			<select
				id="year-select"
				bind:value={selectedYear}
				class="px-3 py-2 border border-input bg-background rounded-md text-sm min-w-[120px]"
			>
				<option value="all">All Years</option>
				{#each availableYears as year}
					<option value={year}>{year}</option>
				{/each}
			</select>
		</div>
		
		<div class="flex-1 max-w-md">
			<div class="relative">
				<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
				<Input
					type="search"
					placeholder="Search payslips..."
					bind:value={searchQuery}
					class="pl-10"
				/>
			</div>
		</div>
	</div>

	<!-- Payslips List -->
	<div class="space-y-4">
		{#each filteredPayslips as payslip (payslip.id)}
			<Card class="hover:shadow-md transition-shadow">
				<CardContent class="p-6">
					<div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
						<!-- Payslip Info -->
						<div class="flex items-center gap-4">
							<div class="p-3 bg-primary/10 rounded-lg">
								<FileText class="w-6 h-6 text-primary" />
							</div>
							<div>
								<h3 class="font-semibold text-lg">
									{payslip.month} {payslip.year}
								</h3>
								<p class="text-sm text-muted-foreground">
									{payslip.payPeriod}
								</p>
								<div class="flex items-center gap-2 mt-1">
									<Badge class={getStatusColor(payslip.status)} variant="secondary">
										{payslip.status}
									</Badge>
									{#if payslip.status === 'available'}
										<span class="text-xs text-muted-foreground">
											Generated: {formatDate(payslip.generatedAt)}
										</span>
									{/if}
								</div>
							</div>
						</div>

						<!-- Salary Details -->
						{#if payslip.status === 'available'}
							<div class="grid grid-cols-3 gap-4 text-center lg:text-left">
								<div>
									<p class="text-sm text-muted-foreground">Gross</p>
									<p class="font-semibold text-green-600">
										{formatCurrency(payslip.grossSalary)}
									</p>
								</div>
								<div>
									<p class="text-sm text-muted-foreground">Deductions</p>
									<p class="font-semibold text-orange-600">
										{formatCurrency(payslip.deductions)}
									</p>
								</div>
								<div>
									<p class="text-sm text-muted-foreground">Net</p>
									<p class="font-semibold text-blue-600">
										{formatCurrency(payslip.netSalary)}
									</p>
								</div>
							</div>
						{:else}
							<div class="text-center lg:text-left">
								<p class="text-sm text-muted-foreground">
									{payslip.status === 'processing' ? 'Payslip is being processed' : 'Payslip pending'}
								</p>
							</div>
						{/if}

						<!-- Actions -->
						<div class="flex gap-2">
							{#if payslip.downloadUrl}
								<Button
									size="sm"
									onclick={() => handleDownload(payslip)}
									class="flex items-center gap-2"
								>
									<Download class="w-4 h-4" />
									Download
								</Button>
								<Button
									size="sm"
									variant="outline"
									onclick={() => handlePreview(payslip)}
								>
									<Eye class="w-4 h-4" />
								</Button>
							{:else}
								<Button
									size="sm"
									disabled
									class="flex items-center gap-2"
								>
									<Calendar class="w-4 h-4" />
									{payslip.status === 'processing' ? 'Processing' : 'Pending'}
								</Button>
							{/if}
						</div>
					</div>
				</CardContent>
			</Card>
		{/each}
	</div>

	{#if filteredPayslips.length === 0}
		<div class="text-center py-12">
			<FileText class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-medium text-muted-foreground mb-2">No payslips found</h3>
			<p class="text-sm text-muted-foreground">
				{searchQuery || selectedYear !== 'all' 
					? 'Try adjusting your search or filter criteria.' 
					: 'Your payslips will appear here once they are generated.'}
			</p>
		</div>
	{/if}
</div>
