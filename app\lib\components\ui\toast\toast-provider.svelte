<script lang="ts">
	import { flip } from 'svelte/animate';
	import { Toast, ToastTitle, ToastDescription, ToastAction, ToastClose } from './index.js';
	import { CheckCircle, AlertCircle, XCircle, Info, Clock } from '@lucide/svelte';
	import { notificationStore, type EnhancedNotification } from '$lib/stores/notificationStore';
	import { onMount } from 'svelte';

	interface Props {
		position?: EnhancedNotification['position'];
		maxVisible?: number;
		enableAnimations?: boolean;
		className?: string;
	}

	let {
		position = 'bottom-right',
		maxVisible = 5,
		enableAnimations = true,
		className = ''
	}: Props = $props();

	let notifications = $state<EnhancedNotification[]>([]);

	// Subscribe to the store
	$effect(() => {
		const unsubscribe = notificationStore.subscribe(newNotifications => {
			notifications = newNotifications;
		});
		return unsubscribe;
	});

	// Position classes for the container
	const positionClasses = {
		'top-right': 'top-4 right-4 items-end',
		'top-left': 'top-4 left-4 items-start',
		'bottom-right': 'bottom-4 right-4 items-end',
		'bottom-left': 'bottom-4 left-4 items-start',
		'top-center': 'top-4 left-1/2 transform -translate-x-1/2 items-center',
		'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2 items-center'
	};

	// Group notifications by position - simplified for debugging
	let notificationsByPosition = $state<Record<string, EnhancedNotification[]>>({});

	// Update grouped notifications when notifications change
	$effect(() => {
		const grouped: Record<string, EnhancedNotification[]> = {};

		notifications.forEach(notification => {
			const notificationPosition = notification.position || position;
			if (!grouped[notificationPosition]) {
				grouped[notificationPosition] = [];
			}
			grouped[notificationPosition].push(notification);
		});

		// Sort each group by priority and timestamp
		Object.keys(grouped).forEach(pos => {
			grouped[pos].sort((a, b) => {
				const priorityOrder = { low: 1, normal: 2, high: 3, critical: 4 };
				const priorityDiff = (priorityOrder[b.priority || 'normal'] || 2) - (priorityOrder[a.priority || 'normal'] || 2);
				if (priorityDiff !== 0) return priorityDiff;
				return b.timestamp - a.timestamp;
			});

			// Limit visible notifications per position
			if (grouped[pos].length > maxVisible) {
				grouped[pos] = grouped[pos].slice(0, maxVisible);
			}
		});

		notificationsByPosition = grouped;
	});

	// Icon mapping
	const getIcon = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return CheckCircle;
			case 'error': return XCircle;
			case 'warning': return AlertCircle;
			case 'info': return Info;
		}
	};

	// Get toast variant based on notification type
	const getVariant = (type: EnhancedNotification['type']) => {
		switch (type) {
			case 'success': return 'success';
			case 'error': return 'destructive';
			case 'warning': return 'warning';
			case 'info': return 'info';
			default: return 'default';
		}
	};

	// Handle notification removal
	const handleRemove = (id: string) => {
		notificationStore.remove(id);
	};

	// Auto-dismiss timer management
	const timers = new Map<string, ReturnType<typeof setTimeout>>();

	const startAutoRemoveTimer = (notification: EnhancedNotification) => {
		if (!notification.duration || notification.duration <= 0) return;
		
		const timerId = setTimeout(() => {
			handleRemove(notification.id);
			timers.delete(notification.id);
		}, notification.duration);
		
		timers.set(notification.id, timerId);
	};

	// Reactive effect to manage timers when notifications change
	$effect(() => {
		// Clear timers for removed notifications
		const currentIds = notifications.map(n => n.id);
		timers.forEach((timerId, id) => {
			if (!currentIds.includes(id)) {
				clearTimeout(timerId);
				timers.delete(id);
			}
		});

		// Start timers for new notifications
		notifications.forEach(notification => {
			if (!timers.has(notification.id)) {
				startAutoRemoveTimer(notification);
			}
		});
	});

	// Keyboard shortcuts
	const handleGlobalKeydown = (event: KeyboardEvent) => {
		// Ctrl/Cmd + Shift + X to clear all notifications
		if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'X') {
			event.preventDefault();
			notificationStore.clear();
		}
		
		// Escape to dismiss the most recent dismissible notification
		if (event.key === 'Escape') {
			const dismissibleNotifications = notifications.filter(n => n.dismissible);
			if (dismissibleNotifications.length > 0) {
				const mostRecent = dismissibleNotifications.reduce((latest, current) => 
					current.timestamp > latest.timestamp ? current : latest
				);
				handleRemove(mostRecent.id);
			}
		}
	};

	// Handle lifecycle
	onMount(() => {
		// Load persisted notifications
		notificationStore.loadPersisted();

		// Add global keyboard listeners
		document.addEventListener('keydown', handleGlobalKeydown);

		// Cleanup
		return () => {
			document.removeEventListener('keydown', handleGlobalKeydown);
			
			// Clear all timers
			timers.forEach(timerId => clearTimeout(timerId));
			timers.clear();
		};
	});

	// Accessibility announcements for screen readers
	const announceToScreenReader = (message: string) => {
		const announcement = document.createElement('div');
		announcement.setAttribute('aria-live', 'polite');
		announcement.setAttribute('aria-atomic', 'true');
		announcement.className = 'sr-only';
		announcement.textContent = message;
		
		document.body.appendChild(announcement);
		
		setTimeout(() => {
			document.body.removeChild(announcement);
		}, 1000);
	};

	// Watch for critical notifications and announce them
	$effect(() => {
		const criticalNotifications = notifications.filter(n => n.priority === 'critical');
		criticalNotifications.forEach(notification => {
			announceToScreenReader(`Critical notification: ${notification.title}. ${notification.message || ''}`);
		});
	});
</script>



<!-- Toast Containers for each position -->
{#each Object.entries(notificationsByPosition) as [pos, positionNotifications]}
	{#if positionNotifications.length > 0}
		<div
			class="fixed z-50 max-w-sm w-full space-y-2 pointer-events-none flex flex-col {positionClasses[pos as keyof typeof positionClasses]} {className}"
			role="region"
			aria-label="Notifications"
			data-toast-provider="true"
			data-position={pos}
		>
			{#each positionNotifications as notification (notification.id)}
				{@const IconComponent = getIcon(notification.type)}
				<div animate:flip={{ duration: enableAnimations ? 300 : 0 }}>
					<Toast variant={getVariant(notification.type)} class="pointer-events-auto">
						<!-- Progress Bar -->
						{#if notification.showProgress && notification.duration && notification.duration > 0}
							<div class="absolute top-0 left-0 right-0 h-1 bg-black/10 rounded-t-md overflow-hidden">
								<div
									class="h-full bg-current transition-all duration-{notification.duration} ease-linear"
									style="width: 0%; animation: progress {notification.duration}ms linear forwards;"
								></div>
							</div>
						{/if}

						<div class="flex items-start gap-3 w-full">
							<!-- Icon -->
							<IconComponent class="w-5 h-5 flex-shrink-0 mt-0.5" />

							<!-- Content -->
							<div class="flex-1 min-w-0">
								<ToastTitle>{notification.title}</ToastTitle>
								
								{#if notification.message}
									<ToastDescription class="mt-1">
										{notification.message}
									</ToastDescription>
								{/if}

								<!-- Rich Content -->
								{#if notification.richContent}
									<div class="mt-2">
										{#if notification.richContent.html}
											{@html notification.richContent.html}
										{:else if notification.richContent.component}
											{@const Component = notification.richContent.component}
											<Component {...notification.richContent.props || {}} />
										{/if}
									</div>
								{/if}

								<!-- Action Button -->
								{#if notification.action}
									<div class="mt-3">
										<ToastAction onclick={notification.action.onClick}>
											{notification.action.label}
										</ToastAction>
									</div>
								{/if}

								<!-- Timestamp for critical notifications -->
								{#if notification.priority === 'critical'}
									<div class="flex items-center gap-1 mt-2 text-xs opacity-70">
										<Clock class="w-3 h-3" />
										<span>{new Date(notification.timestamp).toLocaleTimeString()}</span>
									</div>
								{/if}
							</div>

							<!-- Close Button -->
							{#if notification.dismissible}
								<ToastClose onclick={() => handleRemove(notification.id)} />
							{/if}
						</div>
					</Toast>
				</div>
			{/each}
		</div>
	{/if}
{/each}

<!-- Screen reader announcements -->
<div class="sr-only" aria-live="polite" aria-atomic="true">
	{#if notifications.length > 0}
		{notifications.length} notification{notifications.length === 1 ? '' : 's'} available
	{/if}
</div>

<style>
	@keyframes progress {
		from { width: 100%; }
		to { width: 0%; }
	}
</style>
