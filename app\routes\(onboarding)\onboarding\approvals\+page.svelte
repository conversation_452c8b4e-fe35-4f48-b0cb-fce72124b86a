<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { UserCheck, Clock, CheckCircle, AlertCircle, MessageSquare, Calendar } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	// Approval items
	let approvalItems = [
		{
			id: 'personal-details',
			title: 'Personal Information',
			description: 'Basic details and emergency contacts',
			status: 'approved',
			approvedBy: 'HR Department',
			approvedAt: '2024-01-15T10:30:00Z',
			comments: 'All information verified and approved.'
		},
		{
			id: 'employment-details',
			title: 'Employment Information',
			description: 'Job title, department, and reporting structure',
			status: 'approved',
			approvedBy: 'Department Manager',
			approvedAt: '2024-01-15T11:15:00Z',
			comments: 'Position and reporting structure confirmed.'
		},
		{
			id: 'documents',
			title: 'Document Verification',
			description: 'ID documents and certificates',
			status: 'pending',
			approvedBy: null,
			approvedAt: null,
			comments: 'Documents under review by compliance team.'
		},
		{
			id: 'background-check',
			title: 'Background Verification',
			description: 'Background and reference checks',
			status: 'in-progress',
			approvedBy: null,
			approvedAt: null,
			comments: 'Background check in progress, expected completion in 2-3 days.'
		},
		{
			id: 'manager-approval',
			title: 'Manager Final Approval',
			description: 'Direct manager approval for onboarding completion',
			status: 'pending',
			approvedBy: null,
			approvedAt: null,
			comments: 'Awaiting final approval from direct manager.'
		}
	];

	let managerMessage = '';
	let isSimulatingApproval = false;

	const getStatusIcon = (status: string) => {
		switch (status) {
			case 'approved':
				return CheckCircle;
			case 'pending':
				return Clock;
			case 'in-progress':
				return AlertCircle;
			default:
				return Clock;
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case 'approved':
				return 'text-green-600';
			case 'pending':
				return 'text-yellow-600';
			case 'in-progress':
				return 'text-blue-600';
			default:
				return 'text-gray-600';
		}
	};

	const getStatusBg = (status: string) => {
		switch (status) {
			case 'approved':
				return 'bg-green-50 border-green-200';
			case 'pending':
				return 'bg-yellow-50 border-yellow-200';
			case 'in-progress':
				return 'bg-blue-50 border-blue-200';
			default:
				return 'bg-gray-50 border-gray-200';
		}
	};

	const simulateManagerApproval = async () => {
		isSimulatingApproval = true;
		try {
			// Simulate approval process
			await new Promise(resolve => setTimeout(resolve, 3000));

			// Update approval items
			approvalItems = approvalItems.map(item => {
				if (item.status === 'pending' || item.status === 'in-progress') {
					return {
						...item,
						status: 'approved',
						approvedBy: item.id === 'manager-approval' ? 'Sarah Johnson (Manager)' : 'System',
						approvedAt: new Date().toISOString(),
						comments: item.id === 'manager-approval' 
							? managerMessage || 'Approved for onboarding completion.'
							: 'Automatically approved.'
					};
				}
				return item;
			});

			uiStore.showSuccess('Approvals Complete', 'All required approvals have been obtained!');
		} catch (error) {
			uiStore.showError('Approval Failed', 'Failed to process approvals. Please try again.');
		} finally {
			isSimulatingApproval = false;
		}
	};

	const allApproved = $derived(
		approvalItems.every(item => item.status === 'approved')
	);

	const handleNext = () => {
		if (!allApproved) {
			uiStore.showError('Approvals Pending', 'Please wait for all approvals to be completed before continuing.');
			return;
		}

		// Save approval data
		onboardingStore.updateData('approvals', {
			items: approvalItems,
			allApproved: true,
			completedAt: new Date().toISOString()
		});

		// Mark step as completed and move to final step
		onboardingStore.completeStep('approvals');
		onboardingStore.setCurrentStep('complete');
		goto('/onboarding/complete');
	};

	const handleBack = () => {
		onboardingStore.setCurrentStep('assets');
		goto('/onboarding/assets');
	};
</script>

<svelte:head>
	<title>Manager Approval - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Manager Approval & Final Review"
		subtitle="Your onboarding information is being reviewed by your manager and HR team. Track the approval status below."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Complete Onboarding"
		nextButtonDisabled={!allApproved}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-6">
			<!-- Approval Status List -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">Approval Checklist</h3>
				
				{#each approvalItems as item}
					<div class="border rounded-xl p-4 {getStatusBg(item.status)}">
						<div class="flex items-start gap-3">
							<svelte:component 
								this={getStatusIcon(item.status)} 
								class="w-5 h-5 mt-0.5 {getStatusColor(item.status)}" 
							/>
							<div class="flex-1">
								<div class="flex items-center justify-between mb-2">
									<h4 class="font-semibold text-[#1C1C1C]">{item.title}</h4>
									<span class="text-sm font-medium {getStatusColor(item.status)} capitalize">
										{item.status.replace('-', ' ')}
									</span>
								</div>
								<p class="text-sm text-[#8A6A52] mb-2">{item.description}</p>
								
								{#if item.status === 'approved' && item.approvedBy}
									<div class="text-xs text-[#5C4024] space-y-1">
										<p><strong>Approved by:</strong> {item.approvedBy}</p>
										<p><strong>Date:</strong> {new Date(item.approvedAt).toLocaleString()}</p>
										{#if item.comments}
											<p><strong>Comments:</strong> {item.comments}</p>
										{/if}
									</div>
								{:else}
									<p class="text-xs text-[#8A6A52]">{item.comments}</p>
								{/if}
							</div>
						</div>
					</div>
				{/each}
			</div>

			<!-- Manager Message Section -->
			{#if !allApproved}
				<div class="space-y-4">
					<h3 class="text-lg font-semibold text-[#1C1C1C]">Manager Communication</h3>
					<div class="bg-[#FCF8F2] rounded-xl p-4 border border-[#EDE0CF]">
						<div class="flex items-start gap-3 mb-3">
							<MessageSquare class="w-5 h-5 text-[#C49A6C] mt-0.5" />
							<div>
								<h4 class="font-medium text-[#1C1C1C]">Message from Your Manager</h4>
								<p class="text-sm text-[#8A6A52]">Optional message for the approval process</p>
							</div>
						</div>
						<textarea
							bind:value={managerMessage}
							placeholder="Welcome to the team! Looking forward to working with you..."
							class="w-full p-3 border border-[#EDE0CF] rounded-lg text-sm resize-none"
							rows="3"
						></textarea>
					</div>

					<!-- Simulate Approval Button -->
					<div class="text-center">
						<Button
							onclick={simulateManagerApproval}
							disabled={isSimulatingApproval}
							class="onboarding-button-primary"
						>
							{#if isSimulatingApproval}
								<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
								Processing Approvals...
							{:else}
								<UserCheck class="w-4 h-4 mr-2" />
								Simulate Manager Approval
							{/if}
						</Button>
						<p class="text-xs text-[#8A6A52] mt-2">
							This simulates the approval process for demonstration purposes
						</p>
					</div>
				</div>
			{:else}
				<!-- All Approved Message -->
				<div class="bg-green-50 rounded-xl p-6 border border-green-200 text-center">
					<CheckCircle class="w-12 h-12 text-green-500 mx-auto mb-3" />
					<h3 class="text-lg font-semibold text-green-800 mb-2">All Approvals Complete!</h3>
					<p class="text-green-700 mb-4">
						Congratulations! Your onboarding has been approved by all required parties. 
						You're ready to complete the final step.
					</p>
					<div class="flex items-center justify-center gap-2 text-sm text-green-600">
						<Calendar class="w-4 h-4" />
						<span>Approved on {new Date().toLocaleDateString()}</span>
					</div>
				</div>
			{/if}

			<!-- Progress Summary -->
			<div class="bg-[#FCF8F2] rounded-xl p-4 border border-[#EDE0CF]">
				<h4 class="font-medium text-[#1C1C1C] mb-2">Approval Progress</h4>
				<div class="text-sm text-[#8A6A52] space-y-1">
					<p>Approved: {approvalItems.filter(i => i.status === 'approved').length} / {approvalItems.length}</p>
					<p>Pending: {approvalItems.filter(i => i.status === 'pending').length}</p>
					<p>In Progress: {approvalItems.filter(i => i.status === 'in-progress').length}</p>
				</div>
				
				<!-- Progress Bar -->
				<div class="w-full bg-[#EDE0CF] rounded-full h-2 mt-3">
					<div 
						class="bg-gradient-to-r from-[#F5D6A1] to-[#C49A6C] h-2 rounded-full transition-all duration-500"
						style="width: {(approvalItems.filter(i => i.status === 'approved').length / approvalItems.length) * 100}%"
					></div>
				</div>
			</div>
		</div>
	</OnboardingCard>
</div>
