# 11 — Industrial Relations

Pages:
- `/ir` — report incident form
- `/ir/cases` — case listing (confidential)

Components:
- `IncidentForm.svelte` — structured capture w/ attachments
- `ConfidentialCase.svelte` — restricted view (only HR role in role switcher)

Workflow:
1. Incident reported -> case created -> investigator assigned -> status updated.
2. For frontend, simulate investigator steps and final outcome report.

Acceptance:
- Uploaded evidence marks documents as `confidential` in mock.
