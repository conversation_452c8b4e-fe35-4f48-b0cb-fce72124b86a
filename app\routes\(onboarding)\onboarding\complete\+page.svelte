<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Download, Calendar, Users, BookOpen, Gift, CheckCircle, Sparkles } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	let confettiEnabled = true;
	let welcomePackUrl = 'https://mock.welcome/rtg-welcome-pack.pdf';
	let isGeneratingPack = false;

	// Check for reduced motion preference
	onMount(() => {
		const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
		confettiEnabled = !prefersReducedMotion;
		
		// Trigger confetti animation if enabled
		if (confettiEnabled) {
			setTimeout(() => {
				// Simple confetti effect using CSS animation
				const confettiElements = document.querySelectorAll('.confetti');
				confettiElements.forEach(el => {
					el.classList.add('animate-confetti');
				});
			}, 500);
		}
	});

	const generateWelcomePack = async () => {
		isGeneratingPack = true;
		try {
			// Simulate welcome pack generation
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			uiStore.showSuccess('Welcome Pack Ready', 'Your personalized welcome pack has been generated!');
		} catch (error) {
			uiStore.showError('Generation Failed', 'Failed to generate welcome pack. Please try again.');
		} finally {
			isGeneratingPack = false;
		}
	};

	const completeOnboarding = async () => {
		try {
			// Mark all steps as completed
			store.steps.forEach(step => {
				onboardingStore.completeStep(step.id);
			});

			// Save completion data
			onboardingStore.updateData('completion', {
				completedAt: new Date().toISOString(),
				welcomePackGenerated: true,
				status: 'complete'
			});

			uiStore.showSuccess('Onboarding Complete', 'Welcome to Rainbow Tourism Group! Your onboarding is now complete.');
			
			// Redirect to dashboard after a delay
			setTimeout(() => {
				goto('/');
			}, 3000);
		} catch (error) {
			uiStore.showError('Completion Failed', 'Failed to complete onboarding. Please try again.');
		}
	};

	const addToCalendar = () => {
		uiStore.showSuccess('Calendar Updated', 'Your first day schedule has been added to your calendar.');
	};

	// Summary data
	const completionSummary = {
		totalSteps: store.steps.length,
		completedSteps: store.steps.filter(s => s.status === 'completed').length,
		documentsUploaded: 5,
		policiesAcknowledged: 4,
		trainingsEnrolled: 3,
		assetsRequested: 6
	};
</script>

<svelte:head>
	<title>Onboarding Complete - Welcome to RTG!</title>
</svelte:head>

<!-- Confetti Elements -->
{#if confettiEnabled}
	<div class="fixed inset-0 pointer-events-none z-50">
		{#each Array(20) as _, i}
			<div 
				class="confetti absolute w-2 h-2 bg-gradient-to-r from-[#F5D6A1] to-[#C49A6C] rounded-full opacity-0"
				style="left: {Math.random() * 100}%; top: -10px; animation-delay: {Math.random() * 3}s;"
			></div>
		{/each}
	</div>
{/if}

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && step.status === 'completed') {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="🎉 Congratulations!"
		subtitle="You've successfully completed your onboarding at Rainbow Tourism Group. Welcome to the team!"
		showBackButton={false}
		showNextButton={true}
		nextButtonText="Enter Dashboard"
		onNext={completeOnboarding}
	>
		<div class="space-y-8">
			<!-- Completion Hero -->
			<div class="text-center py-6">
				<div class="w-20 h-20 bg-gradient-to-br from-[#F5D6A1] to-[#C49A6C] rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
					{#if confettiEnabled}
						<Sparkles class="w-10 h-10 text-white animate-pulse" />
					{:else}
						<CheckCircle class="w-10 h-10 text-white" />
					{/if}
				</div>
				<h2 class="text-2xl font-bold text-[#1C1C1C] mb-2">Welcome to the RTG Family!</h2>
				<p class="text-[#8A6A52] text-lg">
					Your onboarding journey is complete. We're excited to have you on board!
				</p>
			</div>

			<!-- Completion Summary -->
			<div class="bg-gradient-to-r from-[#F5D6A1]/10 to-[#C49A6C]/5 rounded-2xl p-6 border border-[#F5D6A1]/20">
				<h3 class="text-lg font-semibold text-[#1C1C1C] mb-4">Your Onboarding Summary</h3>
				<div class="grid md:grid-cols-2 gap-4">
					<div class="space-y-3">
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Steps Completed</span>
							<span class="font-semibold text-[#1C1C1C]">{completionSummary.completedSteps}/{completionSummary.totalSteps}</span>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Documents Uploaded</span>
							<span class="font-semibold text-[#1C1C1C]">{completionSummary.documentsUploaded}</span>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Policies Acknowledged</span>
							<span class="font-semibold text-[#1C1C1C]">{completionSummary.policiesAcknowledged}</span>
						</div>
					</div>
					<div class="space-y-3">
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Training Programs</span>
							<span class="font-semibold text-[#1C1C1C]">{completionSummary.trainingsEnrolled}</span>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Assets Requested</span>
							<span class="font-semibold text-[#1C1C1C]">{completionSummary.assetsRequested}</span>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-[#8A6A52]">Status</span>
							<span class="font-semibold text-green-600">✓ Complete</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Welcome Pack -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">Your Welcome Pack</h3>
				<div class="border border-[#EDE0CF] rounded-xl p-4">
					<div class="flex items-center gap-3 mb-4">
						<Gift class="w-8 h-8 text-[#C49A6C]" />
						<div>
							<h4 class="font-semibold text-[#1C1C1C]">RTG Welcome Package</h4>
							<p class="text-sm text-[#8A6A52]">Your personalized welcome materials and resources</p>
						</div>
					</div>
					
					<div class="grid md:grid-cols-2 gap-3">
						<Button
							onclick={generateWelcomePack}
							disabled={isGeneratingPack}
							variant="outline"
							class="flex-1"
						>
							{#if isGeneratingPack}
								<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
								Generating...
							{:else}
								<Download class="w-4 h-4 mr-2" />
								Download Welcome Pack
							{/if}
						</Button>
						<Button onclick={addToCalendar} variant="outline" class="flex-1">
							<Calendar class="w-4 h-4 mr-2" />
							Add First Day to Calendar
						</Button>
					</div>
				</div>
			</div>

			<!-- Next Steps -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">What's Next?</h3>
				<div class="grid gap-4">
					<div class="flex items-start gap-3 p-4 bg-[#FCF8F2] rounded-lg border border-[#EDE0CF]">
						<Calendar class="w-6 h-6 text-[#C49A6C] mt-0.5" />
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Your First Day</h4>
							<p class="text-sm text-[#8A6A52]">
								Report to reception at 9:00 AM for your welcome tour and team introductions.
							</p>
						</div>
					</div>

					<div class="flex items-start gap-3 p-4 bg-[#FCF8F2] rounded-lg border border-[#EDE0CF]">
						<Users class="w-6 h-6 text-[#C49A6C] mt-0.5" />
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Meet Your Team</h4>
							<p class="text-sm text-[#8A6A52]">
								Your manager will introduce you to your colleagues and show you around your workspace.
							</p>
						</div>
					</div>

					<div class="flex items-start gap-3 p-4 bg-[#FCF8F2] rounded-lg border border-[#EDE0CF]">
						<BookOpen class="w-6 h-6 text-[#C49A6C] mt-0.5" />
						<div>
							<h4 class="font-medium text-[#1C1C1C]">Continue Learning</h4>
							<p class="text-sm text-[#8A6A52]">
								Access your training modules and continue your professional development journey.
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Contact Information -->
			<div class="bg-blue-50 rounded-xl p-4 border border-blue-200">
				<h4 class="font-medium text-blue-900 mb-2">Need Help?</h4>
				<p class="text-sm text-blue-700 mb-3">
					If you have any questions or need assistance, don't hesitate to reach out:
				</p>
				<div class="text-sm text-blue-800 space-y-1">
					<p><strong>HR Department:</strong> <EMAIL></p>
					<p><strong>IT Support:</strong> <EMAIL></p>
					<p><strong>Your Manager:</strong> Available via internal directory</p>
				</div>
			</div>
		</div>
	</OnboardingCard>
</div>

<style>
	@keyframes confetti-fall {
		0% {
			transform: translateY(-100vh) rotate(0deg);
			opacity: 1;
		}
		100% {
			transform: translateY(100vh) rotate(720deg);
			opacity: 0;
		}
	}

	.animate-confetti {
		animation: confetti-fall 3s linear forwards;
	}

	@media (prefers-reduced-motion: reduce) {
		.animate-confetti {
			animation: none;
			opacity: 0;
		}
	}
</style>
