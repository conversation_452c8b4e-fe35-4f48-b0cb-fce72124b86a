<script lang="ts">
	import { cn } from '$lib/utils.js';
	import { fly } from 'svelte/transition';
	import type { HTMLAttributes } from 'svelte/elements';

	interface Props extends HTMLAttributes<HTMLDivElement> {
		variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info';
		class?: string;
	}

	let { variant = 'default', class: className, ...restProps }: Props = $props();

	const variants = {
		default: 'border bg-background text-foreground',
		destructive: 'destructive border-destructive bg-destructive text-destructive-foreground',
		success: 'border-green-200 bg-green-50 text-green-900 dark:border-green-700 dark:bg-green-900/20 dark:text-green-100',
		warning: 'border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-100',
		info: 'border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-700 dark:bg-blue-900/20 dark:text-blue-100'
	};
</script>

<div
	class={cn(
		'group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all',
		variants[variant],
		className
	)}
	role="alert"
	aria-live="assertive"
	aria-atomic="true"
	transition:fly={{ x: 300, duration: 300 }}
	{...restProps}
>
	<slot />
</div>
