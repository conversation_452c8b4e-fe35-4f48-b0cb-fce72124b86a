<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore, type PersonalDetails } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import Select from '$lib/components/ui/Select.svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const personalDetails = $derived(store.data.personalDetails || {});

	// Form state
	let formData = $state({
		firstName: personalDetails.firstName || '',
		lastName: personalDetails.lastName || '',
		preferredName: personalDetails.preferredName || '',
		dateOfBirth: personalDetails.dateOfBirth || '',
		nationalId: personalDetails.nationalId || '',
		email: personalDetails.email || '',
		phone: personalDetails.phone || '',
		address: {
			street: personalDetails.address?.street || '',
			street2: personalDetails.address?.street2 || '',
			city: personalDetails.address?.city || '',
			postalCode: personalDetails.address?.postalCode || '',
			country: personalDetails.address?.country || 'ZW'
		},
		emergencyContact: {
			name: personalDetails.emergencyContact?.name || '',
			relationship: personalDetails.emergencyContact?.relationship || '',
			phone: personalDetails.emergencyContact?.phone || ''
		}
	});

	// Validation state
	let errors = $state<Record<string, string>>({});

	// Country options
	const countryOptions = [
		{ value: 'ZW', label: 'Zimbabwe' },
		{ value: 'ZA', label: 'South Africa' },
		{ value: 'BW', label: 'Botswana' },
		{ value: 'ZM', label: 'Zambia' },
		{ value: 'MW', label: 'Malawi' },
		{ value: 'MZ', label: 'Mozambique' }
	];

	// Relationship options
	const relationshipOptions = [
		{ value: 'spouse', label: 'Spouse' },
		{ value: 'parent', label: 'Parent' },
		{ value: 'sibling', label: 'Sibling' },
		{ value: 'child', label: 'Child' },
		{ value: 'friend', label: 'Friend' },
		{ value: 'other', label: 'Other' }
	];

	// Validation functions
	const validateEmail = (email: string) => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	};

	const validatePhone = (phone: string) => {
		const phoneRegex = /^[+]?[\d\s\-()]{7,15}$/;
		return phoneRegex.test(phone);
	};

	const validateAge = (dateOfBirth: string) => {
		const today = new Date();
		const birthDate = new Date(dateOfBirth);
		const age = today.getFullYear() - birthDate.getFullYear();
		const monthDiff = today.getMonth() - birthDate.getMonth();
		
		if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
			return age - 1 >= 18;
		}
		return age >= 18;
	};

	// Form validation
	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		// Required fields
		if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
		if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
		if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
		if (!formData.email.trim()) newErrors.email = 'Email is required';
		if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
		if (!formData.address.street.trim()) newErrors['address.street'] = 'Street address is required';
		if (!formData.address.city.trim()) newErrors['address.city'] = 'City is required';
		if (!formData.address.postalCode.trim()) newErrors['address.postalCode'] = 'Postal code is required';
		if (!formData.emergencyContact.name.trim()) newErrors['emergencyContact.name'] = 'Emergency contact name is required';
		if (!formData.emergencyContact.relationship || !String(formData.emergencyContact.relationship).trim()) newErrors['emergencyContact.relationship'] = 'Relationship is required';
		if (!formData.emergencyContact.phone.trim()) newErrors['emergencyContact.phone'] = 'Emergency contact phone is required';

		// Format validation
		if (formData.email && !validateEmail(formData.email)) {
			newErrors.email = 'Please enter a valid email address';
		}
		if (formData.phone && !validatePhone(formData.phone)) {
			newErrors.phone = 'Please enter a valid phone number';
		}
		if (formData.emergencyContact.phone && !validatePhone(formData.emergencyContact.phone)) {
			newErrors['emergencyContact.phone'] = 'Please enter a valid phone number';
		}
		if (formData.dateOfBirth && !validateAge(formData.dateOfBirth)) {
			newErrors.dateOfBirth = 'You must be at least 18 years old';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Auto-save functionality
	let saveTimeout: NodeJS.Timeout;
	const autoSave = () => {
		clearTimeout(saveTimeout);
		saveTimeout = setTimeout(() => {
			onboardingStore.updatePersonalDetails(formData);
		}, 1000);
	};

	// Handle input changes
	const handleInputChange = () => {
		autoSave();
	};

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		if (validateForm()) {
			onboardingStore.updatePersonalDetails(formData);
			onboardingStore.completeStep('personal-details');
			onboardingStore.setCurrentStep('employment-details');
			notificationStore.add({
				type: 'success',
				message: 'Personal details saved successfully!'
			});
			goto('/onboarding/employment-details');
		} else {
			notificationStore.add({
				type: 'error',
				message: 'Please fix the errors below before continuing.'
			});
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding');
	};
</script>

<svelte:head>
	<title>Personal Details - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Personal Details"
		subtitle="Please provide your basic information and emergency contact details. This information will be kept confidential and secure."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		onBack={handleBack}
		onNext={handleNext}
	>
		<form class="space-y-8" on:submit|preventDefault={handleNext}>
			<!-- Basic Information -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Basic Information
				</h3>
				
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- First Name -->
					<div class="space-y-2">
						<Label for="firstName" class="text-sm font-medium text-[#5C4024]">
							First Name <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="firstName"
							bind:value={formData.firstName}
							oninput={handleInputChange}
							placeholder="Enter your first name"
							class="onboarding-input {errors.firstName ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.firstName}
							aria-describedby={errors.firstName ? 'firstName-error' : undefined}
						/>
						{#if errors.firstName}
							<p id="firstName-error" class="text-xs text-[#EF4444]">{errors.firstName}</p>
						{/if}
					</div>

					<!-- Last Name -->
					<div class="space-y-2">
						<Label for="lastName" class="text-sm font-medium text-[#5C4024]">
							Last Name <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="lastName"
							bind:value={formData.lastName}
							oninput={handleInputChange}
							placeholder="Enter your last name"
							class="onboarding-input {errors.lastName ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.lastName}
							aria-describedby={errors.lastName ? 'lastName-error' : undefined}
						/>
						{#if errors.lastName}
							<p id="lastName-error" class="text-xs text-[#EF4444]">{errors.lastName}</p>
						{/if}
					</div>

					<!-- Preferred Name -->
					<div class="space-y-2">
						<Label for="preferredName" class="text-sm font-medium text-[#5C4024]">
							Preferred Name
						</Label>
						<Input
							id="preferredName"
							bind:value={formData.preferredName}
							oninput={handleInputChange}
							placeholder="How would you like to be called?"
							class="onboarding-input"
						/>
						<p class="text-xs text-[#8A6A52]">Optional - leave blank if same as first name</p>
					</div>

					<!-- Date of Birth -->
					<div class="space-y-2">
						<Label for="dateOfBirth" class="text-sm font-medium text-[#5C4024]">
							Date of Birth <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="dateOfBirth"
							type="date"
							bind:value={formData.dateOfBirth}
							oninput={handleInputChange}
							class="onboarding-input {errors.dateOfBirth ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.dateOfBirth}
							aria-describedby={errors.dateOfBirth ? 'dateOfBirth-error' : undefined}
						/>
						{#if errors.dateOfBirth}
							<p id="dateOfBirth-error" class="text-xs text-[#EF4444]">{errors.dateOfBirth}</p>
						{/if}
					</div>
				</div>

				<!-- Contact Information -->
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- National ID -->
					<div class="space-y-2">
						<Label for="nationalId" class="text-sm font-medium text-[#5C4024]">
							National ID Number
						</Label>
						<Input
							id="nationalId"
							bind:value={formData.nationalId}
							oninput={handleInputChange}
							placeholder="Enter your national ID"
							class="onboarding-input"
						/>
						<p class="text-xs text-[#8A6A52]">Optional - required for some countries</p>
					</div>

					<!-- Email -->
					<div class="space-y-2">
						<Label for="email" class="text-sm font-medium text-[#5C4024]">
							Email Address <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="email"
							type="email"
							bind:value={formData.email}
							oninput={handleInputChange}
							placeholder="<EMAIL>"
							class="onboarding-input {errors.email ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.email}
							aria-describedby={errors.email ? 'email-error' : undefined}
						/>
						{#if errors.email}
							<p id="email-error" class="text-xs text-[#EF4444]">{errors.email}</p>
						{/if}
					</div>

					<!-- Phone -->
					<div class="space-y-2 md:col-span-2">
						<Label for="phone" class="text-sm font-medium text-[#5C4024]">
							Phone Number <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							oninput={handleInputChange}
							placeholder="+263 77 123 4567"
							class="onboarding-input {errors.phone ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors.phone}
							aria-describedby={errors.phone ? 'phone-error' : undefined}
						/>
						{#if errors.phone}
							<p id="phone-error" class="text-xs text-[#EF4444]">{errors.phone}</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Address Information -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Address Information
				</h3>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Street Address -->
					<div class="space-y-2 md:col-span-2">
						<Label for="street" class="text-sm font-medium text-[#5C4024]">
							Street Address <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="street"
							bind:value={formData.address.street}
							oninput={handleInputChange}
							placeholder="123 Main Street"
							class="onboarding-input {errors['address.street'] ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors['address.street']}
							aria-describedby={errors['address.street'] ? 'street-error' : undefined}
						/>
						{#if errors['address.street']}
							<p id="street-error" class="text-xs text-[#EF4444]">{errors['address.street']}</p>
						{/if}
					</div>

					<!-- Street Address 2 -->
					<div class="space-y-2 md:col-span-2">
						<Label for="street2" class="text-sm font-medium text-[#5C4024]">
							Street Address 2
						</Label>
						<Input
							id="street2"
							bind:value={formData.address.street2}
							oninput={handleInputChange}
							placeholder="Apartment, suite, etc. (optional)"
							class="onboarding-input"
						/>
					</div>

					<!-- City -->
					<div class="space-y-2">
						<Label for="city" class="text-sm font-medium text-[#5C4024]">
							City <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="city"
							bind:value={formData.address.city}
							oninput={handleInputChange}
							placeholder="Harare"
							class="onboarding-input {errors['address.city'] ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors['address.city']}
							aria-describedby={errors['address.city'] ? 'city-error' : undefined}
						/>
						{#if errors['address.city']}
							<p id="city-error" class="text-xs text-[#EF4444]">{errors['address.city']}</p>
						{/if}
					</div>

					<!-- Postal Code -->
					<div class="space-y-2">
						<Label for="postalCode" class="text-sm font-medium text-[#5C4024]">
							Postal Code <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="postalCode"
							bind:value={formData.address.postalCode}
							oninput={handleInputChange}
							placeholder="00000"
							class="onboarding-input {errors['address.postalCode'] ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors['address.postalCode']}
							aria-describedby={errors['address.postalCode'] ? 'postalCode-error' : undefined}
						/>
						{#if errors['address.postalCode']}
							<p id="postalCode-error" class="text-xs text-[#EF4444]">{errors['address.postalCode']}</p>
						{/if}
					</div>

					<!-- Country -->
					<div class="space-y-2 md:col-span-2">
						<Label for="country" class="text-sm font-medium text-[#5C4024]">
							Country <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={countryOptions}
							bind:value={formData.address.country}
							placeholder="Select your country"
							class="onboarding-input"
						/>
					</div>
				</div>
			</div>

			<!-- Emergency Contact -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Emergency Contact
				</h3>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- Emergency Contact Name -->
					<div class="space-y-2">
						<Label for="emergencyName" class="text-sm font-medium text-[#5C4024]">
							Full Name <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="emergencyName"
							bind:value={formData.emergencyContact.name}
							oninput={handleInputChange}
							placeholder="John Doe"
							class="onboarding-input {errors['emergencyContact.name'] ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors['emergencyContact.name']}
							aria-describedby={errors['emergencyContact.name'] ? 'emergencyName-error' : undefined}
						/>
						{#if errors['emergencyContact.name']}
							<p id="emergencyName-error" class="text-xs text-[#EF4444]">{errors['emergencyContact.name']}</p>
						{/if}
					</div>

					<!-- Relationship -->
					<div class="space-y-2">
						<Label for="relationship" class="text-sm font-medium text-[#5C4024]">
							Relationship <span class="text-[#EF4444]">*</span>
						</Label>
						<Select
							options={relationshipOptions}
							bind:value={formData.emergencyContact.relationship}
							placeholder="Select relationship"
							class="onboarding-input {errors['emergencyContact.relationship'] ? 'border-[#EF4444]' : ''}"
						/>
						{#if errors['emergencyContact.relationship']}
							<p class="text-xs text-[#EF4444]">{errors['emergencyContact.relationship']}</p>
						{/if}
					</div>

					<!-- Emergency Contact Phone -->
					<div class="space-y-2 md:col-span-2">
						<Label for="emergencyPhone" class="text-sm font-medium text-[#5C4024]">
							Phone Number <span class="text-[#EF4444]">*</span>
						</Label>
						<Input
							id="emergencyPhone"
							type="tel"
							bind:value={formData.emergencyContact.phone}
							oninput={handleInputChange}
							placeholder="+263 77 123 4567"
							class="onboarding-input {errors['emergencyContact.phone'] ? 'border-[#EF4444]' : ''}"
							aria-invalid={!!errors['emergencyContact.phone']}
							aria-describedby={errors['emergencyContact.phone'] ? 'emergencyPhone-error' : undefined}
						/>
						{#if errors['emergencyContact.phone']}
							<p id="emergencyPhone-error" class="text-xs text-[#EF4444]">{errors['emergencyContact.phone']}</p>
						{/if}
					</div>
				</div>
			</div>

			<!-- Auto-save indicator -->
			{#if store.lastSaved}
				<div class="text-xs text-[#8A6A52] text-center py-2">
					Last saved: {store.lastSaved.toLocaleTimeString()}
				</div>
			{/if}
		</form>
	</OnboardingCard>
</div>
