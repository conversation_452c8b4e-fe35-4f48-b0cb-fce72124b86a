<script lang="ts">
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	// Redirect to main dashboard
	onMount(() => {
		goto('/', { replaceState: true });
	});
</script>

<!-- This page redirects to the main dashboard -->
<div class="flex items-center justify-center min-h-screen">
	<div class="text-center">
		<h1 class="text-2xl font-bold mb-2">Redirecting...</h1>
		<p class="text-muted-foreground">Taking you to the Employee Dashboard</p>
	</div>
</div>
