/* RTG-themed Sonner toast styles */
:root {
  --sonner-font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --sonner-normal-bg: hsl(var(--background));
  --sonner-normal-border: hsl(var(--border));
  --sonner-normal-text: hsl(var(--foreground));

  --sonner-success-bg: hsl(var(--background));
  --sonner-success-border: hsl(142.1 76.2% 36.3%);
  --sonner-success-text: hsl(142.1 76.2% 36.3%);

  --sonner-info-bg: hsl(var(--background));
  --sonner-info-border: hsl(221.2 83.2% 53.3%);
  --sonner-info-text: hsl(221.2 83.2% 53.3%);

  --sonner-warning-bg: hsl(var(--background));
  --sonner-warning-border: hsl(38 92% 50%);
  --sonner-warning-text: hsl(38 92% 50%);

  --sonner-error-bg: hsl(var(--background));
  --sonner-error-border: hsl(0 84.2% 60.2%);
  --sonner-error-text: hsl(0 84.2% 60.2%);
}

.dark {
  --sonner-normal-bg: hsl(var(--background));
  --sonner-normal-border: hsl(var(--border));
  --sonner-normal-text: hsl(var(--foreground));

  --sonner-success-bg: hsl(var(--background));
  --sonner-success-border: hsl(142.1 70.6% 45.3%);
  --sonner-success-text: hsl(142.1 70.6% 45.3%);

  --sonner-info-bg: hsl(var(--background));
  --sonner-info-border: hsl(217.2 91.2% 59.8%);
  --sonner-info-text: hsl(217.2 91.2% 59.8%);

  --sonner-warning-bg: hsl(var(--background));
  --sonner-warning-border: hsl(38 92% 50%);
  --sonner-warning-text: hsl(38 92% 50%);

  --sonner-error-bg: hsl(var(--background));
  --sonner-error-border: hsl(0 72.2% 50.6%);
  --sonner-error-text: hsl(0 72.2% 50.6%);
}

[data-sonner-toaster] {
  --width: 356px;
  --font-family: var(--sonner-font-family);
  --gray1: hsl(var(--muted));
  --gray2: hsl(var(--muted-foreground));
  --gray3: hsl(var(--popover));
  --gray4: hsl(var(--popover-foreground));
  --gray5: hsl(var(--muted));
  --gray6: hsl(var(--muted-foreground));
  --gray7: hsl(var(--popover));
  --gray8: hsl(var(--popover-foreground));
  --gray9: hsl(var(--muted));
  --gray10: hsl(var(--muted-foreground));
  --gray11: hsl(var(--muted-foreground));
  --gray12: hsl(var(--popover-foreground));
  --border-radius: calc(var(--radius) - 2px);
  --shadow: 0 4px 12px #0000001a;
  --shadow-border: rgba(255, 255, 255, 0.2);
}

[data-sonner-toast] {
  --y: translateY(0);
  --lift-amount: calc(var(--lift) * var(--gap));
  --window-width: 100vw;
  --width: var(--width);
  --window-height: 100vh;
  --front-z-index: 1000;
  --normal-bg: var(--sonner-normal-bg);
  --normal-border: var(--sonner-normal-border);
  --normal-text: var(--sonner-normal-text);
  --success-bg: var(--sonner-success-bg);
  --success-border: var(--sonner-success-border);
  --success-text: var(--sonner-success-text);
  --info-bg: var(--sonner-info-bg);
  --info-border: var(--sonner-info-border);
  --info-text: var(--sonner-info-text);
  --warning-bg: var(--sonner-warning-bg);
  --warning-border: var(--sonner-warning-border);
  --warning-text: var(--sonner-warning-text);
  --error-bg: var(--sonner-error-bg);
  --error-border: var(--sonner-error-border);
  --error-text: var(--sonner-error-text);
  --border-radius: var(--border-radius);
  --box-shadow: var(--shadow);
  --gap: 14px;
  --lift: 1;
  --scale: 1;
  --height: auto;
  --initial-height: 0px;
}

[data-sonner-toast][data-styled=true] {
  padding: 16px;
  background: var(--normal-bg);
  border: 1px solid var(--normal-border);
  color: var(--normal-text);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  width: var(--width);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

[data-sonner-toast]:focus-visible {
  box-shadow: 
    var(--box-shadow),
    0px 0px 0px 2px hsl(var(--ring));
}

[data-sonner-toast][data-type=success] {
  --normal-bg: var(--success-bg);
  --normal-border: var(--success-border);
  --normal-text: var(--success-text);
}

[data-sonner-toast][data-type=info] {
  --normal-bg: var(--info-bg);
  --normal-border: var(--info-border);
  --normal-text: var(--info-text);
}

[data-sonner-toast][data-type=warning] {
  --normal-bg: var(--warning-bg);
  --normal-border: var(--warning-border);
  --normal-text: var(--warning-text);
}

[data-sonner-toast][data-type=error] {
  --normal-bg: var(--error-bg);
  --normal-border: var(--error-border);
  --normal-text: var(--error-text);
}

[data-sonner-toast] [data-icon] {
  display: flex;
  height: 16px;
  width: 16px;
  position: relative;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
  margin-left: 0;
  margin-right: 8px;
}

[data-sonner-toast][data-type=success] [data-icon] {
  color: var(--success-text);
}

[data-sonner-toast][data-type=info] [data-icon] {
  color: var(--info-text);
}

[data-sonner-toast][data-type=warning] [data-icon] {
  color: var(--warning-text);
}

[data-sonner-toast][data-type=error] [data-icon] {
  color: var(--error-text);
}

[data-sonner-toast] [data-content] {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 2px;
  min-width: 0;
}

[data-sonner-toast] [data-title] {
  font-weight: 500;
  line-height: 1.5;
  color: var(--normal-text);
}

[data-sonner-toast] [data-description] {
  font-weight: 400;
  line-height: 1.4;
  color: hsl(var(--muted-foreground));
}

[data-sonner-toast] [data-close-button] {
  position: absolute;
  left: var(--width);
  right: 0;
  top: 0;
  height: 20px;
  width: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  background: var(--gray1);
  color: var(--gray12);
  border: 1px solid var(--gray4);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  cursor: pointer;
  z-index: 1;
  transition: all 100ms;
}

[data-sonner-toast] [data-close-button]:focus-visible {
  box-shadow: 
    0px 4px 12px #0000001a,
    0px 0px 0px 2px hsl(var(--ring));
}

/* Priority-based styling */
[data-sonner-toast].toast-priority-critical {
  border-width: 2px;
  box-shadow: var(--box-shadow), 0 0 0 1px hsl(var(--destructive) / 0.5);
}

[data-sonner-toast].toast-priority-high {
  border-width: 1.5px;
}

[data-sonner-toast].toast-priority-low {
  opacity: 0.9;
}

/* Action button styling */
[data-sonner-toast] [data-button] {
  border-radius: calc(var(--radius) - 2px);
  padding-left: 12px;
  padding-right: 12px;
  height: 24px;
  font-size: 12px;
  color: var(--normal-bg);
  background: var(--normal-text);
  margin-left: 8px;
  border: none;
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  transition: all 100ms;
  user-select: none;
}

[data-sonner-toast] [data-button]:focus-visible {
  box-shadow: 0px 0px 0px 2px hsl(var(--ring));
}

[data-sonner-toast] [data-button]:first-of-type {
  background: var(--normal-text);
  color: var(--normal-bg);
}

[data-sonner-toast] [data-cancel] {
  background: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
}
