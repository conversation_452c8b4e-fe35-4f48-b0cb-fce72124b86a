<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { BookOpen, Calendar, Clock, Users, CheckCircle, Plus } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	// Training data
	const mandatoryTrainings = [
		{
			id: 'hospitality-basics',
			title: 'Hospitality Excellence Fundamentals',
			description: 'Core principles of exceptional guest service',
			duration: '2 hours',
			format: 'Online Module',
			enrolled: false,
			completed: false
		},
		{
			id: 'safety-orientation',
			title: 'Workplace Safety Orientation',
			description: 'Essential safety procedures and protocols',
			duration: '1 hour',
			format: 'Online Module',
			enrolled: false,
			completed: false
		},
		{
			id: 'systems-training',
			title: 'Hotel Management Systems',
			description: 'Training on PMS, POS, and other operational systems',
			duration: '3 hours',
			format: 'Hands-on Workshop',
			enrolled: false,
			completed: false
		}
	];

	const optionalTrainings = [
		{
			id: 'cultural-awareness',
			title: 'Cultural Awareness & Sensitivity',
			description: 'Understanding diverse guest backgrounds and preferences',
			duration: '1.5 hours',
			format: 'Online Module',
			enrolled: false,
			completed: false
		},
		{
			id: 'leadership-basics',
			title: 'Leadership Development Basics',
			description: 'Foundational leadership skills for career growth',
			duration: '2 hours',
			format: 'Online Module',
			enrolled: false,
			completed: false
		}
	];

	// First week agenda
	const firstWeekAgenda = [
		{
			day: 'Monday',
			date: 'Day 1',
			activities: [
				{ time: '09:00', activity: 'Welcome & Office Tour', location: 'Reception' },
				{ time: '10:30', activity: 'Meet Your Team', location: 'Department Office' },
				{ time: '14:00', activity: 'IT Setup & System Access', location: 'IT Department' },
				{ time: '15:30', activity: 'HR Orientation Session', location: 'Training Room' }
			]
		},
		{
			day: 'Tuesday',
			date: 'Day 2',
			activities: [
				{ time: '09:00', activity: 'Department Overview', location: 'Department Office' },
				{ time: '11:00', activity: 'Shadow Senior Colleague', location: 'Various' },
				{ time: '14:00', activity: 'Hospitality Basics Training', location: 'Training Room' },
				{ time: '16:00', activity: 'Q&A with Manager', location: 'Manager Office' }
			]
		},
		{
			day: 'Wednesday',
			date: 'Day 3',
			activities: [
				{ time: '09:00', activity: 'Systems Training Workshop', location: 'Training Room' },
				{ time: '14:00', activity: 'Guest Service Simulation', location: 'Training Room' },
				{ time: '16:00', activity: 'Feedback Session', location: 'Manager Office' }
			]
		}
	];

	let selectedTrainings = new Set<string>();

	const enrollInTraining = (trainingId: string, isMandatory = false) => {
		const training = [...mandatoryTrainings, ...optionalTrainings].find(t => t.id === trainingId);
		if (training) {
			training.enrolled = true;
			if (isMandatory) {
				// Auto-complete mandatory trainings for demo
				setTimeout(() => {
					training.completed = true;
					uiStore.showSuccess('Training Completed', `${training.title} has been completed.`);
				}, 2000);
			}
			selectedTrainings.add(trainingId);
			uiStore.showSuccess('Enrolled Successfully', `You've been enrolled in ${training.title}.`);
		}
	};

	const enrollAllMandatory = () => {
		mandatoryTrainings.forEach(training => {
			if (!training.enrolled) {
				enrollInTraining(training.id, true);
			}
		});
	};

	const addToCalendar = () => {
		uiStore.showSuccess('Calendar Updated', 'Your first week agenda has been added to your calendar.');
	};

	const allMandatoryEnrolled = $derived(
		mandatoryTrainings.every(t => t.enrolled)
	);

	const handleNext = () => {
		if (!allMandatoryEnrolled) {
			uiStore.showError('Training Required', 'Please enroll in all mandatory training programs before continuing.');
			return;
		}

		// Save training data
		onboardingStore.updateData('training', {
			mandatoryTrainings: mandatoryTrainings.map(t => ({
				id: t.id,
				enrolled: t.enrolled,
				completed: t.completed,
				enrolledAt: t.enrolled ? new Date().toISOString() : null
			})),
			optionalTrainings: optionalTrainings.filter(t => t.enrolled).map(t => ({
				id: t.id,
				enrolled: t.enrolled,
				enrolledAt: new Date().toISOString()
			})),
			firstWeekScheduled: true
		});

		// Mark step as completed and move to next
		onboardingStore.completeStep('training');
		onboardingStore.setCurrentStep('assets');
		goto('/onboarding/assets');
	};

	const handleBack = () => {
		onboardingStore.setCurrentStep('policies');
		goto('/onboarding/policies');
	};
</script>

<svelte:head>
	<title>Training & First Week - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Training & First Week Plan"
		subtitle="Enroll in training programs and review your first week agenda. This will help you get up to speed quickly and confidently."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		nextButtonDisabled={!allMandatoryEnrolled}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-8">
			<!-- Mandatory Training -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-semibold text-[#1C1C1C]">Mandatory Training Programs</h3>
					<Button
						onclick={enrollAllMandatory}
						disabled={allMandatoryEnrolled}
						size="sm"
						class="onboarding-button-primary"
					>
						{#if allMandatoryEnrolled}
							<CheckCircle class="w-4 h-4 mr-2" />
							All Enrolled
						{:else}
							<Plus class="w-4 h-4 mr-2" />
							Enroll All
						{/if}
					</Button>
				</div>

				<div class="grid gap-4">
					{#each mandatoryTrainings as training}
						<div class="border border-[#EDE0CF] rounded-xl p-4 {training.enrolled ? 'bg-green-50 border-green-200' : ''}">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<div class="flex items-center gap-2 mb-2">
										<BookOpen class="w-5 h-5 text-[#C49A6C]" />
										<h4 class="font-semibold text-[#1C1C1C]">{training.title}</h4>
										{#if training.completed}
											<CheckCircle class="w-5 h-5 text-green-500" />
										{/if}
									</div>
									<p class="text-sm text-[#8A6A52] mb-3">{training.description}</p>
									<div class="flex items-center gap-4 text-xs text-[#8A6A52]">
										<div class="flex items-center gap-1">
											<Clock class="w-3 h-3" />
											{training.duration}
										</div>
										<span>{training.format}</span>
									</div>
								</div>
								<div>
									{#if training.completed}
										<Button variant="ghost" size="sm" class="text-green-600" disabled>
											<CheckCircle class="w-4 h-4 mr-2" />
											Completed
										</Button>
									{:else if training.enrolled}
										<Button variant="outline" size="sm" class="text-blue-600 border-blue-200">
											<BookOpen class="w-4 h-4 mr-2" />
											In Progress
										</Button>
									{:else}
										<Button
											onclick={() => enrollInTraining(training.id, true)}
											size="sm"
											class="onboarding-button-primary"
										>
											Enroll Now
										</Button>
									{/if}
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Optional Training -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold text-[#1C1C1C]">Optional Training Programs</h3>
				<p class="text-sm text-[#8A6A52]">These programs can help accelerate your career development.</p>

				<div class="grid gap-4">
					{#each optionalTrainings as training}
						<div class="border border-[#EDE0CF] rounded-xl p-4 {training.enrolled ? 'bg-blue-50 border-blue-200' : ''}">
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<div class="flex items-center gap-2 mb-2">
										<BookOpen class="w-5 h-5 text-[#C49A6C]" />
										<h4 class="font-semibold text-[#1C1C1C]">{training.title}</h4>
										<span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Optional</span>
									</div>
									<p class="text-sm text-[#8A6A52] mb-3">{training.description}</p>
									<div class="flex items-center gap-4 text-xs text-[#8A6A52]">
										<div class="flex items-center gap-1">
											<Clock class="w-3 h-3" />
											{training.duration}
										</div>
										<span>{training.format}</span>
									</div>
								</div>
								<div>
									{#if training.enrolled}
										<Button variant="outline" size="sm" class="text-blue-600 border-blue-200" disabled>
											<CheckCircle class="w-4 h-4 mr-2" />
											Enrolled
										</Button>
									{:else}
										<Button
											onclick={() => enrollInTraining(training.id)}
											variant="outline"
											size="sm"
										>
											Enroll
										</Button>
									{/if}
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- First Week Agenda -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<h3 class="text-lg font-semibold text-[#1C1C1C]">Your First Week Agenda</h3>
					<Button onclick={addToCalendar} variant="outline" size="sm">
						<Calendar class="w-4 h-4 mr-2" />
						Add to Calendar
					</Button>
				</div>

				<div class="space-y-4">
					{#each firstWeekAgenda as day}
						<div class="border border-[#EDE0CF] rounded-xl p-4">
							<h4 class="font-semibold text-[#1C1C1C] mb-3">{day.day} - {day.date}</h4>
							<div class="space-y-2">
								{#each day.activities as activity}
									<div class="flex items-center gap-3 text-sm">
										<div class="w-16 text-[#C49A6C] font-medium">{activity.time}</div>
										<div class="flex-1 text-[#1C1C1C]">{activity.activity}</div>
										<div class="text-[#8A6A52]">{activity.location}</div>
									</div>
								{/each}
							</div>
						</div>
					{/each}
				</div>
			</div>
		</div>
	</OnboardingCard>
</div>
