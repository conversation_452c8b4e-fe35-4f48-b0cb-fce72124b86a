// API Layer - Mock implementation for development
import type { 
	User, 
	Employee, 
	Department, 
	Organization, 
	Contract, 
	LeaveRequest, 
	Document,
	ApiResponse,
	PaginatedResponse,
	MockApiOptions 
} from '$lib/types/hr';

// Mock data
const mockOrganization: Organization = {
	id: 'org-1',
	name: 'Rainbow Tourism Group',
	code: 'RTG',
	logo_url: '/assets/images/rtg-logo.png',
	created_at: '2023-01-01T00:00:00Z',
	updated_at: '2023-01-01T00:00:00Z'
};

const mockDepartments: Department[] = [
	{
		id: 'dept-1',
		name: 'Human Resources',
		code: 'HR',
		org_id: 'org-1',
		manager_id: 'emp-2',
		created_at: '2023-01-01T00:00:00Z',
		updated_at: '2023-01-01T00:00:00Z'
	},
	{
		id: 'dept-2',
		name: 'Information Technology',
		code: 'IT',
		org_id: 'org-1',
		manager_id: 'emp-3',
		created_at: '2023-01-01T00:00:00Z',
		updated_at: '2023-01-01T00:00:00Z'
	},
	{
		id: 'dept-3',
		name: 'Finance',
		code: 'FIN',
		org_id: 'org-1',
		manager_id: 'emp-4',
		created_at: '2023-01-01T00:00:00Z',
		updated_at: '2023-01-01T00:00:00Z'
	}
];

const mockEmployees: Employee[] = [
	{
		id: 'emp-1',
		employee_number: 'RTG001',
		first_name: 'John',
		last_name: 'Doe',
		email: '<EMAIL>',
		phone: '+263 77 123 4567',
		department_id: 'dept-2',
		position: 'Software Developer',
		hire_date: '2023-01-15',
		status: 'active',
		auth_user_id: 'user-1',
		org_id: 'org-1',
		created_at: '2023-01-15T00:00:00Z',
		updated_at: '2023-01-15T00:00:00Z'
	},
	{
		id: 'emp-2',
		employee_number: 'RTG002',
		first_name: 'Jane',
		last_name: 'Smith',
		email: '<EMAIL>',
		phone: '+263 77 234 5678',
		department_id: 'dept-1',
		position: 'HR Manager',
		hire_date: '2022-06-01',
		status: 'active',
		auth_user_id: 'user-2',
		org_id: 'org-1',
		created_at: '2022-06-01T00:00:00Z',
		updated_at: '2022-06-01T00:00:00Z'
	}
];

// Utility function to simulate API delay and potential failures
async function mockApiCall<T>(
	data: T, 
	options: MockApiOptions = {}
): Promise<ApiResponse<T>> {
	const { delay = 500, shouldFail = false, failureRate = 0.1 } = options;
	
	// Simulate network delay
	await new Promise(resolve => setTimeout(resolve, delay));
	
	// Simulate random failures
	if (shouldFail || Math.random() < failureRate) {
		throw new Error('Mock API call failed');
	}
	
	return {
		data,
		success: true,
		message: 'Success'
	};
}

// Authentication API
export const authApi = {
	login: async (email: string, password: string): Promise<ApiResponse<{ user: User; employee: Employee }>> => {
		// Mock authentication logic
		const user: User = {
			id: 'user-1',
			email,
			user_role: email.includes('admin') ? 'hr_admin' : 
					  email.includes('manager') ? 'manager' : 'employee',
			org_id: 'org-1',
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString()
		};

		const employee = mockEmployees.find(emp => emp.email === email) || mockEmployees[0];

		return mockApiCall({ user, employee });
	},

	logout: async (): Promise<ApiResponse<null>> => {
		return mockApiCall(null, { delay: 200 });
	}
};

// Employee API
export const employeeApi = {
	getEmployees: async (page = 1, limit = 10): Promise<PaginatedResponse<Employee>> => {
		const start = (page - 1) * limit;
		const end = start + limit;
		const paginatedEmployees = mockEmployees.slice(start, end);

		await new Promise(resolve => setTimeout(resolve, 300));

		return {
			data: paginatedEmployees,
			total: mockEmployees.length,
			page,
			limit,
			totalPages: Math.ceil(mockEmployees.length / limit)
		};
	},

	getEmployee: async (id: string): Promise<ApiResponse<Employee | null>> => {
		const employee = mockEmployees.find(emp => emp.id === id) || null;
		return mockApiCall(employee);
	},

	createEmployee: async (employeeData: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Employee>> => {
		const newEmployee: Employee = {
			...employeeData,
			id: `emp-${Date.now()}`,
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString()
		};

		mockEmployees.push(newEmployee);
		return mockApiCall(newEmployee);
	},

	updateEmployee: async (id: string, updates: Partial<Employee>): Promise<ApiResponse<Employee>> => {
		const index = mockEmployees.findIndex(emp => emp.id === id);
		if (index === -1) {
			throw new Error('Employee not found');
		}

		mockEmployees[index] = {
			...mockEmployees[index],
			...updates,
			updated_at: new Date().toISOString()
		};

		return mockApiCall(mockEmployees[index]);
	}
};

// Department API
export const departmentApi = {
	getDepartments: async (): Promise<ApiResponse<Department[]>> => {
		return mockApiCall(mockDepartments);
	},

	getDepartment: async (id: string): Promise<ApiResponse<Department | null>> => {
		const department = mockDepartments.find(dept => dept.id === id) || null;
		return mockApiCall(department);
	}
};

// Organization API
export const organizationApi = {
	getOrganization: async (): Promise<ApiResponse<Organization>> => {
		return mockApiCall(mockOrganization);
	}
};

// Leave API
export const leaveApi = {
	getLeaveRequests: async (employeeId?: string): Promise<ApiResponse<LeaveRequest[]>> => {
		// Mock leave requests - would filter by employeeId in real implementation
		const mockLeaveRequests: LeaveRequest[] = [];
		return mockApiCall(mockLeaveRequests);
	},

	createLeaveRequest: async (leaveData: Omit<LeaveRequest, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<LeaveRequest>> => {
		const newLeaveRequest: LeaveRequest = {
			...leaveData,
			id: `leave-${Date.now()}`,
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString()
		};

		return mockApiCall(newLeaveRequest);
	}
};

// Document API
export const documentApi = {
	uploadDocument: async (file: File, metadata: any): Promise<ApiResponse<{ storage_path: string; status: 'pending_scan' }>> => {
		// Mock file upload
		const mockResponse = {
			storage_path: `documents/${Date.now()}-${file.name}`,
			status: 'pending_scan' as const
		};

		return mockApiCall(mockResponse, { delay: 2000 }); // Longer delay for file upload
	},

	getSignedUrl: async (bucket: string, path: string): Promise<ApiResponse<{ url: string }>> => {
		// Mock signed URL generation
		const mockUrl = `https://mock-storage.rtg.com/${bucket}/${path}?signed=true&expires=${Date.now() + 3600000}`;
		return mockApiCall({ url: mockUrl });
	}
};

// Export all APIs
export const api = {
	auth: authApi,
	employees: employeeApi,
	departments: departmentApi,
	organization: organizationApi,
	leave: leaveApi,
	documents: documentApi
};
