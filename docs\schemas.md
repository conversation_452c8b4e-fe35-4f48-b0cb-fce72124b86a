Great — below is a **complete, production-ready scaffold** you can drop into your Supabase project (SQL + RLS + Edge Function examples). It’s designed for your HRIMS (employees, contracts, documents) and covers:

* normalized, indexed Postgres schema (with org support),
* strong constraints and retention metadata,
* RLS policies using Supabase JWT custom-claims patterns,
* audit table + triggers,
* recommended indexes,
* two Supabase Edge Function examples:

  * **file-scan** flow (storage webhook → scanning service → quarantine/accept),
  * **signed-url** issuance (server-checked, uses service key to create time-limited URLs),
* environment variable and deployment notes,
* testing tips.

I relied on Supabase docs & best-practice guidance for RLS, signed URLs, and Edge Functions. Key references: Supabase RLS & custom claims, Storage signed URLs, Edge Functions, community discussion on file scanning. ([Supabase][1], [GitHub][2])

---

# 1) Database migration SQL (run in Supabase SQL editor or migration tool)

Save this as `001_create_hrims_schema.sql` and run it. It:

* creates schemas and enums,
* creates tables: organizations, departments, employees, contracts, documents, audit\_logs,
* adds indexes, triggers for audit, enables RLS, and creates RLS policies that use JWT claims.

```sql
-- 001_create_hrims_schema.sql
-- Requires: postgres extensions
-- Run as a supabase SQL migration (service role or admin)

-- 1) extensions
create extension if not exists "pgcrypto";  -- gen_random_uuid()

-- 2) Schemas (optional)
create schema if not exists hr;

-- 3) enums
create type hr.employee_status as enum ('active','probation','on_leave','suspended','terminated','resigned');
create type hr.contract_type as enum ('permanent','fixed_term','temporary','contractor');
create type hr.document_type as enum ('id','certificate','passport','payslip','contract','disciplinary','policy_ack','other');
create type hr.document_status as enum ('pending_scan','clean','infected','quarantined','archived');

-- 4) organizations (optional / multi-tenant)
create table if not exists hr.organizations (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  slug text unique,
  metadata jsonb,
  created_at timestamptz default now()
);

-- 5) departments
create table if not exists hr.departments (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid references hr.organizations(id) on delete cascade,
  name text not null,
  code text,
  manager_employee_id uuid null, -- FK to employees created later (deferred constraint)
  created_at timestamptz default now()
);

-- 6) employees
create table if not exists hr.employees (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid references hr.organizations(id) on delete cascade,
  auth_user_id uuid null,                 -- link to auth.users.id (Supabase Auth)
  employee_number text unique,            -- HR unique employee id (e.g., RTG-0001)
  first_name text not null,
  last_name text not null,
  email text not null,                    -- duplicate for easy queries, validated at app layer
  phone text,
  job_title text,
  grade text,
  department_id uuid references hr.departments(id) on delete set null,
  manager_id uuid references hr.employees(id) on delete set null,
  hire_date date,
  termination_date date,
  status hr.employee_status default 'probation',
  probation_end_date date,
  date_of_birth date,
  gender text,
  metadata jsonb,                         -- free-form (e.g., emergency contacts)
  created_by uuid,                        -- auth.uid() that created record
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- JS circular FK fix: make departments.manager_employee_id reference employees
alter table hr.departments add constraint fk_departments_manager
  foreign key (manager_employee_id) references hr.employees(id) on delete set null;

-- 7) contracts
create table if not exists hr.contracts (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid references hr.organizations(id) on delete cascade,
  employee_id uuid references hr.employees(id) on delete cascade,
  contract_type hr.contract_type not null default 'permanent',
  start_date date not null,
  end_date date null,
  probation_days integer default 90,
  salary numeric(12,2) null,
  currency text default 'USD',
  signed boolean default false,
  signed_at timestamptz null,
  signed_by uuid null,                     -- auth user that signed
  storage_path text,                        -- e.g. bucket/path/contract.pdf
  storage_bucket text,
  file_size bigint,
  file_checksum text,
  version integer default 1,
  is_active boolean default true,
  retention_until date,                     -- computed/managed
  created_by uuid,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- 8) documents
create table if not exists hr.documents (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid references hr.organizations(id) on delete cascade,
  employee_id uuid references hr.employees(id) on delete set null,
  contract_id uuid references hr.contracts(id) on delete set null,
  type hr.document_type not null default 'other',
  title text,
  storage_bucket text not null,
  storage_path text not null,               -- unique path within bucket
  mime_type text,
  file_size bigint,
  uploaded_by uuid,                         -- auth uid
  uploaded_at timestamptz default now(),
  status hr.document_status default 'pending_scan',
  scanned_at timestamptz,
  quarantine_reason text,
  tags jsonb,
  expires_at date,
  retention_until date,
  access_level text default 'private',      -- 'private'|'internal'|'public'
  metadata jsonb,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

-- 9) audit logs
create table if not exists hr.audit_logs (
  id uuid primary key default gen_random_uuid(),
  actor_id uuid,                 -- auth uid
  actor_role text,
  action text not null,
  target_table text,
  target_id text,
  payload jsonb,
  ip inet,
  user_agent text,
  created_at timestamptz default now()
);

-- 10) indexes
create index if not exists idx_employees_org on hr.employees (organization_id);
create index if not exists idx_employees_auth on hr.employees (auth_user_id);
create index if not exists idx_documents_emp on hr.documents (employee_id);
create index if not exists idx_documents_status on hr.documents (status);
create index if not exists idx_contracts_emp on hr.contracts (employee_id);
create index if not exists idx_documents_tags on hr.documents using gin (tags);

-- 11) triggers for updated_at
create or replace function hr.updated_at_trigger()
  returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger trg_employees_updated
  before update on hr.employees for each row execute function hr.updated_at_trigger();

create trigger trg_contracts_updated
  before update on hr.contracts for each row execute function hr.updated_at_trigger();

create trigger trg_documents_updated
  before update on hr.documents for each row execute function hr.updated_at_trigger();

-- 12) retention default helper (optional)
create or replace function hr.set_default_retention()
  returns trigger as $$
begin
  IF new.retention_until is null THEN
    -- Default retention: 6 years after uploaded_at (policy)
    new.retention_until = (coalesce(new.expires_at, (new.uploaded_at::date + interval '6 years')) )::date;
  END IF;
  return new;
end;
$$ language plpgsql;

create trigger trg_documents_retention
  before insert on hr.documents for each row execute function hr.set_default_retention();

-- 13) Enable Row-Level Security (RLS)
alter table hr.employees enable row level security;
alter table hr.contracts enable row level security;
alter table hr.documents enable row level security;
-- audit_logs remain accessible to admins only; you can enable RLS on it if desired.

-- 14) Helper function to read JWT claims (returns json)
create or replace function auth.jwt_claims() returns jsonb stable as $$
  select current_setting('request.jwt.claims', true)::jsonb;
$$ language sql;

-- 15) RLS policies
-- NOTES on usage:
-- The RLS policies below rely on a custom JWT claim field called "user_role" in the JWT (see Supabase custom claims docs).
-- That claim is available in SQL via current_setting('request.jwt.claims', true)::json->>'user_role'
-- Replace 'hr','admin','manager' roles with your project's role names if different.

-- EMPLOYEES policies
-- allow HR and admins to SELECT all employees
create policy "employees_select_hr_admin" on hr.employees
  for select
  using (
    (auth.jwt_claims() ->> 'user_role') in ('hr','admin')
  );

-- allow users to select their own employee record
create policy "employees_select_own" on hr.employees
  for select
  using (
    auth.uid() is not null and auth.uid()::text = auth_user_id::text
  );

-- allow HR/admin to INSERT
create policy "employees_insert_hr_admin" on hr.employees
  for insert
  with check (
    (auth.jwt_claims() ->> 'user_role') in ('hr','admin')
  );

-- allow user to update non-sensitive personal data about themselves (limit columns at application level)
create policy "employees_update_own" on hr.employees
  for update
  using ( auth.uid()::text = auth_user_id::text )
  with check ( auth.uid()::text = auth_user_id::text );

-- allow HR/admin to UPDATE all
create policy "employees_update_hr_admin" on hr.employees
  for update
  using ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin') )
  with check ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin') );

-- CONTRACTS policies
-- HR & admin can see all contracts
create policy "contracts_select_hr_admin" on hr.contracts
  for select using ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin') );

-- employee can SELECT their own contracts
create policy "contracts_select_own" on hr.contracts
  for select using ( auth.uid()::text = (select auth_user_id::text from hr.employees where hr.employees.id = hr.contracts.employee_id) );

-- HR/admin can INSERT/UPDATE/DELETE
create policy "contracts_manage_hr_admin" on hr.contracts
  for all
  using ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin') )
  with check ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin') );

-- DOCUMENTS policies
-- HR & admin can select all documents
create policy "documents_select_hr_admin" on hr.documents
  for select using ( (auth.jwt_claims() ->> 'user_role') in ('hr','admin','legal') );

-- Employee may select documents tied to them
create policy "documents_select_own" on hr.documents
  for select using (
    auth.uid()::text = coalesce(hr.documents.uploaded_by::text, (select auth_user_id::text from hr.employees where hr.employees.id = hr.documents.employee_id))
    OR (hr.documents.access_level = 'public')
  );

-- Managers: allow read for documents of employees in their department (using manager claim or lookup)
create policy "documents_select_manager_dept" on hr.documents
  for select using (
    (auth.jwt_claims() ->> 'user_role') = 'manager' and
    exists (
      select 1 from hr.employees e
      where e.id = hr.documents.employee_id
      and e.department_id = (select department_id from hr.employees where auth_user_id::text = auth.uid()::text limit 1)
    )
  );

-- Inserts: HR, admin or employee (uploading their own doc) can insert
create policy "documents_insert" on hr.documents
  for insert with check (
    (auth.jwt_claims() ->> 'user_role') in ('hr','admin') OR auth.uid()::text = new.uploaded_by::text
  );

-- Updates for scanning status: allow edge function service (use role claim 'service' or rely on service_role key)
create policy "documents_update_scan" on hr.documents
  for update using (
    (auth.jwt_claims() ->> 'user_role') in ('hr','admin','service')
    OR auth.uid()::text = new.uploaded_by::text
  ) with check (
    (auth.jwt_claims() ->> 'user_role') in ('hr','admin','service')
    OR auth.uid()::text = new.uploaded_by::text
  );

-- 16) Notes & maintenance: Grant SELECT on storage objects table so signed URLs can be created by RLS-allowed roles
-- Supabase requires SELECT on storage.objects for signed URLs to work. That is managed by Supabase internally for your auth role.

-- End of migration
```

**Why these choices & notes**

* `auth.jwt_claims()` wrapper makes policies readable and performant (pulls `request.jwt.claims` once). See Supabase custom claims guide. ([Supabase][3])
* Policies separate `SELECT` vs `INSERT/UPDATE/DELETE` and use `with check` to enforce write-time conditions. This protects against escalation. See RLS best practices. ([Supabase][1])
* Documents table uses `status` reflecting scanning lifecycle (`pending_scan`, `clean`, `infected`, `quarantined`) to let Edge Functions update securely. The Edge Function should run with a service role (or set `user_role = 'service'` in the JWT) when updating scan results. See community guidance on file scanning approaches. ([GitHub][2], [nikofischer.com][4])

---

# 2) Edge Function — File scan flow (TypeScript for Supabase Edge Functions)

**Overview / flow**

1. App uploads file into a **temporary** bucket (e.g., `uploads-temp`) using user auth token.
2. Supabase Storage emits a webhook (or you schedule a poll) -> Edge Function `file-scan` receives event `{bucket, path, metada...}`.
3. Edge Function downloads file using the **service role key** (Deno env `SUPABASE_SERVICE_ROLE_KEY`) and streams it to a malware scanning service:

   * Option A: **Self-hosted ClamAV REST API** (recommended for privacy & size).
   * Option B: **VirusTotal** or other managed scanning (mind privacy & file size).
4. If scan is **clean**:

   * Move file to permanent bucket (e.g., `documents`), update `hr.documents` row: `status='clean'`, `scanned_at=now()`, `storage_bucket`, `storage_path`, `file_checksum`.
   * Optionally notify uploader / HR.
5. If scan is **infected**:

   * Move file to `quarantine` bucket, set `status='infected'`, `quarantine_reason`, notify HR and block file from signed URL issuance.

> Important: Edge Function runs server-side with service-role privileges. Keep the service key in secrets — do not expose to client.

**Edge Function: `file-scan.ts`**

```ts
// file-scan.ts
// Supabase Edge Function (Deno) — triggers on storage webhook (or call manually for testing)
// Uses: @supabase/supabase-js (Deno build) - available in Supabase Edge functions
import { serve } from "std/server";
import { createClient } from "@supabase/supabase-js";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
const CLAMAV_SCAN_ENDPOINT = Deno.env.get("CLAMAV_SCAN_ENDPOINT")!; // e.g., https://clamav-scan.company/api/scan
const NOTIFY_WEBHOOK = Deno.env.get("NOTIFY_WEBHOOK"); // optional: HR notification webhook

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false }
});

serve(async (req) => {
  try {
    const evt = await req.json();
    // Supabase storage webhook structure: {bucket, name, time, etc} - verify with your webhook payload
    const bucket = evt?.bucket || evt?.record?.bucket;
    const path = evt?.name || evt?.record?.name;

    if (!bucket || !path) {
      return new Response(JSON.stringify({ error: "missing bucket/path" }), { status: 400 });
    }

    // 1) Download the file (service role)
    const download = await supabase.storage.from(bucket).download(path);
    if (download.error) {
      console.error("download error", download.error);
      return new Response(JSON.stringify({ error: "download failed" }), { status: 500 });
    }
    const fileArrayBuffer = await download.data.arrayBuffer();

    // 2) Send to scanning service (example: REST API that accepts file in multipart/form-data)
    const form = new FormData();
    const blob = new Blob([fileArrayBuffer]);
    form.append("file", blob, path.split("/").pop());

    // send to scanning endpoint
    const scanResp = await fetch(CLAMAV_SCAN_ENDPOINT, { method: "POST", body: form });
    const scanResult = await scanResp.json();

    // Expected scanResult: { status: 'clean' | 'infected', verdict: 'OK'|'FOUND', details: '...' }
    if (!scanResp.ok) {
      console.error("scan error", await scanResp.text());
      // mark as quarantined for safety
      await markDocumentQuarantined(bucket, path, "scan-service-failed");
      return new Response(JSON.stringify({ error: "scan failed" }), { status: 502 });
    }

    if (scanResult.status === "clean") {
      // Move file to permanent bucket (documents)
      const destBucket = "documents"; // make sure it exists
      // copy via presigned url or use supabase.storage.from.copy? Supabase JS doesn't have copy; download+upload:
      const uploadResp = await supabase.storage.from(destBucket).upload(path, blob, {
        cacheControl: "3600",
        upsert: false
      });

      if (uploadResp.error) {
        console.error("upload to documents error", uploadResp.error);
        await markDocumentQuarantined(bucket, path, "copy-failed");
        return new Response(JSON.stringify({ error: "upload failed" }), { status: 500 });
      }

      // update hr.documents row (set status clean). We assume that a documents row exists referencing storage_path. If not, create or update accordingly.
      await supabase.from("hr.documents").update({
        status: "clean",
        scanned_at: new Date().toISOString(),
        storage_bucket: destBucket,
        storage_path: path,
        file_size: (await blob.size) || null
      }).eq("storage_path", path);

      // remove from temp bucket to avoid duplicate (optional)
      await supabase.storage.from(bucket).remove([path]);

      // notify optionally
      if (NOTIFY_WEBHOOK) {
        await fetch(NOTIFY_WEBHOOK, {
          method: "POST",
          headers: {"content-type":"application/json"},
          body: JSON.stringify({ event: "document_scanned_clean", bucket: destBucket, path })
        });
      }

      return new Response(JSON.stringify({ status: "clean", path }), { status: 200 });
    } else {
      // infected -> move to quarantine bucket and set record
      const quarantineBucket = "quarantine";
      const uploadResp = await supabase.storage.from(quarantineBucket).upload(path, blob, { upsert: false });
      await supabase.from("hr.documents").update({
        status: "infected",
        scanned_at: new Date().toISOString(),
        quarantine_reason: scanResult.details || 'malware_detected',
        storage_bucket: quarantineBucket,
        storage_path: path
      }).eq("storage_path", path);

      // delete original if necessary
      await supabase.storage.from(bucket).remove([path]);

      // notify HR via webhook
      if (NOTIFY_WEBHOOK) {
        await fetch(NOTIFY_WEBHOOK, {
          method: "POST",
          headers: {"content-type":"application/json"},
          body: JSON.stringify({ event: "document_infected", bucket: quarantineBucket, path, details: scanResult })
        });
      }

      return new Response(JSON.stringify({ status: "infected", path }), { status: 200 });
    }
  } catch (err) {
    console.error(err);
    return new Response(JSON.stringify({ error: String(err) }), { status: 500 });
  }
});

// helper function to mark doc quarantined if no doc row exists
async function markDocumentQuarantined(bucket: string, path: string, reason: string) {
  await supabase.from("hr.documents").upsert({
    storage_bucket: bucket,
    storage_path: path,
    status: "quarantined",
    quarantine_reason: reason,
    uploaded_at: new Date().toISOString()
  }, { onConflict: ["storage_bucket","storage_path"] });
}
```

**Notes & deployment**

* `CLAMAV_SCAN_ENDPOINT` should be a private service you run (e.g., a small container exposing a `/scan` endpoint that streams input to ClamAV). This avoids sending private employee docs to third-party SaaS unless you accept privacy tradeoffs. Community guidance agrees Edge Functions + external scanner is recommended. ([GitHub][2], [nikofischer.com][4])
* Keep `SUPABASE_SERVICE_ROLE_KEY` in the Function's secrets (Supabase CLI / Dashboard secrets). The function uses service role so it can move objects and update DB irrespective of RLS. Protect this secret. ([Supabase][5])

---

# 3) Edge Function — Signed URL issuance (TypeScript)

**Purpose**: Only issue signed URLs when the caller has permission (document owner, HR, or allowed role). Uses Supabase auth inside Edge Function to get token and user claims (via `auth.getUser()`), verifies rights against `hr.documents` row, then calls Storage `createSignedUrl()` with service role or server key.

**Edge Function: `signed-url.ts`**

```ts
// signed-url.ts
import { serve } from "std/server";
import { createClient } from "@supabase/supabase-js";

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: { persistSession: false }
});

serve(async (req) => {
  try {
    if (req.method !== "POST") return new Response("Method not allowed", { status: 405 });

    const { bucket, path, expires_seconds = 60 } = await req.json();

    if (!bucket || !path) {
      return new Response(JSON.stringify({ error: "missing bucket/path" }), { status: 400 });
    }

    // Validate the caller: read Authorization header and use supabase-js to fetch user (edge function)
    const authHeader = req.headers.get("authorization") || "";
    const token = authHeader.replace("Bearer ", "");

    // If we have no token, reject (you can allow service-to-service calls if desired)
    if (!token) return new Response(JSON.stringify({ error: "unauthenticated" }), { status: 401 });

    // Use the client with the token to fetch session user info
    const userClient = createClient(SUPABASE_URL, token, { auth: { persistSession: false } });
    const { data: { user }, error: userErr } = await userClient.auth.getUser();

    if (userErr || !user) {
      return new Response(JSON.stringify({ error: "invalid token" }), { status: 401 });
    }

    // Get user's custom claim role (if present) via auth.getUser() app_metadata or use DB query to decide
    const userRole = (user.user_metadata && user.user_metadata.user_role) || (user.app_metadata && user.app_metadata.provider) || null;

    // 1) Check document access in DB using service role to avoid RLS surprises:
    const { data: docs, error } = await supabase
      .from("hr.documents")
      .select("id,employee_id,storage_bucket,storage_path,access_level")
      .eq("storage_bucket", bucket)
      .eq("storage_path", path)
      .limit(1)
      .maybeSingle();

    if (error) {
      console.error("db error", error);
      return new Response(JSON.stringify({ error: "db error" }), { status: 500 });
    }

    if (!docs) {
      return new Response(JSON.stringify({ error: "document not found" }), { status: 404 });
    }

    // Authorization logic (mirror your RLS rules)
    const isOwner = docs.employee_id && (docs.employee_id === user.id);
    const allowedRoles = ["hr","admin","legal"];
    const isPrivileged = userRole && allowedRoles.includes(userRole);

    if (!(isOwner || isPrivileged || docs.access_level === "public")) {
      return new Response(JSON.stringify({ error: "forbidden" }), { status: 403 });
    }

    // Create signed URL using service role key
    const signed = await supabase.storage.from(bucket).createSignedUrl(path, expires_seconds);

    if (signed.error) {
      console.error("signed url error", signed.error);
      return new Response(JSON.stringify({ error: "failed to create signed url" }), { status: 500 });
    }

    return new Response(JSON.stringify({ url: signed.data.signedUrl }), {
      status: 200,
      headers: { "content-type": "application/json" }
    });

  } catch (err) {
    console.error(err);
    return new Response(JSON.stringify({ error: String(err) }), { status: 500 });
  }
});
```

**Notes**

* You must pass the user’s JWT from the client to the function in the `Authorization: Bearer <token>` header.
* The function uses the **service role key** to read the DB and call `createSignedUrl()` — keep the key secret.
* Per Supabase docs, the underlying `storage.objects` table must allow `select` for the signed URL call to succeed; using the service role avoids per-row RLS issues. See docs for `create_signed_url`. ([Supabase][6])

---

# 4) Additional operational & security notes

* **Custom claims for roles**: Add a `user_role` claim to JWT via Supabase Auth Hooks (or set via admin API) so RLS policies can check `auth.jwt_claims() ->> 'user_role'`. This is fast and avoids extra DB lookups. See Supabase custom claims guide. ([Supabase][3])
* **Service role usage**: Only use the `SUPABASE_SERVICE_ROLE_KEY` inside trusted server code (Edge Functions). Never embed it client-side. Edge Functions can store secrets via Supabase CLI or Dashboard. ([Supabase][5])
* **Storage strategy**:

  * `uploads-temp` (public write by authenticated users) for initial uploads,
  * `documents` (private) for accepted/clean files,
  * `quarantine` for infected files,
  * configure bucket policies and lifecycle rules (e.g., auto-delete temp files older than X).
* **Scanning**: Running ClamAV in a container you control is the safest privacy choice (community guidance). If you choose VirusTotal or other SaaS, confirm legal/privacy constraints for employee documents. ([nikofischer.com][4], [GitHub][2])
* **Auditing**: Use `hr.audit_logs` table and create small logging helpers (DB function or Edge Function wrapper) to write actor, role, IP, action. Consider also streaming critical logs to an external logging service (Sentry/Datadog).
* **Testing**: Simulate uploads with test files, trigger webhook manually, and test both clean and infected responses (ClamAV has EICAR test string).
* **Signed URL TTL**: Keep short (e.g., 60–300s) for sensitive docs. For long-term sharing, generate a one-time download bundle and audit its creation.

---

# 5) How to test locally / deploy

1. **Local dev**

   * Use Supabase CLI (`supabase start`) to run local Postgres + Auth + Storage when possible.
   * Deploy Edge Functions locally with `supabase functions serve`.
   * Provide env secrets: `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`, `CLAMAV_SCAN_ENDPOINT`, `NOTIFY_WEBHOOK`.

2. **Staging**

   * Seed a test org, create a test HR user with `user_role = 'hr'` in custom claims or via admin metadata.
   * Upload EICAR test file (ClamAV test) to `uploads-temp` to simulate infected flow.
   * Verify record states change in `hr.documents` and files move to `quarantine` or `documents` buckets.

3. **Production**

   * Protect service role key, enable backups and PITR on Supabase project, set retention policies.
   * Monitor Edge Function logs and set alerts for scan failures / high quarantine rates.

---

# 6) Quick checklist / next steps I can scaffold for you

* [ ] Create the actual Supabase SQL migration file and execute it in your project.
* [ ] Create the Edge Function files (`file-scan.ts`, `signed-url.ts`) and set the required secrets in Supabase.
* [ ] Provide a small SvelteKit route + client example to:

  * upload to `uploads-temp`,
  * show upload progress,
  * poll for status and request signed URL after scan succeeds.
* [ ] Provide sample ClamAV Docker container + REST wrapper you can deploy for scanning (I can scaffold this too).

Would you like me to:
A) generate the **exact Supabase CLI command / migration file** ready to run, and the **Edge Function zip** for direct deploy, **or**
B) scaffold the SvelteKit client upload + server call example next?

[1]: https://supabase.com/docs/guides/database/postgres/row-level-security?utm_source=chatgpt.com "Row Level Security | Supabase Docs"
[2]: https://github.com/orgs/supabase/discussions/23645?utm_source=chatgpt.com "File scanning on storage · supabase · Discussion #23645"
[3]: https://supabase.com/docs/guides/database/postgres/custom-claims-and-role-based-access-control-rbac?utm_source=chatgpt.com "Custom Claims & Role-based Access Control (RBAC)"
[4]: https://nikofischer.com/supabase-storage-file-upload-guide?utm_source=chatgpt.com "Supabase Storage: How to Implement File Upload Properly"
[5]: https://supabase.com/docs/guides/functions?utm_source=chatgpt.com "Edge Functions | Supabase Docs"
[6]: https://supabase.com/docs/reference/javascript/storage-from-createsignedurl?utm_source=chatgpt.com "JavaScript: Create a signed URL"
