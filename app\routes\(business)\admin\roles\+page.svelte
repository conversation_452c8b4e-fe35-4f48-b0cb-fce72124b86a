<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { 
		Shield, 
		User, 
		Users, 
		Crown, 
		Settings,
		CheckCircle,
		AlertTriangle,
		Info
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const employee = $derived(auth.employee);

	// Available roles for simulation
	const availableRoles = [
		{
			id: 'employee',
			name: 'Employee',
			description: 'Basic employee access with self-service capabilities',
			icon: User,
			color: 'bg-blue-100 text-blue-800',
			permissions: [
				'View own profile',
				'Apply for leave',
				'View leave balance',
				'Update personal information',
				'View company announcements'
			]
		},
		{
			id: 'manager',
			name: 'Manager',
			description: 'Team management capabilities with employee oversight',
			icon: Users,
			color: 'bg-green-100 text-green-800',
			permissions: [
				'All employee permissions',
				'Approve/reject team leave requests',
				'View team member profiles',
				'Access team reports',
				'Manage team schedules',
				'Create job requisitions'
			]
		},
		{
			id: 'hr_admin',
			name: 'HR Administrator',
			description: 'Full HR management access across the organization',
			icon: Shield,
			color: 'bg-purple-100 text-purple-800',
			permissions: [
				'All manager permissions',
				'Manage all employees',
				'Access all HR modules',
				'Configure leave policies',
				'Manage recruitment process',
				'Generate HR reports',
				'Manage employee contracts'
			]
		},
		{
			id: 'super_admin',
			name: 'Super Administrator',
			description: 'Complete system access with administrative privileges',
			icon: Crown,
			color: 'bg-red-100 text-red-800',
			permissions: [
				'All HR admin permissions',
				'System configuration',
				'User role management',
				'Security settings',
				'Data export/import',
				'System maintenance',
				'Audit logs access'
			]
		}
	];

	let isChangingRole = $state(false);

	const handleRoleChange = async (newRole: string) => {
		if (newRole === user?.user_role) {
			notificationStore.info('Role Switch', 'You are already in this role');
			return;
		}

		isChangingRole = true;

		try {
			// Simulate role change
			await new Promise(resolve => setTimeout(resolve, 1500));

			// Update the auth store with new role
			authStore.switchRole(newRole as any);

			const roleName = availableRoles.find(r => r.id === newRole)?.name || newRole;
			notificationStore.success('Role Changed', `Successfully switched to ${roleName} role`);
		} catch (error) {
			console.error('Error changing role:', error);
			notificationStore.error('Role Change Failed', 'Failed to switch role');
		} finally {
			isChangingRole = false;
		}
	};

	const getCurrentRoleInfo = () => {
		return availableRoles.find(role => role.id === user?.user_role) || availableRoles[0];
	};

	const currentRole = $derived(getCurrentRoleInfo());
</script>

<svelte:head>
	<title>Role Simulator - HRIMS Admin</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Role Simulator</h1>
			<p class="text-muted-foreground">Switch between different user roles to test RBAC functionality</p>
		</div>
		<Badge class="bg-yellow-100 text-yellow-800">
			<AlertTriangle class="w-4 h-4 mr-1" />
			Development Only
		</Badge>
	</div>

	<!-- Current Role Info -->
	<Card class="p-6">
		<div class="flex items-center gap-4 mb-4">
			<div class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
				{#if currentRole}
					<currentRole.icon class="w-6 h-6 text-primary-foreground" />
				{/if}
			</div>
			<div>
				<h2 class="text-xl font-semibold text-foreground">Current Role: {currentRole?.name}</h2>
				<p class="text-muted-foreground">{currentRole?.description}</p>
			</div>
			{#if currentRole}
				<Badge class={currentRole.color}>
					<CheckCircle class="w-3 h-3 mr-1" />
					Active
				</Badge>
			{/if}
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div>
				<h3 class="text-lg font-semibold text-foreground mb-3">User Information</h3>
				<div class="space-y-2 text-sm">
					<div class="flex justify-between">
						<span class="text-muted-foreground">Email:</span>
						<span class="text-foreground">{user?.email}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-muted-foreground">Role:</span>
						<span class="text-foreground">{currentRole?.name}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-muted-foreground">Employee Name:</span>
						<span class="text-foreground">{employee?.first_name} {employee?.last_name}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-muted-foreground">Department:</span>
						<span class="text-foreground">{employee?.department}</span>
					</div>
				</div>
			</div>

			<div>
				<h3 class="text-lg font-semibold text-foreground mb-3">Current Permissions</h3>
				<div class="space-y-1">
					{#if currentRole}
						{#each currentRole.permissions as permission}
							<div class="flex items-center gap-2 text-sm">
								<CheckCircle class="w-4 h-4 text-green-600" />
								<span class="text-foreground">{permission}</span>
							</div>
						{/each}
					{/if}
				</div>
			</div>
		</div>
	</Card>

	<!-- Role Selection -->
	<div>
		<h2 class="text-xl font-semibold text-foreground mb-4">Switch Role</h2>
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
			{#each availableRoles as role}
				{@const RoleIcon = role.icon}
				<Card class="p-6 hover:shadow-lg transition-shadow {user?.user_role === role.id ? 'ring-2 ring-primary' : ''}">
					<div class="text-center">
						<div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
							<RoleIcon class="w-8 h-8 text-primary-foreground" />
						</div>
						<h3 class="text-lg font-semibold text-foreground mb-2">{role.name}</h3>
						<p class="text-sm text-muted-foreground mb-4">{role.description}</p>
						
						{#if user?.user_role === role.id}
							<Badge class={role.color}>
								<CheckCircle class="w-3 h-3 mr-1" />
								Current Role
							</Badge>
						{:else}
							<Button 
								variant="outline" 
								class="w-full" 
								onclick={() => handleRoleChange(role.id)}
								disabled={isChangingRole}
							>
								{isChangingRole ? 'Switching...' : 'Switch to Role'}
							</Button>
						{/if}
					</div>
				</Card>
			{/each}
		</div>
	</div>

	<!-- Role Comparison -->
	<Card class="p-6">
		<h2 class="text-xl font-semibold text-foreground mb-4">Role Permissions Comparison</h2>
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead>
					<tr class="border-b border-border">
						<th class="text-left py-3 px-4 font-medium text-foreground">Permission</th>
						{#each availableRoles as role}
							<th class="text-center py-3 px-4 font-medium text-foreground">{role.name}</th>
						{/each}
					</tr>
				</thead>
				<tbody>
					{#each [...new Set(availableRoles.flatMap(r => r.permissions))] as permission}
						<tr class="border-b border-border hover:bg-muted/50">
							<td class="py-3 px-4 text-sm text-foreground">{permission}</td>
							{#each availableRoles as role}
								<td class="text-center py-3 px-4">
									{#if role.permissions.includes(permission)}
										<CheckCircle class="w-5 h-5 text-green-600 mx-auto" />
									{:else}
										<div class="w-5 h-5 mx-auto"></div>
									{/if}
								</td>
							{/each}
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</Card>

	<!-- Development Notice -->
	<Card class="p-6 bg-yellow-50 border-yellow-200">
		<div class="flex items-start gap-3">
			<Info class="w-6 h-6 text-yellow-600 mt-0.5" />
			<div>
				<h3 class="text-lg font-semibold text-yellow-900 mb-2">Development Tool Notice</h3>
				<p class="text-yellow-800 mb-3">
					This role simulator is designed for development and testing purposes only. It allows you to switch between different user roles to test the Role-Based Access Control (RBAC) functionality of the HRIMS system.
				</p>
				<ul class="text-sm text-yellow-700 space-y-1">
					<li>• Role changes are temporary and reset on page refresh</li>
					<li>• All role switches are logged for development purposes</li>
					<li>• This page should not be accessible in production environments</li>
					<li>• Use this tool to verify that different roles see appropriate content and functionality</li>
				</ul>
			</div>
		</div>
	</Card>
</div>
