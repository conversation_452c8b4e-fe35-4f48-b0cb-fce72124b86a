<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import Modal from '$lib/components/ui/Modal.svelte';
	import { 
		Search, 
		Filter, 
		Plus, 
		Eye, 
		Edit, 
		Download,
		FileText,
		Calendar,
		User,
		Building,
		Clock,
		CheckCircle,
		XCircle,
		AlertTriangle,
		Trash2
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock contracts data
	let contracts = $state([
		{
			id: 1,
			employee_name: '<PERSON>',
			employee_id: 'RTG001',
			contract_type: 'Permanent',
			position: 'Senior Software Developer',
			department: 'Information Technology',
			start_date: '2024-01-15',
			end_date: null,
			salary: 75000,
			currency: 'USD',
			status: 'active',
			created_date: '2024-01-10',
			created_by: 'HR Admin',
			last_modified: '2024-01-15',
			contract_number: 'RTG-2024-001',
			probation_period: 6,
			notice_period: 30
		},
		{
			id: 2,
			employee_name: '<PERSON>',
			employee_id: 'RTG002',
			contract_type: 'Fixed Term',
			position: 'Marketing Manager',
			department: 'Marketing',
			start_date: '2024-02-01',
			end_date: '2025-01-31',
			salary: 60000,
			currency: 'USD',
			status: 'active',
			created_date: '2024-01-25',
			created_by: 'HR Admin',
			last_modified: '2024-02-01',
			contract_number: 'RTG-2024-002',
			probation_period: 3,
			notice_period: 30
		},
		{
			id: 3,
			employee_name: 'Mike Johnson',
			employee_id: 'RTG003',
			contract_type: 'Contract',
			position: 'Project Consultant',
			department: 'Operations',
			start_date: '2024-03-01',
			end_date: '2024-08-31',
			salary: 45000,
			currency: 'USD',
			status: 'pending_signature',
			created_date: '2024-02-20',
			created_by: 'HR Admin',
			last_modified: '2024-02-25',
			contract_number: 'RTG-2024-003',
			probation_period: 0,
			notice_period: 14
		},
		{
			id: 4,
			employee_name: 'Emily Brown',
			employee_id: 'RTG004',
			contract_type: 'Permanent',
			position: 'Financial Analyst',
			department: 'Finance',
			start_date: '2023-06-01',
			end_date: null,
			salary: 55000,
			currency: 'USD',
			status: 'expired',
			created_date: '2023-05-15',
			created_by: 'HR Admin',
			last_modified: '2024-01-01',
			contract_number: 'RTG-2023-015',
			probation_period: 6,
			notice_period: 30
		}
	]);

	// Filter and search state
	let searchTerm = $state('');
	let statusFilter = $state('all');
	let departmentFilter = $state('all');
	let contractTypeFilter = $state('all');

	// Modal state
	let showDeleteModal = $state(false);
	let selectedContract = $state<typeof contracts[0] | null>(null);
	let isDeleting = $state(false);

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Status' },
		{ value: 'active', label: 'Active' },
		{ value: 'pending_signature', label: 'Pending Signature' },
		{ value: 'expired', label: 'Expired' },
		{ value: 'terminated', label: 'Terminated' },
		{ value: 'draft', label: 'Draft' }
	];

	const departmentOptions = [
		{ value: 'all', label: 'All Departments' },
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Operations', label: 'Operations' },
		{ value: 'Human Resources', label: 'Human Resources' }
	];

	const contractTypeOptions = [
		{ value: 'all', label: 'All Types' },
		{ value: 'Permanent', label: 'Permanent' },
		{ value: 'Fixed Term', label: 'Fixed Term' },
		{ value: 'Contract', label: 'Contract' },
		{ value: 'Internship', label: 'Internship' },
		{ value: 'Probation', label: 'Probation' }
	];

	// Computed filtered contracts
	const filteredContracts = $derived.by(() => {
		if (!contracts || !Array.isArray(contracts)) {
			return [];
		}

		return contracts.filter(contract => {
			if (!contract) return false;

			const matchesSearch = searchTerm === '' ||
				(contract.employee_name && contract.employee_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
				(contract.employee_id && contract.employee_id.toLowerCase().includes(searchTerm.toLowerCase())) ||
				(contract.contract_number && contract.contract_number.toLowerCase().includes(searchTerm.toLowerCase()));

			const matchesStatus = statusFilter === 'all' || contract.status === statusFilter;
			const matchesDepartment = departmentFilter === 'all' || contract.department === departmentFilter;
			const matchesType = contractTypeFilter === 'all' || contract.contract_type === contractTypeFilter;

			return matchesSearch && matchesStatus && matchesDepartment && matchesType;
		});
	});

	// Status styling
	const getStatusInfo = (status: string) => {
		const statusMap = {
			active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
			pending_signature: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
			expired: { color: 'bg-red-100 text-red-800', icon: XCircle },
			terminated: { color: 'bg-gray-100 text-gray-800', icon: XCircle },
			draft: { color: 'bg-blue-100 text-blue-800', icon: Edit }
		};
		return statusMap[status as keyof typeof statusMap] || statusMap.draft;
	};

	// Actions
	const handleCreateContract = () => {
		goto('/contracts/create');
	};

	const handleViewContract = (id: number) => {
		goto(`/contracts/${id}`);
	};

	const handleEditContract = (id: number) => {
		goto(`/contracts/${id}/edit`);
	};

	const handleDeleteContract = (contract: typeof contracts[0]) => {
		selectedContract = contract;
		showDeleteModal = true;
	};

	const confirmDelete = async () => {
		if (!selectedContract) return;

		isDeleting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Remove from array
			contracts = contracts.filter(c => c.id !== selectedContract!.id);

			notificationStore.success('Contract Deleted', 'Contract has been successfully deleted');
			showDeleteModal = false;
		} catch (error) {
			console.error('Error deleting contract:', error);
			notificationStore.error('Delete Failed', 'Failed to delete contract');
		} finally {
			isDeleting = false;
		}
	};

	const handleDownloadContract = (contract: typeof contracts[0]) => {
		// In real app, this would download the actual contract document
		notificationStore.info('Download Started', `Contract ${contract.contract_number} download has started`);
	};

	const canManageContracts = $derived(user?.user_role === 'hr_admin' || user?.user_role === 'super_admin');
</script>

<svelte:head>
	<title>Contracts - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Contract Management</h1>
			<p class="text-muted-foreground">Manage employee contracts and agreements</p>
		</div>
		{#if canManageContracts}
			<Button variant="default" onclick={handleCreateContract}>
				<Plus class="w-4 h-4" />
				Create Contract
			</Button>
		{/if}
	</div>

	<!-- Filters -->
	<Card class="p-4">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
			<div>
				<Input
					placeholder="Search contracts..."
					bind:value={searchTerm}
					class="w-full"
				>
					<Search slot="icon" class="w-4 h-4" />
				</Input>
			</div>
			<div>
				<Select
					options={statusOptions}
					bind:value={statusFilter}
					placeholder="Filter by status"
				/>
			</div>
			<div>
				<Select
					options={departmentOptions}
					bind:value={departmentFilter}
					placeholder="Filter by department"
				/>
			</div>
			<div>
				<Select
					options={contractTypeOptions}
					bind:value={contractTypeFilter}
					placeholder="Filter by type"
				/>
			</div>
			<div class="flex items-center">
				<Button variant="outline" class="w-full">
					<Filter class="w-4 h-4" />
					More Filters
				</Button>
			</div>
		</div>
	</Card>

	<!-- Summary Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Contracts</p>
					<p class="text-2xl font-bold text-foreground">{filteredContracts.length}</p>
				</div>
				<FileText class="w-8 h-8 text-muted-foreground" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Active</p>
					<p class="text-2xl font-bold text-green-600">
						{filteredContracts.filter(c => c.status === 'active').length}
					</p>
				</div>
				<CheckCircle class="w-8 h-8 text-green-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Pending Signature</p>
					<p class="text-2xl font-bold text-yellow-600">
						{filteredContracts.filter(c => c.status === 'pending_signature').length}
					</p>
				</div>
				<Clock class="w-8 h-8 text-yellow-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Expired</p>
					<p class="text-2xl font-bold text-red-600">
						{filteredContracts.filter(c => c.status === 'expired').length}
					</p>
				</div>
				<XCircle class="w-8 h-8 text-red-600" />
			</div>
		</Card>
	</div>

	<!-- Contracts Table -->
	<Card class="overflow-hidden">
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead class="bg-muted/50">
					<tr>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Employee
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Contract Details
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Duration
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Salary
						</th>
						<th class="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
							Status
						</th>
						{#if canManageContracts}
							<th class="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
								Actions
							</th>
						{/if}
					</tr>
				</thead>
				<tbody class="bg-background divide-y divide-border">
					{#each filteredContracts as contract}
						{@const statusInfo = getStatusInfo(contract.status)}
						{@const StatusIcon = statusInfo.icon}
						<tr class="hover:bg-muted/50 transition-colors">
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="flex items-center">
									<div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
										<span class="text-primary-foreground font-medium text-sm">
											{contract.employee_name.split(' ').map(n => n.charAt(0)).join('')}
										</span>
									</div>
									<div class="ml-3">
										<div class="text-sm font-medium text-foreground">{contract.employee_name}</div>
										<div class="text-sm text-muted-foreground">{contract.employee_id}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-foreground">{contract.contract_number}</div>
								<div class="text-sm text-muted-foreground">{contract.contract_type} • {contract.position}</div>
								<div class="text-sm text-muted-foreground">{contract.department}</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm text-foreground">
									Start: {new Date(contract.start_date).toLocaleDateString()}
								</div>
								{#if contract.end_date}
									<div class="text-sm text-muted-foreground">
										End: {new Date(contract.end_date).toLocaleDateString()}
									</div>
								{:else}
									<div class="text-sm text-muted-foreground">Permanent</div>
								{/if}
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="text-sm font-medium text-foreground">
									{contract.currency} {contract.salary.toLocaleString()}
								</div>
								<div class="text-sm text-muted-foreground">Annual</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<Badge class={statusInfo.color}>
									<StatusIcon class="w-3 h-3 mr-1" />
									{contract.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
								</Badge>
							</td>
							{#if canManageContracts}
								<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
									<div class="flex items-center justify-end gap-2">
										<Button variant="outline" size="sm" onclick={() => handleViewContract(contract.id)}>
											<Eye class="w-4 h-4" />
											View
										</Button>
										<Button variant="outline" size="sm" onclick={() => handleDownloadContract(contract)}>
											<Download class="w-4 h-4" />
											Download
										</Button>
										<Button variant="outline" size="sm" onclick={() => handleEditContract(contract.id)}>
											<Edit class="w-4 h-4" />
											Edit
										</Button>
										<Button variant="destructive" size="sm" onclick={() => handleDeleteContract(contract)}>
											<Trash2 class="w-4 h-4" />
											Delete
										</Button>
									</div>
								</td>
							{/if}
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</Card>

	{#if filteredContracts.length === 0}
		<Card class="p-12 text-center">
			<FileText class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-semibold text-foreground mb-2">No contracts found</h3>
			<p class="text-muted-foreground">Try adjusting your search criteria or filters.</p>
			{#if canManageContracts}
				<Button variant="default" class="mt-4" onclick={handleCreateContract}>
					<Plus class="w-4 h-4" />
					Create First Contract
				</Button>
			{/if}
		</Card>
	{/if}
</div>

<!-- Delete Confirmation Modal -->
<Modal bind:open={showDeleteModal} title="Delete Contract" size="md">
	{#snippet children()}
		{#if selectedContract}
			<div class="space-y-4">
				<div class="flex items-center gap-3 p-4 bg-red-50 rounded-lg">
					<AlertTriangle class="w-6 h-6 text-red-600" />
					<div>
						<h4 class="font-medium text-red-900">Are you sure you want to delete this contract?</h4>
						<p class="text-sm text-red-700">This action cannot be undone.</p>
					</div>
				</div>
				
				<div class="p-4 bg-muted rounded-lg">
					<h4 class="font-medium text-foreground mb-2">Contract Details</h4>
					<div class="space-y-1 text-sm">
						<div><span class="text-muted-foreground">Employee:</span> {selectedContract.employee_name}</div>
						<div><span class="text-muted-foreground">Contract Number:</span> {selectedContract.contract_number}</div>
						<div><span class="text-muted-foreground">Type:</span> {selectedContract.contract_type}</div>
						<div><span class="text-muted-foreground">Status:</span> {selectedContract.status}</div>
					</div>
				</div>
			</div>
		{/if}
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showDeleteModal = false} disabled={isDeleting}>
			Cancel
		</Button>
		<Button variant="destructive" onclick={confirmDelete} disabled={isDeleting}>
			{isDeleting ? 'Deleting...' : 'Delete Contract'}
		</Button>
	{/snippet}
</Modal>
