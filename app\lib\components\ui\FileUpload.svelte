<script lang="ts">
	// RTG-themed FileUpload component with drag & drop
	import { Upload, X, File, AlertCircle } from '@lucide/svelte';
	import { Button } from './button/index.js';
	import { Label } from './label/index.js';

	interface Props {
		label?: string;
		accept?: string;
		multiple?: boolean;
		maxSize?: number; // in MB
		maxFiles?: number;
		disabled?: boolean;
		required?: boolean;
		error?: string;
		hint?: string;
		files?: File[];
		class?: string;
		onFilesChange?: (files: File[]) => void;
	}

	let {
		label,
		accept = '*/*',
		multiple = false,
		maxSize = 10, // 10MB default
		maxFiles = multiple ? 5 : 1,
		disabled = false,
		required = false,
		error,
		hint,
		files = $bindable([]),
		class: className = '',
		onFilesChange
	}: Props = $props();

	let isDragOver = $state(false);
	let fileInput: HTMLInputElement;
	let uploadError = $state('');

	const uploadId = `upload-${Math.random().toString(36).substring(2, 11)}`;

	const validateFile = (file: File): string | null => {
		// Check file size
		if (file.size > maxSize * 1024 * 1024) {
			return `File "${file.name}" is too large. Maximum size is ${maxSize}MB.`;
		}

		// Check file type if accept is specified and not wildcard
		if (accept !== '*/*' && !accept.includes('*')) {
			const acceptedTypes = accept.split(',').map(type => type.trim());
			const fileType = file.type;
			const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
			
			const isAccepted = acceptedTypes.some(type => 
				type === fileType || type === fileExtension
			);
			
			if (!isAccepted) {
				return `File "${file.name}" is not an accepted file type.`;
			}
		}

		return null;
	};

	const handleFiles = (newFiles: FileList | null) => {
		if (!newFiles) return;

		uploadError = '';
		const fileArray = Array.from(newFiles);
		const validFiles: File[] = [];

		// Validate each file
		for (const file of fileArray) {
			const validationError = validateFile(file);
			if (validationError) {
				uploadError = validationError;
				return;
			}
			validFiles.push(file);
		}

		// Check total file count
		const totalFiles = files.length + validFiles.length;
		if (totalFiles > maxFiles) {
			uploadError = `Cannot upload more than ${maxFiles} file${maxFiles > 1 ? 's' : ''}.`;
			return;
		}

		// Add valid files
		if (multiple) {
			files = [...files, ...validFiles];
		} else {
			files = validFiles;
		}

		onFilesChange?.(files);
	};

	const removeFile = (index: number) => {
		files = files.filter((_, i) => i !== index);
		onFilesChange?.(files);
	};

	const handleDragOver = (e: DragEvent) => {
		e.preventDefault();
		if (!disabled) {
			isDragOver = true;
		}
	};

	const handleDragLeave = (e: DragEvent) => {
		e.preventDefault();
		isDragOver = false;
	};

	const handleDrop = (e: DragEvent) => {
		e.preventDefault();
		isDragOver = false;
		if (!disabled) {
			handleFiles(e.dataTransfer?.files || null);
		}
	};

	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	};
</script>

<div class={className}>
	{#if label}
		<Label for={uploadId} class="block text-sm font-medium text-foreground mb-2">
			{label}
			{#if required}
				<span class="text-destructive ml-1">*</span>
			{/if}
		</Label>
	{/if}

	<!-- File Input (Hidden) -->
	<input
		bind:this={fileInput}
		id={uploadId}
		type="file"
		{accept}
		{multiple}
		{disabled}
		class="sr-only"
		onchange={(e) => handleFiles(e.currentTarget.files)}
	/>

	<!-- Drop Zone -->
	<div
		class="border-2 border-dashed rounded-lg p-6 text-center transition-colors {isDragOver 
			? 'border-primary bg-primary/5' 
			: error || uploadError 
				? 'border-destructive bg-destructive/5' 
				: 'border-border hover:border-primary/50'} {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}"
		ondragover={handleDragOver}
		ondragleave={handleDragLeave}
		ondrop={handleDrop}
		onclick={() => !disabled && fileInput.click()}
		role="button"
		tabindex={disabled ? -1 : 0}
		onkeydown={(e) => {
			if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
				e.preventDefault();
				fileInput.click();
			}
		}}
	>
		<Upload class="w-8 h-8 text-muted-foreground mx-auto mb-2" />
		<p class="text-sm font-medium text-foreground mb-1">
			{isDragOver ? 'Drop files here' : 'Click to upload or drag and drop'}
		</p>
		<p class="text-xs text-muted-foreground">
			{accept === '*/*' ? 'Any file type' : accept} • Max {maxSize}MB
			{multiple ? ` • Up to ${maxFiles} files` : ''}
		</p>
	</div>

	<!-- File List -->
	{#if files.length > 0}
		<div class="mt-4 space-y-2">
			{#each files as file, index}
				<div class="flex items-center justify-between p-2 bg-muted rounded-lg">
					<div class="flex items-center gap-2 flex-1 min-w-0">
						<File class="w-4 h-4 text-muted-foreground flex-shrink-0" />
						<div class="min-w-0 flex-1">
							<p class="text-sm font-medium text-foreground truncate">
								{file.name}
							</p>
							<p class="text-xs text-muted-foreground">
								{formatFileSize(file.size)}
							</p>
						</div>
					</div>
					<Button
						variant="ghost"
						size="icon"
						onclick={() => removeFile(index)}
						class="h-6 w-6 text-muted-foreground hover:text-destructive"
						disabled={disabled}
					>
						<X class="h-3 w-3" />
						<span class="sr-only">Remove file</span>
					</Button>
				</div>
			{/each}
		</div>
	{/if}

	<!-- Error Messages -->
	{#if uploadError}
		<div class="flex items-center gap-2 mt-2 text-sm text-destructive">
			<AlertCircle class="w-4 h-4" />
			{uploadError}
		</div>
	{/if}

	{#if error}
		<p class="text-sm text-destructive mt-2">
			{error}
		</p>
	{/if}

	{#if hint && !error && !uploadError}
		<p class="text-sm text-muted-foreground mt-2">
			{hint}
		</p>
	{/if}
</div>
