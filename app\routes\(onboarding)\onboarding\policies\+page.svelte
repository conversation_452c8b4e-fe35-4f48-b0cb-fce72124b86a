<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { FileText, Clock, Check, Eye } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);

	// Policy data
	const policies = [
		{
			id: 'code-of-conduct',
			title: 'Code of Conduct',
			description: 'Professional behavior and ethical standards',
			estimatedTime: '5 min',
			required: true,
			acknowledged: false,
			readTime: 0,
			minReadTime: 30 // seconds
		},
		{
			id: 'data-privacy',
			title: 'Data Privacy Policy',
			description: 'How we handle and protect personal information',
			estimatedTime: '3 min',
			required: true,
			acknowledged: false,
			readTime: 0,
			minReadTime: 20
		},
		{
			id: 'health-safety',
			title: 'Health & Safety Guidelines',
			description: 'Workplace safety procedures and protocols',
			estimatedTime: '4 min',
			required: true,
			acknowledged: false,
			readTime: 0,
			minReadTime: 25
		},
		{
			id: 'leave-policy',
			title: 'Leave Policy',
			description: 'Annual leave, sick leave, and time-off procedures',
			estimatedTime: '3 min',
			required: false,
			acknowledged: false,
			readTime: 0,
			minReadTime: 15
		}
	];

	let selectedPolicy: typeof policies[0] | null = null;
	let readingStartTime = 0;
	let readingTimer: number | null = null;

	const openPolicy = (policy: typeof policies[0]) => {
		selectedPolicy = policy;
		readingStartTime = Date.now();
		
		// Start reading timer
		readingTimer = setInterval(() => {
			if (selectedPolicy) {
				selectedPolicy.readTime = Math.floor((Date.now() - readingStartTime) / 1000);
			}
		}, 1000);
	};

	const closePolicy = () => {
		if (readingTimer) {
			clearInterval(readingTimer);
			readingTimer = null;
		}
		selectedPolicy = null;
	};

	const acknowledgePolicy = (policy: typeof policies[0]) => {
		if (policy.readTime < policy.minReadTime) {
			uiStore.showError(
				'Please Read Completely', 
				`Please spend at least ${policy.minReadTime} seconds reading this policy before acknowledging.`
			);
			return;
		}

		policy.acknowledged = true;
		closePolicy();
		
		uiStore.showSuccess('Policy Acknowledged', `${policy.title} has been acknowledged.`);
		
		// Save acknowledgment
		const acknowledgments = store.data.policies?.acknowledgments || {};
		acknowledgments[policy.id] = {
			acknowledgedAt: new Date().toISOString(),
			readTime: policy.readTime
		};
		
		onboardingStore.updateData('policies', {
			acknowledgments
		});
	};

	const allRequiredPoliciesAcknowledged = $derived(
		policies.filter(p => p.required).every(p => p.acknowledged)
	);

	const handleNext = () => {
		if (!allRequiredPoliciesAcknowledged) {
			uiStore.showError('Policies Required', 'Please acknowledge all required policies before continuing.');
			return;
		}

		// Mark step as completed and move to next
		onboardingStore.completeStep('policies');
		onboardingStore.setCurrentStep('training');
		goto('/onboarding/training');
	};

	const handleBack = () => {
		onboardingStore.setCurrentStep('contracts');
		goto('/onboarding/contracts');
	};
</script>

<svelte:head>
	<title>Company Policies - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Company Policies"
		subtitle="Please review and acknowledge our company policies. These guidelines ensure a safe, respectful, and productive work environment for everyone."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		nextButtonDisabled={!allRequiredPoliciesAcknowledged}
		onBack={handleBack}
		onNext={handleNext}
	>
		{#if !selectedPolicy}
			<!-- Policy List -->
			<div class="space-y-4">
				{#each policies as policy}
					<div class="border border-[#EDE0CF] rounded-xl p-4 hover:border-[#C49A6C] transition-colors">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<div class="flex items-center gap-2 mb-2">
									<h3 class="font-semibold text-[#1C1C1C]">{policy.title}</h3>
									{#if policy.required}
										<span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">Required</span>
									{/if}
									{#if policy.acknowledged}
										<Check class="w-5 h-5 text-green-500" />
									{/if}
								</div>
								<p class="text-sm text-[#8A6A52] mb-3">{policy.description}</p>
								<div class="flex items-center gap-4 text-xs text-[#8A6A52]">
									<div class="flex items-center gap-1">
										<Clock class="w-3 h-3" />
										{policy.estimatedTime}
									</div>
									{#if policy.acknowledged}
										<span class="text-green-600 font-medium">✓ Acknowledged</span>
									{/if}
								</div>
							</div>
							<div class="flex flex-col gap-2">
								<Button
									variant="outline"
									size="sm"
									onclick={() => openPolicy(policy)}
								>
									<Eye class="w-4 h-4 mr-2" />
									Read
								</Button>
								{#if policy.acknowledged}
									<Button
										variant="ghost"
										size="sm"
										class="text-green-600"
										disabled
									>
										<Check class="w-4 h-4 mr-2" />
										Done
									</Button>
								{/if}
							</div>
						</div>
					</div>
				{/each}

				<!-- Progress Summary -->
				<div class="mt-6 p-4 bg-[#FCF8F2] rounded-xl border border-[#EDE0CF]">
					<h4 class="font-medium text-[#1C1C1C] mb-2">Progress Summary</h4>
					<div class="text-sm text-[#8A6A52]">
						<p>Required policies acknowledged: {policies.filter(p => p.required && p.acknowledged).length} / {policies.filter(p => p.required).length}</p>
						<p>Optional policies acknowledged: {policies.filter(p => !p.required && p.acknowledged).length} / {policies.filter(p => !p.required).length}</p>
					</div>
				</div>
			</div>
		{:else}
			<!-- Policy Reader -->
			<div class="space-y-4">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="text-lg font-semibold text-[#1C1C1C]">{selectedPolicy.title}</h3>
						<p class="text-sm text-[#8A6A52]">{selectedPolicy.description}</p>
					</div>
					<Button variant="ghost" onclick={closePolicy}>
						Close
					</Button>
				</div>

				<!-- Reading Timer -->
				<div class="bg-blue-50 rounded-lg p-3 border border-blue-200">
					<div class="flex items-center justify-between text-sm">
						<span class="text-blue-700">Reading time: {selectedPolicy.readTime}s</span>
						<span class="text-blue-600">
							Minimum required: {selectedPolicy.minReadTime}s
						</span>
					</div>
					<div class="w-full bg-blue-200 rounded-full h-2 mt-2">
						<div 
							class="bg-blue-500 h-2 rounded-full transition-all duration-1000"
							style="width: {Math.min(100, (selectedPolicy.readTime / selectedPolicy.minReadTime) * 100)}%"
						></div>
					</div>
				</div>

				<!-- Policy Content (Mock) -->
				<div class="bg-white border border-[#EDE0CF] rounded-lg p-6 max-h-96 overflow-y-auto">
					<div class="prose prose-sm max-w-none">
						{#if selectedPolicy.id === 'code-of-conduct'}
							<h4>Code of Conduct</h4>
							<p>At Rainbow Tourism Group, we are committed to maintaining the highest standards of professional conduct...</p>
							<h5>Professional Behavior</h5>
							<p>All employees are expected to conduct themselves professionally at all times...</p>
							<h5>Respect and Dignity</h5>
							<p>We foster an inclusive environment where all individuals are treated with respect...</p>
							<h5>Confidentiality</h5>
							<p>Employees must maintain confidentiality of sensitive company and guest information...</p>
						{:else if selectedPolicy.id === 'data-privacy'}
							<h4>Data Privacy Policy</h4>
							<p>This policy outlines how Rainbow Tourism Group collects, uses, and protects personal data...</p>
							<h5>Data Collection</h5>
							<p>We collect personal information necessary for employment and business operations...</p>
							<h5>Data Protection</h5>
							<p>All personal data is stored securely and accessed only by authorized personnel...</p>
						{:else if selectedPolicy.id === 'health-safety'}
							<h4>Health & Safety Guidelines</h4>
							<p>The safety and well-being of our employees and guests is our top priority...</p>
							<h5>Workplace Safety</h5>
							<p>All employees must follow established safety procedures and report hazards immediately...</p>
							<h5>Emergency Procedures</h5>
							<p>Familiarize yourself with emergency exits and evacuation procedures...</p>
						{:else}
							<h4>Leave Policy</h4>
							<p>This policy outlines the various types of leave available to employees...</p>
							<h5>Annual Leave</h5>
							<p>Employees are entitled to annual leave based on their length of service...</p>
							<h5>Sick Leave</h5>
							<p>Sick leave is available for medical appointments and illness...</p>
						{/if}
					</div>
				</div>

				<!-- Acknowledge Button -->
				<div class="flex justify-end">
					<Button
						onclick={() => acknowledgePolicy(selectedPolicy)}
						disabled={selectedPolicy.readTime < selectedPolicy.minReadTime}
						class="onboarding-button-primary"
					>
						{#if selectedPolicy.readTime < selectedPolicy.minReadTime}
							<Clock class="w-4 h-4 mr-2" />
							Please continue reading...
						{:else}
							<Check class="w-4 h-4 mr-2" />
							Acknowledge Policy
						{/if}
					</Button>
				</div>
			</div>
		{/if}
	</OnboardingCard>
</div>
