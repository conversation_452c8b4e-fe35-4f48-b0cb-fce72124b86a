<script lang="ts">
  import { cn } from '$lib/utils';
  import { getContext } from 'svelte';
  
  interface Props {
    value: string;
    class?: string;
    children?: import('svelte').Snippet;
  }
  
  let { value, class: className, children, ...restProps }: Props = $props();
  
  const tabs = getContext<{
    value: string;
    setValue: (value: string) => void;
  }>('tabs');
  
  const isActive = $derived(tabs?.value === value);
  
  function handleClick() {
    tabs?.setValue(value);
  }
</script>

<button
  class={cn(
    "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
    isActive ? "bg-background text-foreground shadow-sm" : "text-muted-foreground hover:text-foreground",
    className
  )}
  role="tab"
  aria-selected={isActive}
  onclick={handleClick}
  {...restProps}
>
  {#if children}
    {@render children()}
  {/if}
</button>
