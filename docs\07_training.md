# 07 — Training & HCD (Frontend-only)

Pages:
- `/training` — catalog
- `/training/[id]` — training event detail & enroll button
- `/training/my-enrollments` — list of enrollments

Components:
- `TrainingCard.svelte`, `TrainingCalendar.svelte`, `EnrollmentModal.svelte`
- `AttendanceTracker.svelte` — mock QR check-in simulation

Flows:
1. L&D creates training (mock) -> employees enroll -> trainer marks attendance -> certificate is generated client-side (PDF stub).

Acceptance:
- Enrollment counts increment,
- Attendance toggles and triggers certificate availability.
