import { test, expect } from '@playwright/test';

test.describe('Onboarding Workflow', () => {
	test.beforeEach(async ({ page }) => {
		// Navigate to onboarding with testing parameter to bypass auth
		await page.goto('/onboarding?testing=true');
	});

	test('should display welcome page with correct branding', async ({ page }) => {
		// Check for RTG branding
		await expect(page.locator('text=Rainbow Tourism Group')).toBeVisible();
		await expect(page.locator('text=Employee Onboarding')).toBeVisible();
		
		// Check for welcome message
		await expect(page.locator('text=Congratulations on joining our team!')).toBeVisible();
		
		// Check for Get Started button
		await expect(page.locator('button:has-text("Get Started")')).toBeVisible();
	});

	test('should navigate through onboarding steps', async ({ page }) => {
		// Start onboarding
		await page.click('button:has-text("Get Started")');
		
		// Should be on personal details step
		await expect(page.locator('text=Personal Details')).toBeVisible();
		await expect(page.locator('input[placeholder*="first name"]')).toBeVisible();
		
		// Fill out personal details form
		await page.fill('input[id="firstName"]', 'John');
		await page.fill('input[id="lastName"]', 'Doe');
		await page.fill('input[id="email"]', '<EMAIL>');
		await page.fill('input[id="phone"]', '+263 77 123 4567');
		await page.fill('input[id="dateOfBirth"]', '1990-01-01');
		
		// Fill address
		await page.fill('input[id="street"]', '123 Main Street');
		await page.fill('input[id="city"]', 'Harare');
		await page.fill('input[id="postalCode"]', '00000');
		
		// Fill emergency contact
		await page.fill('input[id="emergencyName"]', 'Jane Doe');
		await page.fill('input[id="emergencyPhone"]', '+263 77 987 6543');
		
		// Continue to next step
		await page.click('button:has-text("Continue")');
		
		// Should be on employment details step
		await expect(page.locator('text=Employment Details')).toBeVisible();
	});

	test('should show validation errors for required fields', async ({ page }) => {
		// Start onboarding
		await page.click('button:has-text("Get Started")');
		
		// Try to continue without filling required fields
		await page.click('button:has-text("Continue")');
		
		// Should show validation errors
		await expect(page.locator('text=First name is required')).toBeVisible();
		await expect(page.locator('text=Last name is required')).toBeVisible();
		await expect(page.locator('text=Email is required')).toBeVisible();
	});

	test('should show progress in stepper', async ({ page }) => {
		// Check initial progress
		await expect(page.locator('text=Progress')).toBeVisible();
		await expect(page.locator('text=0/7')).toBeVisible();
		
		// Check that first step is active
		await expect(page.locator('[aria-current="step"]')).toContainText('Personal Details');
	});

	test('should save form data automatically', async ({ page }) => {
		// Start onboarding
		await page.click('button:has-text("Get Started")');
		
		// Fill a field and wait for auto-save
		await page.fill('input[id="firstName"]', 'John');
		
		// Wait for auto-save indicator (should appear after 1 second)
		await page.waitForTimeout(1500);
		await expect(page.locator('text*="Last saved:"')).toBeVisible();
	});

	test('should complete full onboarding workflow', async ({ page }) => {
		// This is a comprehensive test that goes through all steps
		// Start onboarding
		await page.click('button:has-text("Get Started")');
		
		// Step 1: Personal Details
		await page.fill('input[id="firstName"]', 'John');
		await page.fill('input[id="lastName"]', 'Doe');
		await page.fill('input[id="email"]', '<EMAIL>');
		await page.fill('input[id="phone"]', '+263 77 123 4567');
		await page.fill('input[id="dateOfBirth"]', '1990-01-01');
		await page.fill('input[id="street"]', '123 Main Street');
		await page.fill('input[id="city"]', 'Harare');
		await page.fill('input[id="postalCode"]', '00000');
		await page.fill('input[id="emergencyName"]', 'Jane Doe');
		await page.fill('input[id="emergencyPhone"]', '+263 77 987 6543');
		await page.click('button:has-text("Continue")');
		
		// Step 2: Employment Details
		await expect(page.locator('text=Employment Details')).toBeVisible();
		await page.fill('input[id="jobTitle"]', 'Marketing Specialist');
		await page.fill('input[id="startDate"]', '2024-01-15');
		await page.click('button:has-text("Continue")');
		
		// Step 3: Tax Details
		await expect(page.locator('text=Tax Details')).toBeVisible();
		await page.fill('input[id="taxId"]', 'TAX123456789');
		await page.click('button:has-text("Continue")');
		
		// Step 4: Bank Details
		await expect(page.locator('text=Bank Details')).toBeVisible();
		await page.fill('input[id="accountName"]', 'John Doe');
		await page.fill('input[id="accountNumber"]', '**********');
		await page.click('button:has-text("Continue")');
		
		// Step 5: Documents (skip file upload for test)
		await expect(page.locator('text=Upload Documents')).toBeVisible();
		// In a real test, we would mock file uploads
		// For now, just continue (the button should be disabled until files are uploaded)
		
		// Step 6: Agreements
		// This step would require checking checkboxes and signing
		
		// Step 7: Summary
		// This would show all the entered information
		
		// Note: This test is simplified - in a real implementation,
		// we would need to handle file uploads, form selections, and mock API responses
	});

	test('should handle responsive design', async ({ page }) => {
		// Test mobile viewport
		await page.setViewportSize({ width: 375, height: 667 });
		
		// Check that stepper adapts to mobile
		await expect(page.locator('.onboarding-stepper')).toBeVisible();
		
		// Check that form fields stack properly
		await page.click('button:has-text("Get Started")');
		await expect(page.locator('input[id="firstName"]')).toBeVisible();
	});

	test('should maintain accessibility standards', async ({ page }) => {
		// Start onboarding
		await page.click('button:has-text("Get Started")');
		
		// Check for proper ARIA labels
		await expect(page.locator('nav[aria-label="Onboarding progress"]')).toBeVisible();
		
		// Check for required field indicators
		await expect(page.locator('span:has-text("*")')).toBeVisible();
		
		// Check for form validation ARIA attributes
		await page.click('button:has-text("Continue")');
		await expect(page.locator('[aria-invalid="true"]')).toBeVisible();
	});
});
