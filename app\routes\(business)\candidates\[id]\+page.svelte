<script lang="ts">
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import Modal from '$lib/components/ui/Modal.svelte';
	import { 
		ArrowLeft, 
		Mail, 
		Phone, 
		MapPin, 
		Calendar, 
		FileText, 
		Download,
		MessageSquare,
		CheckCircle,
		XCircle,
		Clock,
		User,
		Briefcase,
		GraduationCap,
		Star
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);
	const candidateId = page.params.id;

	// Mock candidate data - in real app, this would be fetched from API
	const candidate = {
		id: candidateId,
		name: '<PERSON>',
		email: '<EMAIL>',
		phone: '+263 77 555 0123',
		location: 'Harare, Zimbabwe',
		position_applied: 'Senior Software Developer',
		application_date: '2025-08-15',
		status: 'interview_scheduled',
		resume_url: '/documents/alice_johnson_resume.pdf',
		cover_letter_url: '/documents/alice_johnson_cover_letter.pdf',
		experience_years: 5,
		current_company: 'TechCorp Zimbabwe',
		current_position: 'Software Developer',
		education: 'BSc Computer Science - University of Zimbabwe',
		skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python', 'AWS'],
		salary_expectation: '$65,000 - $75,000',
		availability: 'Available in 2 weeks',
		linkedin_url: 'https://linkedin.com/in/alicejohnson',
		portfolio_url: 'https://alicejohnson.dev',
		notes: 'Strong technical background with excellent communication skills. Previous experience in fintech.',
		rating: 4.5
	};

	// Interview history
	const interviews = [
		{
			id: 1,
			type: 'Phone Screening',
			date: '2025-08-18',
			interviewer: 'Sarah Wilson',
			status: 'completed',
			feedback: 'Excellent technical knowledge and communication skills. Recommended for technical interview.',
			rating: 5
		},
		{
			id: 2,
			type: 'Technical Interview',
			date: '2025-08-22',
			interviewer: 'Mike Johnson',
			status: 'scheduled',
			feedback: '',
			rating: null
		}
	];

	// Application timeline
	const timeline = [
		{
			date: '2025-08-15',
			event: 'Application Submitted',
			description: 'Candidate applied for Senior Software Developer position',
			status: 'completed'
		},
		{
			date: '2025-08-16',
			event: 'Application Reviewed',
			description: 'Initial screening completed by HR team',
			status: 'completed'
		},
		{
			date: '2025-08-18',
			event: 'Phone Screening',
			description: 'Phone interview with Sarah Wilson',
			status: 'completed'
		},
		{
			date: '2025-08-22',
			event: 'Technical Interview',
			description: 'Scheduled technical interview with Mike Johnson',
			status: 'scheduled'
		}
	];

	let showStatusModal = $state(false);
	let newStatus = $state(candidate.status);
	let statusNotes = $state('');
	let isUpdatingStatus = $state(false);

	const statusOptions = [
		{ value: 'applied', label: 'Applied', color: 'bg-blue-100 text-blue-800' },
		{ value: 'screening', label: 'Screening', color: 'bg-yellow-100 text-yellow-800' },
		{ value: 'interview_scheduled', label: 'Interview Scheduled', color: 'bg-purple-100 text-purple-800' },
		{ value: 'interviewed', label: 'Interviewed', color: 'bg-indigo-100 text-indigo-800' },
		{ value: 'offer_extended', label: 'Offer Extended', color: 'bg-green-100 text-green-800' },
		{ value: 'hired', label: 'Hired', color: 'bg-green-500 text-white' },
		{ value: 'rejected', label: 'Rejected', color: 'bg-red-100 text-red-800' },
		{ value: 'withdrawn', label: 'Withdrawn', color: 'bg-gray-100 text-gray-800' }
	];

	const getStatusInfo = (status: string) => {
		return statusOptions.find(s => s.value === status) || statusOptions[0];
	};

	const getInterviewStatusIcon = (status: string) => {
		switch (status) {
			case 'completed': return CheckCircle;
			case 'scheduled': return Clock;
			case 'cancelled': return XCircle;
			default: return Clock;
		}
	};

	const getInterviewStatusColor = (status: string) => {
		switch (status) {
			case 'completed': return 'text-green-600';
			case 'scheduled': return 'text-blue-600';
			case 'cancelled': return 'text-red-600';
			default: return 'text-gray-600';
		}
	};

	const handleBack = () => {
		goto('/candidates');
	};

	const handleUpdateStatus = async () => {
		isUpdatingStatus = true;
		
		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 1500));
			
			console.log('Updating candidate status:', { candidateId, newStatus, statusNotes });
			
			notificationStore.success('Status Updated', 'Candidate status has been successfully updated');
			showStatusModal = false;
			statusNotes = '';
		} catch (error) {
			console.error('Error updating status:', error);
			notificationStore.error('Update Failed', 'Failed to update candidate status');
		} finally {
			isUpdatingStatus = false;
		}
	};

	const handleDownloadResume = () => {
		// In real app, this would download the actual file
		notificationStore.info('Download Started', 'Resume download has started');
	};

	const handleSendMessage = () => {
		// In real app, this would open email client or messaging interface
		notificationStore.info('Email Client', 'Opening email client to send message');
	};

	const renderStars = (rating: number) => {
		const stars = [];
		for (let i = 1; i <= 5; i++) {
			stars.push(
				i <= rating ? 'text-yellow-400' : 'text-gray-300'
			);
		}
		return stars;
	};

	const canManage = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin' || user?.user_role === 'manager';
</script>

<svelte:head>
	<title>{candidate.name} - Candidate Profile - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleBack}>
				<ArrowLeft class="w-4 h-4" />
				Back to Candidates
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">{candidate.name}</h1>
				<p class="text-muted-foreground">{candidate.position_applied}</p>
			</div>
		</div>
		
		{#if canManage}
			<div class="flex gap-2">
				<Button variant="outline" onclick={handleSendMessage}>
					<MessageSquare class="w-4 h-4" />
					Send Message
				</Button>
				<Button variant="default" onclick={() => showStatusModal = true}>
					Update Status
				</Button>
			</div>
		{/if}
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Candidate Profile -->
		<div class="lg:col-span-1">
			<Card class="p-6">
				<div class="text-center mb-6">
					<div class="w-20 h-20 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-2xl font-bold text-primary-foreground">
							{candidate.name.split(' ').map(n => n.charAt(0)).join('')}
						</span>
					</div>
					<h2 class="text-xl font-semibold text-foreground mb-1">{candidate.name}</h2>
					<p class="text-muted-foreground mb-3">{candidate.current_position}</p>
					<Badge class={getStatusInfo(candidate.status).color}>
						{getStatusInfo(candidate.status).label}
					</Badge>
				</div>

				<div class="space-y-4">
					<div class="flex items-center gap-3">
						<Mail class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{candidate.email}</span>
					</div>
					<div class="flex items-center gap-3">
						<Phone class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{candidate.phone}</span>
					</div>
					<div class="flex items-center gap-3">
						<MapPin class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{candidate.location}</span>
					</div>
					<div class="flex items-center gap-3">
						<Calendar class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">
							Applied: {new Date(candidate.application_date).toLocaleDateString()}
						</span>
					</div>
					<div class="flex items-center gap-3">
						<Briefcase class="w-4 h-4 text-muted-foreground" />
						<span class="text-sm text-foreground">{candidate.experience_years} years experience</span>
					</div>
				</div>

				<div class="mt-6 pt-6 border-t border-border">
					<div class="flex items-center justify-between mb-2">
						<span class="text-sm font-medium text-foreground">Overall Rating</span>
						<div class="flex items-center gap-1">
							{#each renderStars(candidate.rating) as starColor}
								<Star class="w-4 h-4 {starColor} fill-current" />
							{/each}
							<span class="text-sm text-muted-foreground ml-1">({candidate.rating}/5)</span>
						</div>
					</div>
				</div>

				<div class="mt-4 space-y-2">
					<Button variant="outline" class="w-full" onclick={handleDownloadResume}>
						<Download class="w-4 h-4" />
						Download Resume
					</Button>
					{#if candidate.cover_letter_url}
						<Button variant="outline" class="w-full">
							<FileText class="w-4 h-4" />
							View Cover Letter
						</Button>
					{/if}
				</div>
			</Card>
		</div>

		<!-- Candidate Details -->
		<div class="lg:col-span-2 space-y-6">
			<!-- Professional Information -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Professional Information</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Current Company</div>
						<p class="text-foreground">{candidate.current_company}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Current Position</div>
						<p class="text-foreground">{candidate.current_position}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Education</div>
						<p class="text-foreground">{candidate.education}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Salary Expectation</div>
						<p class="text-foreground">{candidate.salary_expectation}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Availability</div>
						<p class="text-foreground">{candidate.availability}</p>
					</div>
					<div>
						<div class="text-sm font-medium text-muted-foreground mb-1">Portfolio</div>
						<a href={candidate.portfolio_url} target="_blank" class="text-primary hover:underline">
							{candidate.portfolio_url}
						</a>
					</div>
				</div>
			</Card>

			<!-- Skills -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Skills & Expertise</h3>
				<div class="flex flex-wrap gap-2">
					{#each candidate.skills as skill}
						<Badge variant="secondary" class="bg-primary/10 text-primary">
							{skill}
						</Badge>
					{/each}
				</div>
			</Card>

			<!-- Interview History -->
			<Card class="p-6">
				<h3 class="text-lg font-semibold text-foreground mb-4">Interview History</h3>
				<div class="space-y-4">
					{#each interviews as interview}
						{@const StatusIcon = getInterviewStatusIcon(interview.status)}
						<div class="flex items-start gap-3 p-3 border border-border rounded-lg">
							<StatusIcon class="w-5 h-5 {getInterviewStatusColor(interview.status)} mt-0.5" />
							<div class="flex-1">
								<div class="flex items-center justify-between mb-1">
									<h4 class="text-sm font-medium text-foreground">{interview.type}</h4>
									<span class="text-xs text-muted-foreground">
										{new Date(interview.date).toLocaleDateString()}
									</span>
								</div>
								<p class="text-sm text-muted-foreground mb-1">
									Interviewer: {interview.interviewer}
								</p>
								{#if interview.feedback}
									<p class="text-sm text-foreground mb-2">{interview.feedback}</p>
								{/if}
								{#if interview.rating}
									<div class="flex items-center gap-1">
										{#each renderStars(interview.rating) as starColor}
											<Star class="w-3 h-3 {starColor} fill-current" />
										{/each}
										<span class="text-xs text-muted-foreground ml-1">({interview.rating}/5)</span>
									</div>
								{/if}
							</div>
						</div>
					{/each}
				</div>
			</Card>

			<!-- Notes -->
			{#if candidate.notes}
				<Card class="p-6">
					<h3 class="text-lg font-semibold text-foreground mb-4">Notes</h3>
					<p class="text-foreground">{candidate.notes}</p>
				</Card>
			{/if}
		</div>
	</div>
</div>

<!-- Status Update Modal -->
<Modal bind:open={showStatusModal} title="Update Candidate Status" size="md">
	{#snippet children()}
		<div class="space-y-4">
			<div>
				<label class="text-sm font-medium text-foreground mb-2 block">New Status</label>
				<select 
					bind:value={newStatus}
					class="w-full p-2 border border-border rounded-md bg-background text-foreground"
					disabled={isUpdatingStatus}
				>
					{#each statusOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>
			<div>
				<label class="text-sm font-medium text-foreground mb-2 block">Notes (Optional)</label>
				<textarea
					bind:value={statusNotes}
					class="w-full p-2 border border-border rounded-md bg-background text-foreground"
					rows="3"
					placeholder="Add any notes about this status change..."
					disabled={isUpdatingStatus}
				></textarea>
			</div>
		</div>
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showStatusModal = false} disabled={isUpdatingStatus}>
			Cancel
		</Button>
		<Button variant="default" onclick={handleUpdateStatus} disabled={isUpdatingStatus}>
			{isUpdatingStatus ? 'Updating...' : 'Update Status'}
		</Button>
	{/snippet}
</Modal>
