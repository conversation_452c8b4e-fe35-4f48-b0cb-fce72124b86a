<script lang="ts">
	import { goto } from '$app/navigation';
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import Select from '$lib/components/ui/Select.svelte';
	import { ArrowLeft, Save, X, Plus, Briefcase } from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Form data
	let formData = $state({
		title: '',
		department: '',
		location: '',
		employment_type: 'Full-time',
		experience_level: 'Mid-level',
		salary_min: '',
		salary_max: '',
		currency: 'USD',
		description: '',
		requirements: '',
		responsibilities: '',
		benefits: '',
		application_deadline: '',
		hiring_manager: '',
		priority: 'Medium',
		positions_available: '1'
	});

	// Form validation errors
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Department options
	const departmentOptions = [
		{ value: 'Information Technology', label: 'Information Technology' },
		{ value: 'Human Resources', label: 'Human Resources' },
		{ value: 'Finance', label: 'Finance' },
		{ value: 'Marketing', label: 'Marketing' },
		{ value: 'Operations', label: 'Operations' },
		{ value: 'Sales', label: 'Sales' },
		{ value: 'Customer Service', label: 'Customer Service' },
		{ value: 'Legal', label: 'Legal' }
	];

	// Employment type options
	const employmentTypeOptions = [
		{ value: 'Full-time', label: 'Full-time' },
		{ value: 'Part-time', label: 'Part-time' },
		{ value: 'Contract', label: 'Contract' },
		{ value: 'Temporary', label: 'Temporary' },
		{ value: 'Internship', label: 'Internship' }
	];

	// Experience level options
	const experienceLevelOptions = [
		{ value: 'Entry-level', label: 'Entry-level' },
		{ value: 'Mid-level', label: 'Mid-level' },
		{ value: 'Senior-level', label: 'Senior-level' },
		{ value: 'Executive', label: 'Executive' }
	];

	// Priority options
	const priorityOptions = [
		{ value: 'Low', label: 'Low' },
		{ value: 'Medium', label: 'Medium' },
		{ value: 'High', label: 'High' },
		{ value: 'Urgent', label: 'Urgent' }
	];

	// Currency options
	const currencyOptions = [
		{ value: 'USD', label: 'USD ($)' },
		{ value: 'ZWL', label: 'ZWL (Z$)' },
		{ value: 'EUR', label: 'EUR (€)' },
		{ value: 'GBP', label: 'GBP (£)' }
	];

	// Location options
	const locationOptions = [
		{ value: 'Harare, Zimbabwe', label: 'Harare, Zimbabwe' },
		{ value: 'Bulawayo, Zimbabwe', label: 'Bulawayo, Zimbabwe' },
		{ value: 'Victoria Falls, Zimbabwe', label: 'Victoria Falls, Zimbabwe' },
		{ value: 'Remote', label: 'Remote' },
		{ value: 'Hybrid', label: 'Hybrid' }
	];

	// Hiring manager options (mock data)
	const hiringManagerOptions = [
		{ value: 'Sarah Wilson', label: 'Sarah Wilson' },
		{ value: 'Mike Johnson', label: 'Mike Johnson' },
		{ value: 'Emily Brown', label: 'Emily Brown' },
		{ value: 'David Smith', label: 'David Smith' }
	];

	// Set default application deadline (30 days from now)
	const defaultDeadline = new Date();
	defaultDeadline.setDate(defaultDeadline.getDate() + 30);
	formData.application_deadline = defaultDeadline.toISOString().split('T')[0];

	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		// Required field validation
		if (!formData.title.trim()) {
			newErrors.title = 'Job title is required';
		}
		if (!formData.department) {
			newErrors.department = 'Department is required';
		}
		if (!formData.location) {
			newErrors.location = 'Location is required';
		}
		if (!formData.description.trim()) {
			newErrors.description = 'Job description is required';
		}
		if (!formData.requirements.trim()) {
			newErrors.requirements = 'Requirements are required';
		}
		if (!formData.responsibilities.trim()) {
			newErrors.responsibilities = 'Responsibilities are required';
		}
		if (!formData.application_deadline) {
			newErrors.application_deadline = 'Application deadline is required';
		}

		// Salary validation
		if (formData.salary_min && formData.salary_max) {
			const minSalary = parseFloat(formData.salary_min);
			const maxSalary = parseFloat(formData.salary_max);
			if (minSalary >= maxSalary) {
				newErrors.salary_max = 'Maximum salary must be greater than minimum salary';
			}
		}

		// Positions validation
		if (formData.positions_available && parseInt(formData.positions_available) < 1) {
			newErrors.positions_available = 'Number of positions must be at least 1';
		}

		// Deadline validation
		if (formData.application_deadline) {
			const deadline = new Date(formData.application_deadline);
			const today = new Date();
			today.setHours(0, 0, 0, 0);
			if (deadline < today) {
				newErrors.application_deadline = 'Application deadline must be in the future';
			}
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async () => {
		if (!validateForm()) {
			notificationStore.error('Validation Error', 'Please fix the errors below');
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			console.log('Creating job requisition:', formData);
			
			notificationStore.success('Job Requisition Created', 'New job posting has been successfully created');
			
			// Redirect to recruitment page
			goto('/recruitment');
		} catch (error) {
			console.error('Error creating job requisition:', error);
			notificationStore.error('Creation Failed', 'Failed to create job requisition');
		} finally {
			isSubmitting = false;
		}
	};

	const handleCancel = () => {
		goto('/recruitment');
	};

	const canCreate = user?.user_role === 'hr_admin' || user?.user_role === 'super_admin' || user?.user_role === 'manager';

	// Redirect if user doesn't have permission
	if (!canCreate) {
		goto('/recruitment');
	}
</script>

<svelte:head>
	<title>Create Job Requisition - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-4">
			<Button variant="outline" size="sm" onclick={handleCancel}>
				<ArrowLeft class="w-4 h-4" />
				Back to Recruitment
			</Button>
			<div>
				<h1 class="text-2xl font-bold text-foreground">Create Job Requisition</h1>
				<p class="text-muted-foreground">Post a new job opening</p>
			</div>
		</div>
		
		<div class="flex gap-2">
			<Button variant="outline" onclick={handleCancel} disabled={isSubmitting}>
				<X class="w-4 h-4" />
				Cancel
			</Button>
			<Button variant="default" onclick={handleSubmit} disabled={isSubmitting}>
				<Briefcase class="w-4 h-4" />
				{isSubmitting ? 'Creating...' : 'Create Job Posting'}
			</Button>
		</div>
	</div>

	<!-- Creation Form -->
	<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
		<!-- Basic Information -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold text-foreground mb-4">Basic Information</h3>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div class="md:col-span-2">
					<Label for="title">Job Title *</Label>
					<Input
						id="title"
						bind:value={formData.title}
						error={errors.title}
						disabled={isSubmitting}
						required
						placeholder="Senior Software Developer"
					/>
				</div>
				<div>
					<Label for="department">Department *</Label>
					<Select
						options={departmentOptions}
						bind:value={formData.department}
						error={errors.department}
						disabled={isSubmitting}
						required
						placeholder="Select department..."
					/>
				</div>
				<div>
					<Label for="location">Location *</Label>
					<Select
						options={locationOptions}
						bind:value={formData.location}
						error={errors.location}
						disabled={isSubmitting}
						required
						placeholder="Select location..."
					/>
				</div>
				<div>
					<Label for="employment_type">Employment Type</Label>
					<Select
						options={employmentTypeOptions}
						bind:value={formData.employment_type}
						error={errors.employment_type}
						disabled={isSubmitting}
					/>
				</div>
				<div>
					<Label for="experience_level">Experience Level</Label>
					<Select
						options={experienceLevelOptions}
						bind:value={formData.experience_level}
						error={errors.experience_level}
						disabled={isSubmitting}
					/>
				</div>
				<div>
					<Label for="positions_available">Positions Available</Label>
					<Input
						id="positions_available"
						type="number"
						bind:value={formData.positions_available}
						error={errors.positions_available}
						disabled={isSubmitting}
						min="1"
					/>
				</div>
				<div>
					<Label for="priority">Priority</Label>
					<Select
						options={priorityOptions}
						bind:value={formData.priority}
						error={errors.priority}
						disabled={isSubmitting}
					/>
				</div>
			</div>
		</Card>

		<!-- Compensation -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold text-foreground mb-4">Compensation</h3>
			<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
				<div>
					<Label for="currency">Currency</Label>
					<Select
						options={currencyOptions}
						bind:value={formData.currency}
						error={errors.currency}
						disabled={isSubmitting}
					/>
				</div>
				<div>
					<Label for="salary_min">Minimum Salary</Label>
					<Input
						id="salary_min"
						type="number"
						bind:value={formData.salary_min}
						error={errors.salary_min}
						disabled={isSubmitting}
						placeholder="50000"
					/>
				</div>
				<div>
					<Label for="salary_max">Maximum Salary</Label>
					<Input
						id="salary_max"
						type="number"
						bind:value={formData.salary_max}
						error={errors.salary_max}
						disabled={isSubmitting}
						placeholder="80000"
					/>
				</div>
			</div>
		</Card>

		<!-- Job Details -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold text-foreground mb-4">Job Details</h3>
			<div class="space-y-4">
				<div>
					<Label for="description">Job Description *</Label>
					<Textarea
						id="description"
						bind:value={formData.description}
						error={errors.description}
						disabled={isSubmitting}
						required
						rows={4}
						placeholder="Provide a detailed description of the role, including key objectives and what the successful candidate will be doing..."
					/>
				</div>
				<div>
					<Label for="responsibilities">Key Responsibilities *</Label>
					<Textarea
						id="responsibilities"
						bind:value={formData.responsibilities}
						error={errors.responsibilities}
						disabled={isSubmitting}
						required
						rows={4}
						placeholder="• Develop and maintain web applications
• Collaborate with cross-functional teams
• Write clean, maintainable code..."
					/>
				</div>
				<div>
					<Label for="requirements">Requirements *</Label>
					<Textarea
						id="requirements"
						bind:value={formData.requirements}
						error={errors.requirements}
						disabled={isSubmitting}
						required
						rows={4}
						placeholder="• Bachelor's degree in Computer Science or related field
• 3+ years of experience in software development
• Proficiency in JavaScript, TypeScript..."
					/>
				</div>
				<div>
					<Label for="benefits">Benefits & Perks</Label>
					<Textarea
						id="benefits"
						bind:value={formData.benefits}
						error={errors.benefits}
						disabled={isSubmitting}
						rows={3}
						placeholder="• Competitive salary and benefits package
• Health insurance
• Professional development opportunities..."
					/>
				</div>
			</div>
		</Card>

		<!-- Application Details -->
		<Card class="p-6">
			<h3 class="text-lg font-semibold text-foreground mb-4">Application Details</h3>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label for="application_deadline">Application Deadline *</Label>
					<Input
						id="application_deadline"
						type="date"
						bind:value={formData.application_deadline}
						error={errors.application_deadline}
						disabled={isSubmitting}
						required
					/>
				</div>
				<div>
					<Label for="hiring_manager">Hiring Manager</Label>
					<Select
						options={hiringManagerOptions}
						bind:value={formData.hiring_manager}
						error={errors.hiring_manager}
						disabled={isSubmitting}
						placeholder="Select hiring manager..."
					/>
				</div>
			</div>
		</Card>
	</form>
</div>
