<script lang="ts">
	// RTG-themed NotificationList component for toast notifications
	import { fly } from 'svelte/transition';
	import { CheckCircle, AlertCircle, XCircle, Info, X } from '@lucide/svelte';
	import { Button } from './button/index.js';

	// Legacy notification interface for backward compatibility
	export interface Notification {
		id: string;
		type: 'success' | 'error' | 'warning' | 'info';
		title: string;
		message?: string;
		duration?: number; // in milliseconds, 0 for persistent
		action?: {
			label: string;
			onClick: () => void;
		};
	}

	// Re-export the enhanced notification type
	export type { EnhancedNotification } from '$lib/stores/notificationStore';

	interface Props {
		notifications: Notification[];
		position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
		onRemove: (id: string) => void;
	}

	let {
		notifications,
		position = 'top-right',
		onRemove
	}: Props = $props();

	const positionClasses = {
		'top-right': 'top-4 right-4',
		'top-left': 'top-4 left-4',
		'bottom-right': 'bottom-4 right-4',
		'bottom-left': 'bottom-4 left-4',
		'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
		'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
	};

	const getIcon = (type: Notification['type']) => {
		switch (type) {
			case 'success': return CheckCircle;
			case 'error': return XCircle;
			case 'warning': return AlertCircle;
			case 'info': return Info;
		}
	};

	const getTypeClasses = (type: Notification['type']) => {
		switch (type) {
			case 'success':
				return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200';
			case 'error':
				return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200';
			case 'warning':
				return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200';
			case 'info':
				return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200';
		}
	};

	const getIconClasses = (type: Notification['type']) => {
		switch (type) {
			case 'success': return 'text-green-500';
			case 'error': return 'text-red-500';
			case 'warning': return 'text-yellow-500';
			case 'info': return 'text-blue-500';
		}
	};

	// Auto-remove notifications after their duration
	$effect(() => {
		notifications.forEach(notification => {
			if (notification.duration && notification.duration > 0) {
				setTimeout(() => {
					onRemove(notification.id);
				}, notification.duration);
			}
		});
	});
</script>

<!-- Notification Container -->
<div class="fixed {positionClasses[position]} z-50 max-w-sm w-full space-y-2 pointer-events-none">
	{#each notifications as notification (notification.id)}
		{@const IconComponent = getIcon(notification.type)}
		<div
			class="pointer-events-auto border rounded-lg shadow-lg p-4 {getTypeClasses(notification.type)}"
			transition:fly={{
				x: position.includes('right') ? 300 : position.includes('left') ? -300 : 0,
				y: position.includes('top') ? -100 : position.includes('bottom') ? 100 : 0,
				duration: 300
			}}
		>
			<div class="flex items-start gap-3">
				<!-- Icon -->
				<IconComponent class="w-5 h-5 {getIconClasses(notification.type)} flex-shrink-0 mt-0.5" />

				<!-- Content -->
				<div class="flex-1 min-w-0">
					<h4 class="text-sm font-medium">
						{notification.title}
					</h4>
					{#if notification.message}
						<p class="text-sm opacity-90 mt-1">
							{notification.message}
						</p>
					{/if}

					<!-- Action Button -->
					{#if notification.action}
						<div class="mt-3">
							<Button
								variant="ghost"
								size="sm"
								onclick={notification.action.onClick}
								class="text-current hover:bg-current/10 p-1 h-auto"
							>
								{notification.action.label}
							</Button>
						</div>
					{/if}
				</div>

				<!-- Close Button -->
				<Button
					variant="ghost"
					size="icon"
					onclick={() => onRemove(notification.id)}
					class="h-6 w-6 text-current hover:bg-current/10 flex-shrink-0"
				>
					<X class="h-3 w-3" />
					<span class="sr-only">Close notification</span>
				</Button>
			</div>
		</div>
	{/each}
</div>
