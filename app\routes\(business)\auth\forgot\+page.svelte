<script lang="ts">
	import { goto } from '$app/navigation';
	import { notificationStore } from '$lib/stores/notificationStore';
	import Button from '$lib/components/ui/Button.svelte';
	import Input from '$lib/components/ui/Input.svelte';
	import Card from '$lib/components/ui/Card.svelte';
	import { ArrowLeft, Mail, CheckCircle } from '@lucide/svelte';

	let email = $state('');
	let isLoading = $state(false);
	let isEmailSent = $state(false);
	let error = $state('');

	const handleSubmit = async () => {
		if (!email) {
			error = 'Please enter your email address';
			return;
		}

		if (!isValidEmail(email)) {
			error = 'Please enter a valid email address';
			return;
		}

		isLoading = true;
		error = '';

		try {
			// Simulate API call for password reset
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			console.log('Password reset requested for:', email);
			
			// Show success state
			isEmailSent = true;
			notificationStore.success('Reset Email Sent', 'Check your email for password reset instructions');
		} catch (err) {
			error = 'Failed to send reset email. Please try again.';
			console.error('Password reset error:', err);
		} finally {
			isLoading = false;
		}
	};

	const handleBackToLogin = () => {
		goto('/auth/login');
	};

	const handleKeyPress = (event: KeyboardEvent) => {
		if (event.key === 'Enter' && !isEmailSent) {
			handleSubmit();
		}
	};

	const isValidEmail = (email: string): boolean => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	};

	const handleTryAgain = () => {
		isEmailSent = false;
		email = '';
		error = '';
	};
</script>

<svelte:head>
	<title>Forgot Password - HRIMS</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-muted/50 via-background to-muted/30 flex items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- RTG Branding -->
		<div class="text-center mb-8">
			<div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
				<span class="text-primary-foreground font-bold text-2xl">RTG</span>
			</div>
			<h1 class="text-3xl font-bold text-foreground mb-2">
				{isEmailSent ? 'Check Your Email' : 'Forgot Password'}
			</h1>
			<p class="text-muted-foreground">
				{isEmailSent 
					? 'We\'ve sent password reset instructions to your email' 
					: 'Enter your email to receive password reset instructions'}
			</p>
		</div>

		<Card class="p-8 shadow-xl">
			{#if !isEmailSent}
				<!-- Password Reset Form -->
				<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
					<div>
						<Input
							type="email"
							placeholder="Enter your email address"
							bind:value={email}
							error={error}
							disabled={isLoading}
							required
							onkeypress={handleKeyPress}
							class="w-full"
						>
							<Mail slot="icon" class="w-4 h-4" />
						</Input>
					</div>

					<Button
						type="submit"
						variant="primary"
						disabled={isLoading || !email}
						class="w-full"
					>
						{#snippet children()}
							{#if isLoading}
								<div class="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin mr-2"></div>
								Sending Reset Email...
							{:else}
								<Mail class="w-4 h-4 mr-2" />
								Send Reset Email
							{/if}
						{/snippet}
					</Button>
				</form>
			{:else}
				<!-- Success State -->
				<div class="text-center space-y-6">
					<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
						<CheckCircle class="w-8 h-8 text-green-600" />
					</div>
					
					<div class="space-y-2">
						<h3 class="text-lg font-semibold text-foreground">Email Sent Successfully</h3>
						<p class="text-muted-foreground">
							We've sent password reset instructions to:
						</p>
						<p class="text-foreground font-medium">{email}</p>
					</div>

					<div class="p-4 bg-muted rounded-lg text-left">
						<h4 class="font-medium text-foreground mb-2">Next Steps:</h4>
						<ul class="text-sm text-muted-foreground space-y-1">
							<li>1. Check your email inbox (and spam folder)</li>
							<li>2. Click the reset link in the email</li>
							<li>3. Create a new password</li>
							<li>4. Sign in with your new password</li>
						</ul>
					</div>

					<div class="space-y-3">
						<Button
							variant="primary"
							onclick={handleBackToLogin}
							class="w-full"
						>
							{#snippet children()}
								<ArrowLeft class="w-4 h-4 mr-2" />
								Back to Login
							{/snippet}
						</Button>
						
						<Button
							variant="outline"
							onclick={handleTryAgain}
							class="w-full"
						>
							{#snippet children()}
								<Mail class="w-4 h-4 mr-2" />
								Send to Different Email
							{/snippet}
						</Button>
					</div>
				</div>
			{/if}

			<!-- Back to Login Link -->
			{#if !isEmailSent}
				<div class="mt-6 text-center">
					<button
						onclick={handleBackToLogin}
						disabled={isLoading}
						class="text-sm text-primary hover:text-primary/80 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 mx-auto"
					>
						<ArrowLeft class="w-4 h-4" />
						Back to Login
					</button>
				</div>
			{/if}
		</Card>

		<!-- Help Text -->
		<div class="mt-6 text-center">
			<p class="text-sm text-muted-foreground">
				Having trouble? Contact your system administrator or IT support.
			</p>
		</div>

		<!-- Footer -->
		<div class="mt-8 text-center">
			<p class="text-xs text-muted-foreground">
				© 2024 Rainbow Tourism Group. All rights reserved.
			</p>
		</div>
	</div>
</div>
