# HRIMS Onboarding — Complete Detailed Style Guide (references `style-guide.dashboard.json`)

**Purpose:**
This document is the canonical, exhaustive style guide for the HRIMS onboarding UI. It *extends* and *references* the primary `style-guide.dashboard.json` (the dashboard tokens are the single source of truth). Wherever you see `$ref` entries below, they point back to token paths in `style-guide.dashboard.json` and should be resolved/merged at build time.

---

## Contents

1. Principles & goals
2. Design tokens (mapping + authoritative values)
3. Color system & accessible usage
4. Typography system
5. Spacing, layout, grids and breakpoints
6. Radi<PERSON>, shadows, elevation
7. Iconography & imagery
8. Motion & interaction
9. Component library (detailed specs)
10. Forms & validation UX patterns
11. Stepper & multi-step form rules (exact visual & interactive spec)
12. Accessibility & localization
13. Tailwind & implementation notes (config + CSS vars)
14. Developer API & tokens merge guidance
15. QA checklist & design handoff

---

# 1. Principles & goals

* **Consistent** — All onboarding pages must strictly reference `style-guide.dashboard.json` tokens. Do not hardcode colors, sizes or spacings. Use tokens and component classes.
* **Accessible** — Meet WCAG AA for color contrast and keyboard interactions. Respect `prefers-reduced-motion`.
* **Mobile-first** — Design and code for small screens first; scale up to desktop.
* **Modular** — Components are composable and configurable via props/variants.
* **Audit & compliant** — Signed documents, legal forms and medical info use restricted styles and clear privacy microcopy.

---

# 2. Design tokens (mapping + authoritative values)

This file extends `style-guide.dashboard.json`. Use `$ref` to point back to canonical values.

```json
{
  "meta": { "extends": "style-guide.dashboard.json" },
  "tokens": {
    "colors": {
      "pageBg": { "$ref": "style-guide.dashboard.json#/tokens/colors/mintBackground" },
      "cardBg": { "$ref": "style-guide.dashboard.json#/tokens/colors/neutral100" },
      "sidebarBg": "rgba(246, 253, 251, 0.6)",
      "primary": { "$ref": "style-guide.dashboard.json#/tokens/colors/primaryTeal" },
      "primaryHover": { "$ref": "style-guide.dashboard.json#/tokens/colors/primaryTealDark" },
      "muted": { "$ref": "style-guide.dashboard.json#/tokens/colors/neutral300" },
      "text": { "$ref": "style-guide.dashboard.json#/tokens/colors/neutral700" },
      "success": { "$ref": "style-guide.dashboard.json#/tokens/colors/successGreen" },
      "error": { "$ref": "style-guide.dashboard.json#/tokens/colors/errorRed" }
    },

    "typography": {
      "body": { "$ref": "style-guide.dashboard.json#/tokens/typography/Inter" },
      "heading": { "$ref": "style-guide.dashboard.json#/tokens/typography/Poppins" },
      "sizes": { "$ref": "style-guide.dashboard.json#/tokens/typography/fontSizes" }
    },

    "spacing": {
      "xs": "4px",
      "sm": "8px",
      "md": "16px",
      "lg": "24px",
      "xl": "48px"
    },

    "radii": {
      "card": "24px",
      "input": "12px",
      "pill": "9999px"
    },

    "shadows": {
      "card": "0 6px 18px rgba(18,22,26,0.06)",
      "soft": "0 2px 8px rgba(18,22,26,0.04)",
      "focus": "0 0 0 4px rgba(31,175,154,0.12)"
    },

    "motion": {
      "duration": 220,
      "easing": "cubic-bezier(.2,.9,.2,1)",
      "stagger": 60
    },

    "layout": {
      "sidebarWidth": "280px",
      "maxContentWidth": "980px",
      "cardPadding": "48px",
      "mobileCardPadding": "20px"
    }
  }
}
```

**Token usage rules**

* Always reference tokens via the token system. If you need a one-off utility, create a new token instead.
* Tokens must use meaningful names (`primary`, `muted`, `card`) not semantic names like `teal-600`.

---

# 3. Color system & accessible usage

**Color roles** (do not use raw hex in components)

* `pageBg` — background of the page (mint in onboarding). Use for full bleed.
* `cardBg` — main card background.
* `sidebarBg` — subtle tint for the left sidebar.
* `primary` — main CTA and active state color.
* `primaryHover` — hover/active variant for primary.
* `muted` — secondary surface borders and helper text.
* `text` — main text color.
* `success`, `error` — status colors.

**Contrast & scales**

* Normal text: contrast ratio >= 4.5:1 against cardBg.
* Large text (>= 18pt / bold): contrast ratio >= 3:1 allowed.
* Buttons: text on CTA must meet contrast with CTA background (>= 4.5:1). If not possible, use alternative color or outline style.

**System colors fallback**

* For unknown locales, use `primary` as provided by `style-guide.dashboard.json`. UI must support admin override of primary color per brand.

---

# 4. Typography system

**Fonts**

* Body: `Inter, system-ui, -apple-system, 'Segoe UI', Roboto` (\$ref above)
* Headings: `Poppins, Inter` (\$ref above)

**Scale**

* `xs` = 12px (0.75rem)
* `sm` = 14px (0.875rem)
* `base` = 16px (1rem)
* `lg` = 18px (1.125rem)
* `xl` = 20px (1.25rem)
* `2xl` = 24px (1.5rem)

**Line height & weights**

* Body: `line-height: 1.5`, weight 400
* Headings: weight 600 for H2/H3; H1 weight 700
* Labels & microcopy: `font-size: sm`, weight 500 for labels, 400 for helper text

**Usage rules**

* Use `heading` for H1/H2; use `body` for paragraph and input text.
* Avoid more than 3 font weights in a single page. Prefer 400, 500, 700.

---

# 5. Spacing, layout, grids and breakpoints

**Grid system**

* Mobile-first grid. Use `grid-cols-1` by default.
* At `md` (≥768px) use `grid-cols-2` for the form area and `grid` for the outer card `grid-cols-[280px_1fr]`.

**Breakpoints (Tailwind defaults)**

* `sm: 640px` `md: 768px` `lg: 1024px` `xl: 1280px`

**Card layout**

* Card padding: `cardPadding` (48px) desktop, `mobileCardPadding` (20px) mobile.
* Max width: `maxContentWidth` 980px.
* Sidebar width: `sidebarWidth` 280px.

**Spacing rules**

* Use token spacing (`xs`, `sm`, `md`, `lg`, `xl`) for margin/padding. Avoid px values in components.
* Vertical rhythm: baseline increments of 8px.

---

# 6. Radii, shadows, elevation

**Radii**

* Cards: 24px
* Inputs: 12px
* Pills / chips: pill token 9999px

**Shadows**

* Card: `card` shadow for primary container.
* Soft: for buttons / popovers.
* Focus: use `focus` shadow token for input focus outlines.

**Elevation rules**

* Only use shadows to imply stacking; don't overuse. Modal > Card > Dropdown > Tooltip in z-index and shadow intensity.

---

# 7. Iconography & imagery

**Icons**

* Use Lucide icons as canonical set. Keep stroke width consistent (2px).
* Use semantic icons only (check, alert, info, calendar). Do not use decorative icons where clarity is important.

**Imagery**

* Photos: use muted, desaturated photos for backgrounds. Never use imagery that competes with content.
* Illustrations: prefer single-color illustrations that can be tinted via `primary` token.

**Avatar**

* Default avatar size: 40px (circle). Use initials fallback with background tints from `primary`/`muted` palette.

---

# 8. Motion & interaction

**Motion tokens**

* `duration`: 220ms
* `easing`: cubic-bezier(.2,.9,.2,1)
* `stagger`: 60ms between fields

**Global motion rules**

* Page transitions: fade + slide (y 8px → 0)
* Card entrance: subtle scale 0.995 → 1 and fade
* Field reveal: staggered opacity + slide-up 8px
* Stepper complete: scale + rotate (complete animation) — keep short
* Button hover: translateY -2px + slight lift

**Accessibility**

* Respect `prefers-reduced-motion`: if user prefers reduced motion, set all durations to 0 and remove non-essential motion.

---

# 9. Component library (detailed specs)

Each component has tokens, class names (Tailwind utilities), states, accessibility notes and variants.

## Button

* Variants: `primary`, `secondary`, `ghost`, `danger`, `link`.
* Base size: height 40–48px; pill radius.
* Primary: background `primary`, text `#fff`, shadow `soft`.
* Hover: use `primaryHover` and `transform translateY(-2px)`.
* Disabled: `opacity: 0.5`, `cursor: not-allowed`.
* Accessibility: `aria-pressed` for toggles, keyboard focus ring using `shadows.focus`.

**Tailwind example**
`btn-primary = py-2 px-6 rounded-full bg-[var(--primary)] text-white shadow-soft hover:bg-[var(--primaryHover)] focus:ring-4 focus:ring-[var(--primary)]/20`.

## Input / Select / Textarea

* Height: 48px; radius 12px.
* Border: 1px solid `muted`.
* Focus: `outline-none ring-2 ring-[var(--primary)]/30` + shadow focus.
* Placeholder: `muted` color.
* Error: border `error`, helper text in `error` color, error icon.
* Accessibility: `label[for]`, `aria-invalid` on error, `aria-describedby` linking helper text.

## Card

* Radius 24px; padding token `cardPadding` desktop, `mobileCardPadding` mobile; shadow `card`.
* Max width 980px; centered with `margin: auto`.

## Modal / Dialog

* Centered, backdrop `rgba(0,0,0,0.4)`, card-style content with `card` tokens.
* Animations: modalVariants (scale + fade).
* Accessibility: trap focus, escape closes, ARIA role `dialog` with `aria-modal="true"`.

## Stepper (left vertical + top horizontal)

* Vertical (desktop): width 280px column with stacked steps.
* Horizontal (mobile): `overflow-x-auto`, compact chips for each step.
* Step sizes: circle 36–40px; label text small.
* States: `upcoming`, `active`, `complete`.
* Animations: active state uses subtle expand/colour change; complete uses rotation animation for the checkmark.

## File Upload

* Drop zone tile with dashed border; hover and drag states animate background tint and border.
* Uploaded file row: filename, size, thumbnail, remove button.
* Accessibility: support keyboard file picker and ARIA live region for upload status.

## Toast / Notifications

* Toast anchored top-right on desktop; bottom on mobile; auto-dismiss with pause-on-hover; accessible via `role="status"`.

## Tooltip

* Use minimal delays (200ms) and short text; accessible using `aria-describedby` for target.

## Table / Lists

* Compact row heights; use zebra striping only if beneficial to readability; support sorting and keyboard navigation.

---

# 10. Forms & validation UX patterns

**Form layout**

* Multi-step form with left stepper (desktop) or top stepper (mobile).
* Use `grid-cols-1 md:grid-cols-2` for field layout. Fields mixing full-width should use `md:col-span-2`.

**Validation**

* Client-side validation: immediate feedback for obvious format errors (email, phone, IBAN) but do not show severe error icons while typing; show inline message on blur or submit.
* Server-side validation: on submit, map server errors to field-level messages and show toast for global errors.

**Error patterns**

* Input border `error` + small error text below input.
* If multiple errors after submit, focus the first invalid input and announce errors via `aria-live="assertive"`.

**Partial saves**

* Auto-save on field change with debounce (500ms). Show "Saved" microcopy for last save time.
* Partial saves should not mark step as complete — only final submit per form marks complete.

**Signatures**

* For e-signature: accept typed name + checkbox for low-assurance, or integrate with DocuSign/HelloSign for high-assurance. Always store `signatureMeta: {type, timestamp, ip, userAgent, documentHash}`.

---

# 11. Stepper & multi-step form rules (exact visual & interactive spec)

**Step structure**

* Each step has: `title`, `subtitle (optional)`, `status` (`upcoming|active|complete|blocked`), `required` boolean.
* Left column shows sequential steps; active step is emphasized and expanded.

**Navigation rules**

* Users can go back to previous completed steps and edit (unless admin locked).
* Users can jump forward only if prerequisites for the next steps are satisfied (e.g., required form uploaded).
* Use `aria-current="step"` for the active step element.

**Progress & completion**

* Visual progress: vertical progress line fills as steps complete (animate scaleY).
* Percent complete computed as: (completed\_required\_steps / total\_required\_steps) \* 100.

---

# 12. Accessibility & localization

**Keyboard**

* Tab order must follow visual order. All interactive elements must be reachable by keyboard.
* Focus ring must be visible and use `shadows.focus` token.

**Screen readers**

* Use semantic HTML and ARIA landmarks (`role="main"`, `role="navigation"` for stepper if needed).
* For multi-step forms, announce step changes with `aria-live` polite region.

**Localization**

* Support LTR and RTL. All tokens should be neutral to direction; component layout must apply `direction` aware utilities.
* Date formats, phone input masks, and addresses must be localized by `country` selection. Provide a `locale` registry and formatters.

**Privacy microcopy**

* For PII/sensitive fields (medical, bank), show small microcopy explaining who can access and retention policy with link to privacy policy.

---

# 13. Tailwind & implementation notes (config + CSS vars)

**Tailwind tokens usage**

* Map tokens to CSS variables in `:root` at build time. Example CSS variables added to `globals.css`:

```css
:root{
  --pageBg: var(--token-pageBg);
  --cardBg: var(--token-cardBg);
  --primary: var(--token-primary);
  --primaryHover: var(--token-primaryHover);
  --muted: var(--token-muted);
  --text: var(--token-text);
  --card-radius: 24px;
}
```

**Tailwind config snippet** (tailwind.config.js)

```js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: 'var(--primary)',
        primaryHover: 'var(--primaryHover)',
        cardBg: 'var(--cardBg)',
      },
      borderRadius: {
        card: '24px',
        input: '12px'
      },
      boxShadow: {
        card: '0 6px 18px rgba(18,22,26,0.06)'
      }
    }
  }
}
```

**CSS variables generation**

* Build step must read `style-guide.dashboard.json` and `onboarding.styles.json` and generate `:root` variables with the precedence: dashboard tokens override defaults, onboarding extends.

---

# 14. Developer API & tokens merge guidance

**Merge strategy**

* Use deep-merge: `dashboardTokens <- onboardingTokens` so onboarding can override values.
* Provide a `tokens` endpoint or compiled JSON file that front-end imports at runtime for theming.

**Runtime theming**

* Support runtime brand overrides by applying CSS variables to `document.documentElement.style.setProperty('--primary', '#...')`.

---

# 15. QA checklist & design handoff

**Design QA**

* [ ] Tokens resolved and variables present in `:root`.
* [ ] Responsive breakpoints validated on 320px → 1440px.
* [ ] Contrast checks passed (WCAG AA) for all UI elements.
* [ ] Motion reduced for `prefers-reduced-motion`.
* [ ] All interactive elements keyboard accessible.

**Developer Handoff**

* Provide: `style-guide.dashboard.json`, `onboarding.styles.json`, compiled `:root` CSS variables, Tailwind config patch, component design tokens mapping, React component skeletons, and sample data fixtures.

---

## Final notes

This style guide is authoritative for onboarding UI implementation. The `style-guide.dashboard.json` remains canonical for brand tokens; onboarding extends it only where necessary. If you want, I will:
