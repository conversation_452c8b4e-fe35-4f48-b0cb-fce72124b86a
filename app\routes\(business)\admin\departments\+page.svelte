<script lang="ts">
	import { authStore } from '$lib/stores/authStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import { Card } from '$lib/components/ui/card/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import Modal from '$lib/components/ui/Modal.svelte';
	import Select from '$lib/components/ui/Select.svelte';
	import { 
		Search, 
		Plus, 
		Edit, 
		Trash2,
		Building,
		Users,
		User,
		MapPin,
		Phone,
		Mail,
		AlertTriangle
	} from '@lucide/svelte';

	const auth = $derived($authStore);
	const user = $derived(auth.user);

	// Mock departments data
	let departments = $state([
		{
			id: 1,
			name: 'Information Technology',
			code: 'IT',
			description: 'Responsible for all technology infrastructure, software development, and digital solutions',
			manager_name: '<PERSON>',
			manager_email: '<EMAIL>',
			location: 'Harare Office - Floor 3',
			phone: '+263 4 123 4567',
			employee_count: 15,
			budget: 250000,
			status: 'active',
			created_date: '2023-01-15'
		},
		{
			id: 2,
			name: 'Human Resources',
			code: 'HR',
			description: 'Manages employee relations, recruitment, training, and organizational development',
			manager_name: 'Mike Johnson',
			manager_email: '<EMAIL>',
			location: 'Harare Office - Floor 2',
			phone: '+263 4 123 4568',
			employee_count: 8,
			budget: 180000,
			status: 'active',
			created_date: '2023-01-15'
		},
		{
			id: 3,
			name: 'Finance',
			code: 'FIN',
			description: 'Handles financial planning, accounting, budgeting, and financial reporting',
			manager_name: 'Emily Brown',
			manager_email: '<EMAIL>',
			location: 'Harare Office - Floor 1',
			phone: '+263 4 123 4569',
			employee_count: 12,
			budget: 200000,
			status: 'active',
			created_date: '2023-01-15'
		},
		{
			id: 4,
			name: 'Marketing',
			code: 'MKT',
			description: 'Develops marketing strategies, brand management, and customer engagement initiatives',
			manager_name: 'David Smith',
			manager_email: '<EMAIL>',
			location: 'Harare Office - Floor 2',
			phone: '+263 4 123 4570',
			employee_count: 10,
			budget: 150000,
			status: 'active',
			created_date: '2023-02-01'
		},
		{
			id: 5,
			name: 'Operations',
			code: 'OPS',
			description: 'Manages day-to-day operations, logistics, and service delivery',
			manager_name: 'Lisa Johnson',
			manager_email: '<EMAIL>',
			location: 'Victoria Falls Office',
			phone: '+263 13 123 456',
			employee_count: 25,
			budget: 300000,
			status: 'active',
			created_date: '2023-01-15'
		}
	]);

	// Modal states
	let showCreateModal = $state(false);
	let showEditModal = $state(false);
	let showDeleteModal = $state(false);
	let selectedDepartment = $state<typeof departments[0] | null>(null);

	// Form data
	let formData = $state({
		name: '',
		code: '',
		description: '',
		manager_name: '',
		manager_email: '',
		location: '',
		phone: '',
		budget: '',
		status: 'active'
	});

	// Form validation
	let errors = $state<Record<string, string>>({});
	let isSubmitting = $state(false);

	// Search and filter
	let searchTerm = $state('');
	let statusFilter = $state('all');

	// Filter options
	const statusOptions = [
		{ value: 'all', label: 'All Status' },
		{ value: 'active', label: 'Active' },
		{ value: 'inactive', label: 'Inactive' }
	];

	// Computed filtered departments
	const filteredDepartments = $derived.by(() => {
		if (!departments || !Array.isArray(departments)) {
			return [];
		}

		return departments.filter(dept => {
			if (!dept) return false;

			const matchesSearch = searchTerm === '' ||
				(dept.name && dept.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
				(dept.code && dept.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
				(dept.manager_name && dept.manager_name.toLowerCase().includes(searchTerm.toLowerCase()));

			const matchesStatus = statusFilter === 'all' || dept.status === statusFilter;

			return matchesSearch && matchesStatus;
		});
	});

	// Form validation
	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		if (!formData.name.trim()) newErrors.name = 'Department name is required';
		if (!formData.code.trim()) newErrors.code = 'Department code is required';
		if (!formData.description.trim()) newErrors.description = 'Description is required';
		if (!formData.manager_name.trim()) newErrors.manager_name = 'Manager name is required';
		if (!formData.manager_email.trim()) newErrors.manager_email = 'Manager email is required';
		if (!formData.location.trim()) newErrors.location = 'Location is required';
		if (formData.budget && parseFloat(formData.budget) < 0) {
			newErrors.budget = 'Budget must be a positive number';
		}

		// Email validation
		if (formData.manager_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.manager_email)) {
			newErrors.manager_email = 'Please enter a valid email address';
		}

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Actions
	const handleCreate = () => {
		formData = {
			name: '',
			code: '',
			description: '',
			manager_name: '',
			manager_email: '',
			location: '',
			phone: '',
			budget: '',
			status: 'active'
		};
		errors = {};
		showCreateModal = true;
	};

	const handleEdit = (department: typeof departments[0]) => {
		selectedDepartment = department;
		formData = {
			name: department.name,
			code: department.code,
			description: department.description,
			manager_name: department.manager_name,
			manager_email: department.manager_email,
			location: department.location,
			phone: department.phone,
			budget: department.budget.toString(),
			status: department.status
		};
		errors = {};
		showEditModal = true;
	};

	const handleDelete = (department: typeof departments[0]) => {
		selectedDepartment = department;
		showDeleteModal = true;
	};

	const handleSubmit = async (isEdit: boolean = false) => {
		if (!validateForm()) {
			notificationStore.error('Validation Error', 'Please fix the errors below');
			return;
		}

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));

			if (isEdit && selectedDepartment) {
				// Update existing department
				const index = departments.findIndex(d => d.id === selectedDepartment.id);
				if (index !== -1) {
					departments[index] = {
						...departments[index],
						...formData,
						budget: parseFloat(formData.budget) || 0
					};
				}
				notificationStore.success('Department Updated', 'Department has been successfully updated');
				showEditModal = false;
			} else {
				// Create new department
				const newDepartment = {
					id: Math.max(...departments.map(d => d.id)) + 1,
					...formData,
					budget: parseFloat(formData.budget) || 0,
					employee_count: 0,
					created_date: new Date().toISOString().split('T')[0]
				};
				departments = [...departments, newDepartment];
				notificationStore.success('Department Created', 'New department has been successfully created');
				showCreateModal = false;
			}
		} catch (error) {
			console.error('Error saving department:', error);
			notificationStore.error('Save Failed', 'Failed to save department');
		} finally {
			isSubmitting = false;
		}
	};

	const confirmDelete = async () => {
		if (!selectedDepartment) return;

		isSubmitting = true;

		try {
			// Simulate API call
			await new Promise(resolve => setTimeout(resolve, 2000));

			// Remove from array
			departments = departments.filter(d => d.id !== selectedDepartment!.id);

			notificationStore.success('Department Deleted', 'Department has been successfully deleted');
			showDeleteModal = false;
		} catch (error) {
			console.error('Error deleting department:', error);
			notificationStore.error('Delete Failed', 'Failed to delete department');
		} finally {
			isSubmitting = false;
		}
	};

	const canManage = $derived(user?.user_role === 'hr_admin' || user?.user_role === 'super_admin');

	// Redirect if user doesn't have permission
	if (!canManage) {
		// In a real app, this would redirect to an unauthorized page
		console.log('Access denied: insufficient permissions');
	}
</script>

<svelte:head>
	<title>Department Management - HRIMS</title>
</svelte:head>

<div class="space-y-6">
	<!-- Page Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-2xl font-bold text-foreground">Department Management</h1>
			<p class="text-muted-foreground">Manage organizational departments and structure</p>
		</div>
		{#if canManage}
			<Button variant="default" onclick={handleCreate}>
				<Plus class="w-4 h-4" />
				Create Department
			</Button>
		{/if}
	</div>

	<!-- Filters -->
	<Card class="p-4">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
			<div class="md:col-span-2">
				<Input
					placeholder="Search departments..."
					bind:value={searchTerm}
					class="w-full"
				>
					<Search slot="icon" class="w-4 h-4" />
				</Input>
			</div>
			<div>
				<Select
					options={statusOptions}
					bind:value={statusFilter}
					placeholder="Filter by status"
				/>
			</div>
		</div>
	</Card>

	<!-- Summary Stats -->
	<div class="grid grid-cols-1 md:grid-cols-4 gap-4">
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Departments</p>
					<p class="text-2xl font-bold text-foreground">{filteredDepartments.length}</p>
				</div>
				<Building class="w-8 h-8 text-muted-foreground" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Employees</p>
					<p class="text-2xl font-bold text-primary">
						{filteredDepartments.reduce((sum, d) => sum + d.employee_count, 0)}
					</p>
				</div>
				<Users class="w-8 h-8 text-primary" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Total Budget</p>
					<p class="text-2xl font-bold text-green-600">
						${filteredDepartments.reduce((sum, d) => sum + d.budget, 0).toLocaleString()}
					</p>
				</div>
				<Building class="w-8 h-8 text-green-600" />
			</div>
		</Card>
		<Card class="p-4">
			<div class="flex items-center justify-between">
				<div>
					<p class="text-sm text-muted-foreground">Active Departments</p>
					<p class="text-2xl font-bold text-blue-600">
						{filteredDepartments.filter(d => d.status === 'active').length}
					</p>
				</div>
				<Building class="w-8 h-8 text-blue-600" />
			</div>
		</Card>
	</div>

	<!-- Departments Grid -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		{#each filteredDepartments as department}
			<Card class="p-6">
				<div class="flex items-start justify-between mb-4">
					<div class="flex items-center gap-3">
						<div class="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
							<span class="text-primary-foreground font-bold text-lg">
								{department.code}
							</span>
						</div>
						<div>
							<h3 class="text-lg font-semibold text-foreground">{department.name}</h3>
							<p class="text-sm text-muted-foreground">{department.code}</p>
						</div>
					</div>
					<Badge class={department.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
						{department.status.charAt(0).toUpperCase() + department.status.slice(1)}
					</Badge>
				</div>

				<p class="text-sm text-muted-foreground mb-4">{department.description}</p>

				<div class="space-y-3 mb-4">
					<div class="flex items-center gap-2 text-sm">
						<User class="w-4 h-4 text-muted-foreground" />
						<span class="text-foreground">{department.manager_name}</span>
					</div>
					<div class="flex items-center gap-2 text-sm">
						<Mail class="w-4 h-4 text-muted-foreground" />
						<span class="text-foreground">{department.manager_email}</span>
					</div>
					<div class="flex items-center gap-2 text-sm">
						<MapPin class="w-4 h-4 text-muted-foreground" />
						<span class="text-foreground">{department.location}</span>
					</div>
					{#if department.phone}
						<div class="flex items-center gap-2 text-sm">
							<Phone class="w-4 h-4 text-muted-foreground" />
							<span class="text-foreground">{department.phone}</span>
						</div>
					{/if}
				</div>

				<div class="flex items-center justify-between pt-4 border-t border-border">
					<div class="flex items-center gap-4 text-sm">
						<div>
							<span class="text-muted-foreground">Employees:</span>
							<span class="font-medium text-foreground">{department.employee_count}</span>
						</div>
						<div>
							<span class="text-muted-foreground">Budget:</span>
							<span class="font-medium text-foreground">${department.budget.toLocaleString()}</span>
						</div>
					</div>
					{#if canManage}
						<div class="flex gap-2">
							<Button variant="outline" size="sm" onclick={() => handleEdit(department)}>
								<Edit class="w-4 h-4" />
								Edit
							</Button>
							<Button variant="destructive" size="sm" onclick={() => handleDelete(department)}>
								<Trash2 class="w-4 h-4" />
								Delete
							</Button>
						</div>
					{/if}
				</div>
			</Card>
		{/each}
	</div>

	{#if filteredDepartments.length === 0}
		<Card class="p-12 text-center">
			<Building class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
			<h3 class="text-lg font-semibold text-foreground mb-2">No departments found</h3>
			<p class="text-muted-foreground">Try adjusting your search criteria.</p>
			{#if canManage}
				<Button variant="default" class="mt-4" onclick={handleCreate}>
					<Plus class="w-4 h-4" />
					Create First Department
				</Button>
			{/if}
		</Card>
	{/if}
</div>

<!-- Create Department Modal -->
<Modal bind:open={showCreateModal} title="Create New Department" size="lg">
	{#snippet children()}
		<form onsubmit={(e) => { e.preventDefault(); handleSubmit(false); }} class="space-y-4">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label for="name">Department Name *</Label>
					<Input
						id="name"
						bind:value={formData.name}
						error={errors.name}
						disabled={isSubmitting}
						required
						placeholder="Information Technology"
					/>
				</div>
				<div>
					<Label for="code">Department Code *</Label>
					<Input
						id="code"
						bind:value={formData.code}
						error={errors.code}
						disabled={isSubmitting}
						required
						placeholder="IT"
					/>
				</div>
				<div class="md:col-span-2">
					<Label for="description">Description *</Label>
					<Textarea
						id="description"
						bind:value={formData.description}
						error={errors.description}
						disabled={isSubmitting}
						required
						rows={3}
						placeholder="Brief description of the department's responsibilities..."
					/>
				</div>
				<div>
					<Label for="manager_name">Manager Name *</Label>
					<Input
						id="manager_name"
						bind:value={formData.manager_name}
						error={errors.manager_name}
						disabled={isSubmitting}
						required
						placeholder="John Doe"
					/>
				</div>
				<div>
					<Label for="manager_email">Manager Email *</Label>
					<Input
						id="manager_email"
						type="email"
						bind:value={formData.manager_email}
						error={errors.manager_email}
						disabled={isSubmitting}
						required
						placeholder="<EMAIL>"
					/>
				</div>
				<div>
					<Label for="location">Location *</Label>
					<Input
						id="location"
						bind:value={formData.location}
						error={errors.location}
						disabled={isSubmitting}
						required
						placeholder="Harare Office - Floor 3"
					/>
				</div>
				<div>
					<Label for="phone">Phone</Label>
					<Input
						id="phone"
						bind:value={formData.phone}
						error={errors.phone}
						disabled={isSubmitting}
						placeholder="+263 4 123 4567"
					/>
				</div>
				<div>
					<Label for="budget">Annual Budget</Label>
					<Input
						id="budget"
						type="number"
						bind:value={formData.budget}
						error={errors.budget}
						disabled={isSubmitting}
						placeholder="250000"
						min="0"
						step="1000"
					/>
				</div>
				<div>
					<Label for="status">Status</Label>
					<Select
						options={[
							{ value: 'active', label: 'Active' },
							{ value: 'inactive', label: 'Inactive' }
						]}
						bind:value={formData.status}
						error={errors.status}
						disabled={isSubmitting}
					/>
				</div>
			</div>
		</form>
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showCreateModal = false} disabled={isSubmitting}>
			Cancel
		</Button>
		<Button variant="default" onclick={() => handleSubmit(false)} disabled={isSubmitting}>
			{isSubmitting ? 'Creating...' : 'Create Department'}
		</Button>
	{/snippet}
</Modal>

<!-- Edit Department Modal -->
<Modal bind:open={showEditModal} title="Edit Department" size="lg">
	{#snippet children()}
		<form onsubmit={(e) => { e.preventDefault(); handleSubmit(true); }} class="space-y-4">
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<Label for="edit_name">Department Name *</Label>
					<Input
						id="edit_name"
						bind:value={formData.name}
						error={errors.name}
						disabled={isSubmitting}
						required
						placeholder="Information Technology"
					/>
				</div>
				<div>
					<Label for="edit_code">Department Code *</Label>
					<Input
						id="edit_code"
						bind:value={formData.code}
						error={errors.code}
						disabled={isSubmitting}
						required
						placeholder="IT"
					/>
				</div>
				<div class="md:col-span-2">
					<Label for="edit_description">Description *</Label>
					<Textarea
						id="edit_description"
						bind:value={formData.description}
						error={errors.description}
						disabled={isSubmitting}
						required
						rows={3}
						placeholder="Brief description of the department's responsibilities..."
					/>
				</div>
				<div>
					<Label for="edit_manager_name">Manager Name *</Label>
					<Input
						id="edit_manager_name"
						bind:value={formData.manager_name}
						error={errors.manager_name}
						disabled={isSubmitting}
						required
						placeholder="John Doe"
					/>
				</div>
				<div>
					<Label for="edit_manager_email">Manager Email *</Label>
					<Input
						id="edit_manager_email"
						type="email"
						bind:value={formData.manager_email}
						error={errors.manager_email}
						disabled={isSubmitting}
						required
						placeholder="<EMAIL>"
					/>
				</div>
				<div>
					<Label for="edit_location">Location *</Label>
					<Input
						id="edit_location"
						bind:value={formData.location}
						error={errors.location}
						disabled={isSubmitting}
						required
						placeholder="Harare Office - Floor 3"
					/>
				</div>
				<div>
					<Label for="edit_phone">Phone</Label>
					<Input
						id="edit_phone"
						bind:value={formData.phone}
						error={errors.phone}
						disabled={isSubmitting}
						placeholder="+263 4 123 4567"
					/>
				</div>
				<div>
					<Label for="edit_budget">Annual Budget</Label>
					<Input
						id="edit_budget"
						type="number"
						bind:value={formData.budget}
						error={errors.budget}
						disabled={isSubmitting}
						placeholder="250000"
						min="0"
						step="1000"
					/>
				</div>
				<div>
					<Label for="edit_status">Status</Label>
					<Select
						options={[
							{ value: 'active', label: 'Active' },
							{ value: 'inactive', label: 'Inactive' }
						]}
						bind:value={formData.status}
						error={errors.status}
						disabled={isSubmitting}
					/>
				</div>
			</div>
		</form>
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showEditModal = false} disabled={isSubmitting}>
			Cancel
		</Button>
		<Button variant="default" onclick={() => handleSubmit(true)} disabled={isSubmitting}>
			{isSubmitting ? 'Updating...' : 'Update Department'}
		</Button>
	{/snippet}
</Modal>

<!-- Delete Confirmation Modal -->
<Modal bind:open={showDeleteModal} title="Delete Department" size="md">
	{#snippet children()}
		{#if selectedDepartment}
			<div class="space-y-4">
				<div class="flex items-center gap-3 p-4 bg-red-50 rounded-lg">
					<AlertTriangle class="w-6 h-6 text-red-600" />
					<div>
						<h4 class="font-medium text-red-900">Are you sure you want to delete this department?</h4>
						<p class="text-sm text-red-700">This action cannot be undone and will affect all employees in this department.</p>
					</div>
				</div>
				
				<div class="p-4 bg-muted rounded-lg">
					<h4 class="font-medium text-foreground mb-2">Department Details</h4>
					<div class="space-y-1 text-sm">
						<div><span class="text-muted-foreground">Name:</span> {selectedDepartment.name}</div>
						<div><span class="text-muted-foreground">Code:</span> {selectedDepartment.code}</div>
						<div><span class="text-muted-foreground">Manager:</span> {selectedDepartment.manager_name}</div>
						<div><span class="text-muted-foreground">Employees:</span> {selectedDepartment.employee_count}</div>
					</div>
				</div>
			</div>
		{/if}
	{/snippet}
	{#snippet footer()}
		<Button variant="outline" onclick={() => showDeleteModal = false} disabled={isSubmitting}>
			Cancel
		</Button>
		<Button variant="destructive" onclick={confirmDelete} disabled={isSubmitting}>
			{isSubmitting ? 'Deleting...' : 'Delete Department'}
		</Button>
	{/snippet}
</Modal>
