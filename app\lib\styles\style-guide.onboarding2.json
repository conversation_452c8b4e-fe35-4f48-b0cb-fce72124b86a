{"meta": {"name": "Onboarding - Style Guide (refined)", "version": "1.1.0", "description": "Refined onboarding style guide with explicit motion token CSS var mappings and small clarifications for frontend teams."}, "colors": {"brand": {"primary": "#F5D6A1", "accent": "#C49A6C", "secondary": "#8C6239", "neutralDark": "#3B2A1A", "white": "#FFFFFF"}}, "gradients": {"primary": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "hero": "linear-gradient(120deg,#F5D6A1 0%,#8C6239 50%,#3B2A1A 100%)"}, "motion": {"tokens": {"duration-xxs": 80, "duration-xs": 120, "duration-sm": 180, "duration-md": 280, "easing-pop": "cubic-bezier(.175,.885,.32,1.275)", "easing-standard": "cubic-bezier(.2,.8,.2,1)"}}, "tokensToCssVars": {"prefix": "--rtg-onb", "mapping": {"color-primary": "colors.brand.primary", "gradient-primary": "gradients.primary", "gradient-hero": "gradients.hero", "motion-duration-sm": "motion.tokens.duration-sm", "motion-duration-md": "motion.tokens.duration-md", "motion-easing-pop": "motion.tokens.easing-pop"}}, "tailwindSnippet": {"explanation": "Extend Tailwind with onboarding tokens and motion variables.", "code": "module.exports = { theme: { extend: { colors: { 'rtg-onb-primary': 'var(--rtg-onb-color-primary)' }, backgroundImage: { 'rtg-onb-hero': 'var(--rtg-onb-gradient-hero)' }, transitionTimingFunction: { 'rtg-pop': 'var(--rtg-onb-motion-easing-pop)' }, transitionDuration: { 'rtg-sm': 'var(--rtg-onb-motion-duration-sm)' } } } }"}, "notes": "This file is intentionally small — canonical tokens live in style-guide.dashboard.json. Use onboarding file for onboarding-specific overrides and motion var mapping."}