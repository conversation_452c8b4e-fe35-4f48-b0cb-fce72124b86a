<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { FileText, Shield, CheckCircle, Eye, Download } from '@lucide/svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const agreements = $derived(store.data.agreements || {});

	// Agreement state
	let agreementData = $state({
		nda: agreements.nda || false,
		policies: agreements.policies || false,
		contract: agreements.contract || false,
		signature: '',
		signedAt: null as Date | null
	});

	let errors = $state<Record<string, string>>({});
	let isSignaturePadOpen = $state(false);

	// Available agreements/policies
	const availableAgreements = [
		{
			id: 'nda',
			title: 'Non-Disclosure Agreement (NDA)',
			description: 'Confidentiality agreement protecting company information',
			required: true,
			url: '/documents/nda.pdf'
		},
		{
			id: 'policies',
			title: 'Company Policies & Code of Conduct',
			description: 'Employee handbook and behavioral guidelines',
			required: true,
			url: '/documents/employee-handbook.pdf'
		},
		{
			id: 'contract',
			title: 'Employment Contract',
			description: 'Terms and conditions of employment',
			required: true,
			url: '/documents/employment-contract.pdf'
		}
	];

	// Validation
	const validateForm = () => {
		const newErrors: Record<string, string> = {};

		if (!agreementData.nda) newErrors.nda = 'NDA acceptance is required';
		if (!agreementData.policies) newErrors.policies = 'Policy acceptance is required';
		if (!agreementData.contract) newErrors.contract = 'Contract acceptance is required';
		if (!agreementData.signature.trim()) newErrors.signature = 'Digital signature is required';

		errors = newErrors;
		return Object.keys(newErrors).length === 0;
	};

	// Handle signature
	const handleSignature = () => {
		if (agreementData.signature.trim()) {
			agreementData.signedAt = new Date();
			notificationStore.add({
				type: 'success',
				message: 'Digital signature captured successfully!'
			});
		}
	};

	// Mock document preview
	const previewDocument = (url: string, title: string) => {
		notificationStore.add({
			type: 'info',
			message: `Opening ${title} for preview...`
		});
		// In a real implementation, this would open a PDF viewer modal
	};

	// Mock document download
	const downloadDocument = (url: string, title: string) => {
		notificationStore.add({
			type: 'success',
			message: `${title} download started`
		});
		// In a real implementation, this would trigger a file download
	};

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle next step
	const handleNext = () => {
		if (validateForm()) {
			// Update store with agreements
			onboardingStore.updatePersonalDetails({ 
				...store.data.personalDetails,
				agreements: {
					nda: agreementData.nda,
					policies: agreementData.policies,
					contract: agreementData.contract,
					signature: agreementData.signature,
					signedAt: agreementData.signedAt
				}
			});

			onboardingStore.completeStep('agreements');
			onboardingStore.setCurrentStep('summary');
			notificationStore.add({
				type: 'success',
				message: 'Agreements signed successfully!'
			});
			goto('/onboarding/summary');
		} else {
			notificationStore.add({
				type: 'error',
				message: 'Please complete all required agreements before continuing.'
			});
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/documents');
	};

	// Check if all agreements are accepted
	const allAgreementsAccepted = $derived(() => {
		return agreementData.nda && agreementData.policies && agreementData.contract && agreementData.signature.trim();
	});
</script>

<svelte:head>
	<title>Agreements - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Agreements & Policies"
		subtitle="Please review and accept the required agreements and policies. These are legally binding documents."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Complete Agreements"
		nextButtonDisabled={!allAgreementsAccepted}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-8">
			<!-- Agreements List -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Required Agreements
				</h3>
				
				<div class="space-y-4">
					{#each availableAgreements as agreement}
						<div class="border border-[#EDE0CF] rounded-xl p-6">
							<div class="flex items-start gap-4">
								<FileText class="w-6 h-6 text-[#C49A6C] flex-shrink-0 mt-1" />
								<div class="flex-1">
									<h4 class="font-semibold text-[#1C1C1C] mb-2">
										{agreement.title}
										{#if agreement.required}
											<span class="text-[#EF4444] ml-1">*</span>
										{/if}
									</h4>
									<p class="text-sm text-[#8A6A52] mb-4">{agreement.description}</p>
									
									<!-- Document Actions -->
									<div class="flex items-center gap-3 mb-4">
										<Button
											type="button"
											variant="outline"
											size="sm"
											onclick={() => previewDocument(agreement.url, agreement.title)}
										>
											<Eye class="w-4 h-4 mr-2" />
											Preview
										</Button>
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onclick={() => downloadDocument(agreement.url, agreement.title)}
										>
											<Download class="w-4 h-4 mr-2" />
											Download
										</Button>
									</div>

									<!-- Agreement Checkbox -->
									<div class="flex items-start gap-3">
										<Checkbox
											id={agreement.id}
											bind:checked={agreementData[agreement.id]}
											class="mt-1"
											aria-describedby={errors[agreement.id] ? `${agreement.id}-error` : undefined}
										/>
										<div class="flex-1">
											<Label 
												for={agreement.id} 
												class="text-sm font-medium text-[#1C1C1C] leading-relaxed cursor-pointer"
											>
												I have read, understood, and agree to the terms and conditions outlined in the {agreement.title}.
											</Label>
											{#if errors[agreement.id]}
												<p id="{agreement.id}-error" class="text-xs text-[#EF4444] mt-1">
													{errors[agreement.id]}
												</p>
											{/if}
										</div>
									</div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Digital Signature -->
			<div class="space-y-6">
				<h3 class="text-lg font-semibold text-[#1C1C1C] border-b border-[#EDE0CF] pb-2">
					Digital Signature
				</h3>
				
				<div class="border border-[#EDE0CF] rounded-xl p-6">
					<div class="flex items-start gap-4">
						<Shield class="w-6 h-6 text-[#10B981] flex-shrink-0 mt-1" />
						<div class="flex-1">
							<h4 class="font-semibold text-[#1C1C1C] mb-2">Electronic Signature</h4>
							<p class="text-sm text-[#8A6A52] mb-4">
								By typing your full name below, you are providing your electronic signature and agreeing to all the above documents.
							</p>
							
							<div class="space-y-2">
								<Label for="signature" class="text-sm font-medium text-[#5C4024]">
									Full Legal Name <span class="text-[#EF4444]">*</span>
								</Label>
								<Input
									id="signature"
									bind:value={agreementData.signature}
									oninput={handleSignature}
									placeholder="Type your full legal name"
									class="onboarding-input {errors.signature ? 'border-[#EF4444]' : ''}"
									aria-invalid={!!errors.signature}
									aria-describedby={errors.signature ? 'signature-error' : 'signature-help'}
								/>
								{#if errors.signature}
									<p id="signature-error" class="text-xs text-[#EF4444]">{errors.signature}</p>
								{:else}
									<p id="signature-help" class="text-xs text-[#8A6A52]">
										This constitutes your legal electronic signature
									</p>
								{/if}
							</div>

							{#if agreementData.signedAt}
								<div class="mt-4 flex items-center gap-2 text-sm text-[#10B981]">
									<CheckCircle class="w-4 h-4" />
									<span>Signed on {agreementData.signedAt.toLocaleString()}</span>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>

			<!-- Legal Notice -->
			<div class="bg-[#FFF7ED] border border-[#F59E0B]/20 rounded-xl p-4">
				<div class="flex items-start gap-3">
					<Shield class="w-5 h-5 text-[#F59E0B] flex-shrink-0 mt-0.5" />
					<div>
						<h4 class="font-medium text-[#92400E] mb-1">Legal Notice</h4>
						<p class="text-sm text-[#92400E] leading-relaxed">
							By providing your electronic signature, you acknowledge that you have read and understood all agreements, 
							and that your electronic signature has the same legal effect as a handwritten signature. 
							These agreements will remain in effect throughout your employment.
						</p>
					</div>
				</div>
			</div>

			<!-- Progress Indicator -->
			{#if allAgreementsAccepted}
				<div class="bg-[#E8F9EE] border border-[#10B981]/20 rounded-xl p-4">
					<div class="flex items-center gap-3">
						<CheckCircle class="w-5 h-5 text-[#10B981]" />
						<p class="text-sm text-[#027A3A]">
							All agreements completed successfully! You can now proceed to the final summary.
						</p>
					</div>
				</div>
			{/if}
		</div>
	</OnboardingCard>
</div>
