<script lang="ts">
	// RTG-themed Select component using shadcn-svelte
	import * as Select from './select/index.js';
	import { Label } from './label/index.js';

	interface Option {
		value: string;
		label: string;
		disabled?: boolean;
	}

	interface Props {
		label?: string;
		placeholder?: string;
		options: Option[];
		value?: string;
		disabled?: boolean;
		required?: boolean;
		error?: string;
		hint?: string;
		fullWidth?: boolean;
		class?: string;
	}

	let {
		label,
		placeholder = 'Select an option...',
		options,
		value = $bindable(),
		disabled = false,
		required = false,
		error,
		hint,
		fullWidth = true,
		class: className = ''
	}: Props = $props();

	const widthClass = fullWidth ? 'w-full' : '';
	const finalClassName = `${widthClass} ${className}`;

	// Generate unique ID for accessibility
	const selectId = `select-${Math.random().toString(36).substring(2, 11)}`;
	
	// Find selected option for display
	const selectedOption = $derived(options.find(opt => opt.value === value));
</script>

<div class={finalClassName}>
	{#if label}
		<Label for={selectId} class="block text-sm font-medium text-foreground mb-2">
			{label}
			{#if required}
				<span class="text-destructive ml-1">*</span>
			{/if}
		</Label>
	{/if}

	<Select.Root bind:value {disabled}>
		<Select.Trigger 
			id={selectId}
			class="w-full {error ? 'border-destructive focus:ring-destructive' : ''}"
			aria-invalid={error ? 'true' : 'false'}
			aria-describedby={error ? `${selectId}-error` : hint ? `${selectId}-hint` : undefined}
		>
			<Select.Value placeholder={placeholder}>
				{selectedOption?.label || placeholder}
			</Select.Value>
		</Select.Trigger>
		
		<Select.Content>
			{#each options as option}
				<Select.Item 
					value={option.value} 
					disabled={option.disabled}
					class="cursor-pointer"
				>
					{option.label}
				</Select.Item>
			{/each}
		</Select.Content>
	</Select.Root>

	{#if hint && !error}
		<p id="{selectId}-hint" class="text-sm text-muted-foreground mt-1">
			{hint}
		</p>
	{/if}

	{#if error}
		<p id="{selectId}-error" class="text-sm text-destructive mt-1">
			{error}
		</p>
	{/if}
</div>
