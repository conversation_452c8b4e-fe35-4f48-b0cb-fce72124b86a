<script lang="ts">
	import type { NavItem, User } from '$lib/types/hr';
	import { page } from '$app/state';
	import {
		Home,
		User as UserIcon,
		Users,
		Calendar,
		Briefcase,
		Folder,
		FileText,
		Inbox,
		Plus,
		Clock
	} from '@lucide/svelte';

	interface Props {
		isOpen: boolean;
		user: User | null;
		onClose: () => void;
	}

	let { isOpen, user, onClose }: Props = $props();

	// Navigation items based on user role
	const getNavItems = (userRole: User['user_role'] | undefined): NavItem[] => {
		const baseItems: NavItem[] = [
			{
				id: 'dashboard',
				label: 'Dashboard',
				href: '/',
				icon: 'dashboard'
			}
		];

		if (userRole === 'employee') {
			return [
				...baseItems,
				{
					id: 'profile',
					label: 'My Profile',
					href: '/profile',
					icon: 'user'
				},
				{
					id: 'leave',
					label: 'Leave Management',
					href: '/leave',
					icon: 'calendar',
					children: [
						{ id: 'leave-apply', label: 'Apply for Leave', href: '/leave/apply', icon: 'plus' },
						{ id: 'leave-balance', label: 'Leave Balance', href: '/leave/balance', icon: 'clock' }
					]
				},
				{
					id: 'documents',
					label: 'My Documents',
					href: '/documents',
					icon: 'folder'
				}
			];
		}

		if (userRole === 'manager' || userRole === 'hr_admin' || userRole === 'super_admin') {
			return [
				...baseItems,
				{
					id: 'employees',
					label: 'Employees',
					href: '/employees',
					icon: 'users'
				},
				{
					id: 'leave-mgmt',
					label: 'Leave Management',
					href: '/leave',
					icon: 'calendar',
					children: [
						{ id: 'leave-requests', label: 'Leave Requests', href: '/leave/requests', icon: 'inbox' },
						{ id: 'leave-calendar', label: 'Leave Calendar', href: '/leave/calendar', icon: 'calendar' }
					]
				},
				{
					id: 'recruitment',
					label: 'Recruitment',
					href: '/recruitment',
					icon: 'briefcase',
					children: [
						{ id: 'requisitions', label: 'Requisitions', href: '/requisitions', icon: 'document' },
						{ id: 'candidates', label: 'Candidates', href: '/candidates', icon: 'users' }
					]
				},
				{
					id: 'contracts',
					label: 'Contracts',
					href: '/contracts',
					icon: 'document'
				}
			];

			// Add admin section for hr_admin and super_admin
			if (userRole === 'hr_admin' || userRole === 'super_admin') {
				baseItems.push({
					id: 'admin',
					label: 'Administration',
					href: '/admin',
					icon: 'settings',
					children: [
						{ id: 'departments', label: 'Departments', href: '/admin/departments', icon: 'building' },
						{ id: 'roles', label: 'Role Simulator', href: '/admin/roles', icon: 'shield' }
					]
				});
			}

			// Add profile at the end
			baseItems.push({
				id: 'profile',
				label: 'Profile Settings',
				href: '/profile',
				icon: 'user'
			});

			return baseItems;
		}

		return baseItems;
	};

	const navItems = getNavItems(user?.user_role);

	const isActiveRoute = (href: string): boolean => {
		return page.url.pathname === href || page.url.pathname.startsWith(href + '/');
	};

	const getIconComponent = (iconName: string) => {
		const iconMap = {
			dashboard: Home,
			user: UserIcon,
			users: Users,
			calendar: Calendar,
			briefcase: Briefcase,
			folder: Folder,
			document: FileText,
			inbox: Inbox,
			plus: Plus,
			clock: Clock
		};
		return iconMap[iconName as keyof typeof iconMap] || FileText;
	};
</script>

<!-- RTG-themed Sidebar using traditional approach -->
<div class="w-64 bg-sidebar border-r border-border flex flex-col h-screen {isOpen ? 'block' : 'hidden'} lg:block rounded-tr-2xl border-t-none">
	<!-- Sidebar Header -->
	<div class="border-border">
		<div class="flex items-center justify-between">
			<button
				onclick={onClose}
				class="lg:hidden p-1 rounded-md text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
				aria-label="Close sidebar"
			>
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
				</svg> 
			</button>
		</div>
	</div>

	<!-- Sidebar Header -->
	<div class="border-t border-border p-4">
		<div class="text-xs text-muted-foreground text-center">
			<p>Rainbow Tourism Group</p>
			<p class="mt-1">HRIMS v1.0</p>
		</div>
	</div>

	<!-- Navigation Menu -->
	<nav class="flex-1 p-4 overflow-y-auto h-9/10">
		<div class="space-y-2">
			{#each navItems as item}
				{@const IconComponent = getIconComponent(item.icon || 'document')}
				<div>
					<a
						href={item.href}
						class="flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 {isActiveRoute(item.href)
							? 'bg-primary text-primary-foreground'
							: 'text-muted-foreground hover:text-foreground hover:bg-muted'}"
					>
						<IconComponent class="w-5 h-5 flex-shrink-0" />
						<span>{item.label}</span>
						{#if item.badge}
							<span class="ml-auto bg-destructive text-destructive-foreground text-xs px-2 py-0.5 rounded-full">
								{item.badge}
							</span>
						{/if}
					</a>

					{#if item.children}
						<div class="ml-8 mt-2 space-y-1">
							{#each item.children as child}
								{@const ChildIconComponent = getIconComponent(child.icon || 'document')}
								<a
									href={child.href}
									class="flex items-center gap-2 px-3 py-1.5 rounded-md text-sm transition-colors duration-200 {isActiveRoute(child.href)
										? 'text-primary bg-primary/10'
										: 'text-muted-foreground hover:text-foreground hover:bg-muted'}"
								>
									<ChildIconComponent class="w-4 h-4 flex-shrink-0" />
									<span>{child.label}</span>
								</a>
							{/each}
						</div>
					{/if}
				</div>
			{/each}
		</div>
	</nav>
</div>
