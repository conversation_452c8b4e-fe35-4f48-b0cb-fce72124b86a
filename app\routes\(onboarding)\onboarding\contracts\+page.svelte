<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { uiStore } from '$lib/stores/uiStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { FileText, Download, PenTool, Check } from '@lucide/svelte';

	// Get store state
	const store = $derived($onboardingStore);
	const contractData = $derived(store.data.contracts || {});

	// Form state
	let isGeneratingContract = false;
	let contractGenerated = false;
	let contractUrl = '';
	let signatureType = 'draw'; // 'draw' or 'type'
	let typedSignature = '';
	let isDrawing = false;
	let isSigned = false;
	let signedAt = '';

	// Canvas for signature drawing
	let signatureCanvas: HTMLCanvasElement;
	let ctx: CanvasRenderingContext2D | null = null;

	const generateContract = async () => {
		isGeneratingContract = true;
		try {
			// Simulate contract generation
			await new Promise(resolve => setTimeout(resolve, 2000));
			contractGenerated = true;
			contractUrl = 'https://mock.pdf/employment-contract.pdf';
			uiStore.showSuccess('Contract Generated', 'Your employment contract has been generated and is ready for review.');
		} catch (error) {
			uiStore.showError('Generation Failed', 'Failed to generate contract. Please try again.');
		} finally {
			isGeneratingContract = false;
		}
	};

	const initCanvas = () => {
		if (signatureCanvas) {
			ctx = signatureCanvas.getContext('2d');
			if (ctx) {
				ctx.strokeStyle = '#1C1C1C';
				ctx.lineWidth = 2;
				ctx.lineCap = 'round';
			}
		}
	};

	const startDrawing = (event: MouseEvent) => {
		if (!ctx) return;
		isDrawing = true;
		const rect = signatureCanvas.getBoundingClientRect();
		ctx.beginPath();
		ctx.moveTo(event.clientX - rect.left, event.clientY - rect.top);
	};

	const draw = (event: MouseEvent) => {
		if (!isDrawing || !ctx) return;
		const rect = signatureCanvas.getBoundingClientRect();
		ctx.lineTo(event.clientX - rect.left, event.clientY - rect.top);
		ctx.stroke();
	};

	const stopDrawing = () => {
		isDrawing = false;
	};

	const clearSignature = () => {
		if (ctx) {
			ctx.clearRect(0, 0, signatureCanvas.width, signatureCanvas.height);
		}
		typedSignature = '';
	};

	const signContract = async () => {
		if (signatureType === 'type' && !typedSignature.trim()) {
			uiStore.showError('Signature Required', 'Please enter your typed signature.');
			return;
		}

		if (signatureType === 'draw' && ctx) {
			const imageData = ctx.getImageData(0, 0, signatureCanvas.width, signatureCanvas.height);
			const hasSignature = imageData.data.some(channel => channel !== 0);
			if (!hasSignature) {
				uiStore.showError('Signature Required', 'Please draw your signature.');
				return;
			}
		}

		try {
			// Simulate contract signing
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			isSigned = true;
			signedAt = new Date().toISOString();

			// Save contract data
			onboardingStore.updateData('contracts', {
				contractUrl,
				signatureType,
				typedSignature: signatureType === 'type' ? typedSignature : '',
				signatureData: signatureType === 'draw' ? signatureCanvas.toDataURL() : '',
				signedAt,
				isSigned: true
			});

			uiStore.showSuccess('Contract Signed', 'Your employment contract has been successfully signed!');
		} catch (error) {
			uiStore.showError('Signing Failed', 'Failed to sign contract. Please try again.');
		}
	};

	const handleNext = () => {
		if (!isSigned) {
			uiStore.showError('Contract Required', 'Please sign your employment contract before continuing.');
			return;
		}

		// Mark step as completed and move to next
		onboardingStore.completeStep('contracts');
		onboardingStore.setCurrentStep('policies');
		goto('/onboarding/policies');
	};

	const handleBack = () => {
		const prevStep = store.steps.find(s => s.id === 'documents');
		if (prevStep) {
			onboardingStore.setCurrentStep('documents');
			goto('/onboarding/documents');
		}
	};

	// Initialize canvas when component mounts
	$effect(() => {
		if (signatureCanvas && signatureType === 'draw') {
			initCanvas();
		}
	});
</script>

<svelte:head>
	<title>Employment Contract - Onboarding</title>
</svelte:head>

<div class="onboarding-card flex">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={(stepId) => {
			const step = store.steps.find(s => s.id === stepId);
			if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
				onboardingStore.setCurrentStep(stepId);
				goto(step.route);
			}
		}}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Employment Contract"
		subtitle="Review and sign your employment contract. This document outlines your role, responsibilities, and terms of employment."
		showBackButton={true}
		showNextButton={true}
		nextButtonText="Continue"
		nextButtonDisabled={!isSigned}
		onBack={handleBack}
		onNext={handleNext}
	>
		<div class="space-y-6">
			<!-- Contract Generation -->
			{#if !contractGenerated}
				<div class="text-center py-8">
					<FileText class="w-16 h-16 text-[#C49A6C] mx-auto mb-4" />
					<h3 class="text-lg font-semibold text-[#1C1C1C] mb-2">Generate Your Contract</h3>
					<p class="text-[#8A6A52] mb-6">
						We'll generate your personalized employment contract based on the information you've provided.
					</p>
					<Button 
						onclick={generateContract}
						disabled={isGeneratingContract}
						class="onboarding-button-primary"
					>
						{#if isGeneratingContract}
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
							Generating Contract...
						{:else}
							<FileText class="w-4 h-4 mr-2" />
							Generate Contract
						{/if}
					</Button>
				</div>
			{:else}
				<!-- Contract Review -->
				<div class="space-y-4">
					<div class="bg-[#FCF8F2] rounded-xl p-6 border border-[#EDE0CF]">
						<div class="flex items-center gap-3 mb-4">
							<FileText class="w-8 h-8 text-[#C49A6C]" />
							<div>
								<h3 class="font-semibold text-[#1C1C1C]">Employment Contract</h3>
								<p class="text-sm text-[#8A6A52]">Rainbow Tourism Group - Employment Agreement</p>
							</div>
						</div>
						
						<div class="flex items-center gap-3">
							<Button variant="outline" class="flex-1">
								<Download class="w-4 h-4 mr-2" />
								Download PDF
							</Button>
							<Button variant="outline">
								View in Browser
							</Button>
						</div>
					</div>

					<!-- Signature Section -->
					<div class="space-y-4">
						<h3 class="text-lg font-semibold text-[#1C1C1C]">Electronic Signature</h3>
						
						<!-- Signature Type Selection -->
						<div class="flex gap-4">
							<label class="flex items-center gap-2 cursor-pointer">
								<input 
									type="radio" 
									bind:group={signatureType} 
									value="draw"
									class="text-[#C49A6C]"
								/>
								<PenTool class="w-4 h-4" />
								Draw Signature
							</label>
							<label class="flex items-center gap-2 cursor-pointer">
								<input 
									type="radio" 
									bind:group={signatureType} 
									value="type"
									class="text-[#C49A6C]"
								/>
								Type Signature
							</label>
						</div>

						{#if signatureType === 'draw'}
							<div class="space-y-2">
								<Label>Draw your signature below:</Label>
								<div class="border-2 border-dashed border-[#EDE0CF] rounded-lg p-4">
									<canvas
										bind:this={signatureCanvas}
										width="400"
										height="150"
										class="border border-[#EDE0CF] rounded bg-white cursor-crosshair w-full"
										onmousedown={startDrawing}
										onmousemove={draw}
										onmouseup={stopDrawing}
										onmouseleave={stopDrawing}
									></canvas>
								</div>
								<Button variant="outline" onclick={clearSignature} size="sm">
									Clear Signature
								</Button>
							</div>
						{:else}
							<div class="space-y-2">
								<Label for="typedSignature">Type your full name as signature:</Label>
								<Input
									id="typedSignature"
									type="text"
									placeholder="Enter your full name"
									bind:value={typedSignature}
									class="onboarding-input font-serif text-lg"
								/>
							</div>
						{/if}

						{#if !isSigned}
							<Button 
								onclick={signContract}
								class="onboarding-button-primary w-full"
								disabled={signatureType === 'type' && !typedSignature.trim()}
							>
								<PenTool class="w-4 h-4 mr-2" />
								Sign Contract
							</Button>
						{:else}
							<div class="bg-green-50 rounded-lg p-4 border border-green-200">
								<div class="flex items-center gap-2 text-green-800">
									<Check class="w-5 h-5" />
									<span class="font-medium">Contract Signed Successfully</span>
								</div>
								<p class="text-sm text-green-600 mt-1">
									Signed on {new Date(signedAt).toLocaleString()}
								</p>
							</div>
						{/if}
					</div>
				</div>
			{/if}
		</div>
	</OnboardingCard>
</div>
