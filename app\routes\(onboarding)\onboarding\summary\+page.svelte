<script lang="ts">
	import { goto } from '$app/navigation';
	import { onboardingStore } from '$lib/stores/onboardingStore';
	import { notificationStore } from '$lib/stores/notificationStore';
	import OnboardingStepper from '$lib/components/onboarding/OnboardingStepper.svelte';
	import OnboardingCard from '$lib/components/onboarding/OnboardingCard.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { 
		CheckCircle, 
		User, 
		Briefcase, 
		CreditCard, 
		Building, 
		FileText, 
		Download,
		Mail,
		Calendar,
		MapPin,
		Phone
	} from '@lucide/svelte';

	// Subscribe to store
	const store = $derived($onboardingStore);
	const { personalDetails, employmentDetails, taxInfo, bankDetails } = $derived(store.data);

	let isSubmitting = $state(false);

	// Summary sections
	const summaryData = $derived(() => [
		{
			title: 'Personal Information',
			icon: User,
			data: personalDetails ? [
				{ label: 'Name', value: `${personalDetails.firstName} ${personalDetails.lastName}` },
				{ label: 'Preferred Name', value: personalDetails.preferredName || 'Same as first name' },
				{ label: 'Email', value: personalDetails.email },
				{ label: 'Phone', value: personalDetails.phone },
				{ label: 'Address', value: personalDetails.address ? 
					`${personalDetails.address.street}, ${personalDetails.address.city}, ${personalDetails.address.country}` : 'Not provided' },
				{ label: 'Emergency Contact', value: personalDetails.emergencyContact ? 
					`${personalDetails.emergencyContact.name} (${personalDetails.emergencyContact.relationship})` : 'Not provided' }
			] : []
		},
		{
			title: 'Employment Details',
			icon: Briefcase,
			data: employmentDetails ? [
				{ label: 'Job Title', value: employmentDetails.jobTitle },
				{ label: 'Department', value: employmentDetails.department },
				{ label: 'Manager', value: employmentDetails.manager },
				{ label: 'Start Date', value: employmentDetails.startDate ? new Date(employmentDetails.startDate).toLocaleDateString() : 'Not set' },
				{ label: 'Employment Type', value: employmentDetails.employmentType },
				{ label: 'Work Location', value: employmentDetails.workLocation }
			] : []
		},
		{
			title: 'Tax & Payroll',
			icon: CreditCard,
			data: taxInfo ? [
				{ label: 'Tax ID', value: taxInfo.taxId },
				{ label: 'Tax Status', value: taxInfo.taxStatus },
				{ label: 'Pay Frequency', value: taxInfo.payFrequency },
				{ label: 'Payslip Format', value: taxInfo.preferredPayslipFormat }
			] : []
		},
		{
			title: 'Banking Information',
			icon: Building,
			data: bankDetails ? [
				{ label: 'Bank Name', value: bankDetails.bankName },
				{ label: 'Account Name', value: bankDetails.accountName },
				{ label: 'Account Number', value: bankDetails.accountNumber ? `****${bankDetails.accountNumber.slice(-4)}` : 'Not provided' },
				{ label: 'SWIFT Code', value: bankDetails.swiftCode || 'Not provided' }
			] : []
		}
	]);

	// Handle step navigation
	const handleStepClick = (stepId: string) => {
		const step = store.steps.find(s => s.id === stepId);
		if (step && (step.status === 'completed' || step.id === store.currentStepId)) {
			onboardingStore.setCurrentStep(stepId);
			goto(step.route);
		}
	};

	// Handle final submission
	const handleSubmit = async () => {
		isSubmitting = true;
		
		try {
			// Simulate API submission
			await new Promise(resolve => setTimeout(resolve, 2000));
			
			// Complete the onboarding process
			onboardingStore.completeStep('summary');
			
			// Show success notification
			notificationStore.add({
				type: 'success',
				message: 'Onboarding completed successfully! Welcome to Rainbow Tourism Group!'
			});

			// Redirect to dashboard or welcome page
			setTimeout(() => {
				goto('/dashboard');
			}, 2000);
			
		} catch (error) {
			notificationStore.add({
				type: 'error',
				message: 'Failed to submit onboarding. Please try again.'
			});
		} finally {
			isSubmitting = false;
		}
	};

	// Handle back step
	const handleBack = () => {
		goto('/onboarding/agreements');
	};

	// Generate welcome pack (mock)
	const downloadWelcomePack = () => {
		notificationStore.add({
			type: 'success',
			message: 'Welcome pack download started!'
		});
	};
</script>

<svelte:head>
	<title>Summary - Onboarding</title>
</svelte:head>

<!-- Main Onboarding Container -->
<div class="onboarding-card grid md:grid-cols-[280px_1fr] grid-cols-1">
	<!-- Left Stepper -->
	<OnboardingStepper 
		steps={store.steps} 
		currentStepId={store.currentStepId}
		onStepClick={handleStepClick}
	/>

	<!-- Right Content -->
	<OnboardingCard
		title="Review & Submit"
		subtitle="Please review all your information below. Once submitted, your onboarding will be complete and you'll be ready to start your journey with us!"
		showBackButton={true}
		showNextButton={true}
		nextButtonText={isSubmitting ? 'Submitting...' : 'Complete Onboarding'}
		nextButtonDisabled={isSubmitting}
		onBack={handleBack}
		onNext={handleSubmit}
	>
		<div class="space-y-8">
			<!-- Completion Status -->
			<div class="bg-gradient-to-r from-[#E8F9EE] to-[#F0FDF4] border border-[#10B981]/20 rounded-2xl p-6">
				<div class="flex items-center gap-4">
					<div class="w-12 h-12 bg-[#10B981] rounded-full flex items-center justify-center">
						<CheckCircle class="w-6 h-6 text-white" />
					</div>
					<div>
						<h3 class="text-lg font-semibold text-[#027A3A]">🎉 Almost Done!</h3>
						<p class="text-[#027A3A]">You've completed all onboarding steps. Review your information and submit to finish.</p>
					</div>
				</div>
			</div>

			<!-- Summary Cards -->
			<div class="grid gap-6">
				{#each summaryData as section}
					{#if section.data.length > 0}
						<Card class="border-[#EDE0CF]">
							<CardHeader class="pb-4">
								<CardTitle class="flex items-center gap-3 text-[#1C1C1C]">
									<svelte:component this={section.icon} class="w-5 h-5 text-[#C49A6C]" />
									{section.title}
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
									{#each section.data as item}
										<div class="space-y-1">
											<dt class="text-sm font-medium text-[#5C4024]">{item.label}</dt>
											<dd class="text-sm text-[#1C1C1C]">{item.value}</dd>
										</div>
									{/each}
								</div>
							</CardContent>
						</Card>
					{/if}
				{/each}
			</div>

			<!-- Documents Status -->
			<Card class="border-[#EDE0CF]">
				<CardHeader class="pb-4">
					<CardTitle class="flex items-center gap-3 text-[#1C1C1C]">
						<FileText class="w-5 h-5 text-[#C49A6C]" />
						Documents & Agreements
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="space-y-3">
						<div class="flex items-center justify-between">
							<span class="text-sm text-[#5C4024]">Required Documents</span>
							<div class="flex items-center gap-2 text-sm text-[#10B981]">
								<CheckCircle class="w-4 h-4" />
								Uploaded & Verified
							</div>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-sm text-[#5C4024]">Employment Agreements</span>
							<div class="flex items-center gap-2 text-sm text-[#10B981]">
								<CheckCircle class="w-4 h-4" />
								Signed & Accepted
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<!-- Next Steps -->
			<Card class="border-[#C49A6C]/20 bg-gradient-to-r from-[#FCF8F2] to-[#FFF7ED]">
				<CardHeader class="pb-4">
					<CardTitle class="text-[#1C1C1C]">What Happens Next?</CardTitle>
				</CardHeader>
				<CardContent>
					<div class="space-y-4">
						<div class="flex items-start gap-3">
							<div class="w-6 h-6 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
								<span class="text-[#1C1C1C] font-semibold text-xs">1</span>
							</div>
							<div>
								<h4 class="font-medium text-[#1C1C1C]">HR Review</h4>
								<p class="text-sm text-[#8A6A52]">Your information will be reviewed by our HR team within 24 hours</p>
							</div>
						</div>
						
						<div class="flex items-start gap-3">
							<div class="w-6 h-6 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
								<span class="text-[#1C1C1C] font-semibold text-xs">2</span>
							</div>
							<div>
								<h4 class="font-medium text-[#1C1C1C]">Account Setup</h4>
								<p class="text-sm text-[#8A6A52]">Your employee accounts and access will be provisioned</p>
							</div>
						</div>
						
						<div class="flex items-start gap-3">
							<div class="w-6 h-6 bg-[#F5D6A1] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
								<span class="text-[#1C1C1C] font-semibold text-xs">3</span>
							</div>
							<div>
								<h4 class="font-medium text-[#1C1C1C]">Welcome Package</h4>
								<p class="text-sm text-[#8A6A52]">You'll receive your welcome package and first-day instructions</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			<!-- Action Buttons -->
			<div class="flex items-center justify-between pt-4">
				<Button
					type="button"
					variant="outline"
					onclick={downloadWelcomePack}
				>
					<Download class="w-4 h-4 mr-2" />
					Download Welcome Pack
				</Button>

				<div class="text-sm text-[#8A6A52]">
					Need help? Contact <a href="mailto:<EMAIL>" class="text-[#C49A6C] hover:underline"><EMAIL></a>
				</div>
			</div>

			<!-- Submission Status -->
			{#if isSubmitting}
				<div class="bg-[#FFF7ED] border border-[#F59E0B]/20 rounded-xl p-4">
					<div class="flex items-center gap-3">
						<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-[#F59E0B]"></div>
						<p class="text-sm text-[#92400E]">Submitting your onboarding information...</p>
					</div>
				</div>
			{/if}
		</div>
	</OnboardingCard>
</div>
