{"meta": {"name": "Training Evaluation Style Guide", "file": "style-guide.training-evaluation.json", "version": "1.0.0", "description": "Evaluation forms, Likert scales, analytics and chart tokens using RTG colors and gradients."}, "tokensReference": {"base": "style-guide.dashboard.json"}, "colors": {"likertActive": "#C49A6C", "likertInactive": "#FCF8F2", "chartPositive": "#10B981", "chartNegative": "#EF4444", "commentBg": "#FFF7ED"}, "gradients": {"satisfaction": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)"}, "components": {"evaluationForm": {"likert": {"scale": 5, "activeBg": "#C49A6C", "inactiveBg": "#FCF8F2"}, "commentBox": {"bg": "#FFF7ED", "border": "1px solid #EAD9BF"}, "submitBtn": {"bg": "#8C6239", "text": "#FFFFFF"}}, "analyticsCards": {"cardBg": "#FFFFFF", "scoreLarge": {"color": "#C49A6C", "weight": 700}}}, "interactions": {"submit": {"thankYouToast": true}, "filter": {"debounceMs": 200}}, "accessibility": {"likert": "Use aria-labels for radio groups; keyboard accessible control"}, "notes": {"mocks": ["/api/evaluations", "/api/training/analytics"], "acceptance": ["Evaluation form records responses in mock store", "Analytics compute averages and display via charts"]}}