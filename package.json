{"name": "hrims", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"motion": "^12.23.12", "zod": "^3.22.4"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@playwright/test": "^1.40.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.21", "bits-ui": "^2.9.4", "clsx": "^2.1.1", "mode-watcher": "^1.1.0", "msw": "^2.0.0", "playwright": "^1.40.0", "postcss": "^8.5.6", "shadcn-svelte": "^1.0.7", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.12", "tw-animate-css": "^1.3.7", "typescript": "^5.0.0", "vite": "^7.0.4", "vitest": "^1.0.0"}, "msw": {"workerDirectory": ["static"]}}