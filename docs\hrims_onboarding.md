# HRIMS Onboarding — Style Guide, JSON Form Schemas & Full Workflow

**Purpose:** This single reference document contains everything required to implement the full HRIMS onboarding experience: (1) Onboarding-specific style tokens (which reference `style-guide.dashboard.json` tokens), (2) component / layout rules to match the provided visual (left stepper + main card), (3) complete JSON Schemas for every onboarding form, and (4) a fully detailed onboarding workflow (triggers, automations, conditional branches, API/webhook contracts, security, and implementation notes).

> **Note:** This file intentionally references tokens from `style-guide.dashboard.json`. Wherever you see `$ref` keys in the token section below, replace with the exact path from your canonical `style-guide.dashboard.json` (or merge at build time). This document is exhaustive — nothing left out.

---

## Table of contents

1. Onboarding style tokens & mapping to `style-guide.dashboard.json`
2. Component & layout spec (pixels, Tailwind classes, accessibility)
3. `onboarding.styles.json` (JSON tokens snippet — meant to `extend` `style-guide.dashboard.json`)
4. Full JSON Schema bundle: `forms-schema.json` (all forms + definitions)
5. Sample filled form instance (example)
6. Complete onboarding workflow (markdown): pre-boarding → day 0/1 → training → 30/60/90 checks → confirmation → offboarding hooks
7. System integration & API/webhook specs
8. Security, privacy, localization, legal constraints
9. QA checklist and implementation notes

---

# 1) Onboarding style tokens & mapping

These tokens should *extend* your existing `style-guide.dashboard.json`. Keep the dashboard as source-of-truth and add onboarding-specific tokens below which `reference` dashboard tokens.

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "meta": {
    "name": "onboarding.styles",
    "extends": "style-guide.dashboard.json"
  },
  "tokens": {
    "colors": {
      "bgPage": { "$ref": "style-guide.dashboard.json#/tokens/colors/mintBackground" },
      "cardBg": { "$ref": "style-guide.dashboard.json#/tokens/colors/neutral100" },
      "primary": { "$ref": "style-guide.dashboard.json#/tokens/colors/primaryTeal" },
      "accent": { "$ref": "style-guide.dashboard.json#/tokens/colors/primaryTealDark" },
      "muted": { "$ref": "style-guide.dashboard.json#/tokens/colors/neutral300" },
      "success": { "$ref": "style-guide.dashboard.json#/tokens/colors/successGreen" },
      "error": { "$ref": "style-guide.dashboard.json#/tokens/colors/errorRed" }
    },
    "typography": {
      "bodyFont": { "$ref": "style-guide.dashboard.json#/tokens/typography/Inter" },
      "headingFont": { "$ref": "style-guide.dashboard.json#/tokens/typography/Poppins" },
      "fontSizes": { "$ref": "style-guide.dashboard.json#/tokens/typography/fontSizes" }
    },
    "radii": {
      "card": "24px",
      "pill": "9999px",
      "input": "12px"
    },
    "shadows": {
      "card": "0 6px 18px rgba(18, 22, 26, 0.06)",
      "soft": "0 2px 8px rgba(18,22,26,0.04)"
    },
    "motion": {
      "duration": "220ms",
      "easing": "cubic-bezier(.2,.9,.2,1)"
    },
    "layout": {
      "sidebarWidth": "280px",
      "cardPadding": "48px",
      "maxContentWidth": "980px"
    }
  }
}
```

> Implementation note: at build time or runtime merge these tokens with `style-guide.dashboard.json`. Prefer the dashboard as canonical.

---

# 2) Component & layout spec — match the attached image

**Overall page layout**
- Page background: `bgPage` (soft mint). Use a full-bleed container with centered card.
- Content card: `cardBg`, corner radius `card` (24px), drop shadow `card`, width: `min(980px, 95vw)`, padding `cardPadding`.
- Left column: fixed 280px stepper sidebar, background slightly lighter than card (`bgCardSidebar` derived from `bgPage` with 6–8% opacity). Rounded on left side (same radius as card). Vertical centered stepper.
- Right column: form area using grid layout: two-column grid for main fields, with rows as needed.

**Tailwind-class mapping examples**
- Outer container: `min-h-screen flex items-center justify-center bg-[var(--bgPage)] p-6`
- Card: `bg-[var(--cardBg)] rounded-[24px] shadow-card w-full max-w-[980px] grid grid-cols-[280px_1fr] overflow-hidden`
- Sidebar: `p-8 border-r border-[var(--muted)]/10`
- Step item: `flex items-center gap-4` + circle token `w-8 h-8 rounded-full bg-[var(--success)] text-white flex items-center justify-center`
- Form area: `p-12` (48px) `grid grid-cols-2 gap-6`
- Full-width input (single column): `col-span-2`

**Card & input styles (component rules)**
- Inputs: full width, height `48px`, border `1px solid neutral300`, border-radius `12px`, padding `0.75rem`, placeholder muted. Focus: `outline-none ring-2 ring-[var(--primary)]/30`.
- Labels: `text-xs font-medium text-neutral700` above inputs.
- Helper microcopy: `text-xs text-neutral400` beneath field when required.
- Error state: border turns `error`, text small & red under input.
- Next button: primary pill, `py-2 px-6 rounded-full bg-[var(--primary)] text-white shadow-soft`, placed bottom-right inside form area. Disabled state: `opacity-50 cursor-not-allowed`.

**Stepper specifics**
- Step states: `completed`, `active`, `upcoming`.
- Completed: filled circle with check icon (white on primary/green), vertical line continues.
- Active: filled primary outline, text bold.
- Upcoming: muted circle (border only) with step number, muted text.

**Accessibility**
- All inputs must have associated `<label for>` and `aria-describedby` when helper text exists.
- Focus order must be linear through DOM — ensure `tabindex=0` default.
- Color contrast: primary & text meet AA at minimum.

---

# 3) `onboarding.styles.json` (export snippet you can import)

```json
{
  "name": "onboarding.styles",
  "version": "1.0",
  "tokens": {
    "colors": {
      "mintBackground": "#E6F9F4",
      "cardBg": "#FFFFFF",
      "primaryTeal": "#1FAF9A",
      "primaryTealDark": "#168C74",
      "neutral100": "#FFFFFF",
      "neutral300": "#D8E6E2",
      "neutral700": "#2B2F33"
    },
    "typography": {
      "body": "Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue'",
      "heading": "Poppins, Inter, system-ui",
      "sizes": { "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem" }
    },
    "radii": { "card": "24px", "input": "12px", "chip": "9999px" },
    "shadows": { "card": "0 6px 18px rgba(18,22,26,0.06)", "soft": "0 2px 8px rgba(18,22,26,0.04)" },
    "motion": { "duration": "220ms", "easing": "cubic-bezier(.2,.9,.2,1)" }
  }
}
```

> Use these tokens to drive CSS variables in your root stylesheet and reference them inside Tailwind config as custom properties.

---

# 4) JSON Schema bundle — `forms-schema.json`

This bundle contains every onboarding form as a JSON Schema (Draft-07 compatible). Each schema includes: `title`, `description`, `type`, `properties`, `required`, `validation` (regex), `ui` hints (layout), `sensitivity` classification, and `access` (who can `view`/`edit`).

> Due to length, you can import the full `definitions` object below as `#/definitions` in your form engine.

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "HRIMS Onboarding Forms Bundle",
  "type": "object",
  "definitions": {
    "PersonalDetails": {
      "title": "Personal Details",
      "description": "Captures the core personal identity fields for a new hire.",
      "type": "object",
      "properties": {
        "firstName": { "type": "string", "minLength": 1, "maxLength": 100 },
        "lastName": { "type": "string", "minLength": 1, "maxLength": 100 },
        "preferredName": { "type": ["string", "null"], "maxLength": 50 },
        "dateOfBirth": { "type": "string", "format": "date" },
        "nationalId": { "type": ["string", "null"], "pattern": "^\\d{6,20}$" },
        "email": { "type": "string", "format": "email" },
        "phone": { "type": "string", "pattern": "^[+]?\\d{7,15}$" },
        "address": {
          "type": "object",
          "properties": {
            "street": { "type": "string" },
            "street2": { "type": ["string", "null"] },
            "city": { "type": "string" },
            "postalCode": { "type": "string" },
            "country": { "type": "string" }
          }
        },
        "emergencyContact": {
          "type": "object",
          "properties": {
            "name": { "type": "string" },
            "relationship": { "type": "string" },
            "phone": { "type": "string", "pattern": "^[+]?\\d{7,15}$" }
          }
        }
      },
      "required": ["firstName","lastName","email","dateOfBirth"],
      "ui": {
        "layout": "two-column",
        "hints": {
          "nationalId": "This field is country-specific. Only show when `country` is set to a region that requires it.`"
        }
      },
      "sensitivity": "PII",
      "access": { "view": ["HR","Manager"], "edit": ["HR","Employee"] }
    },

    "TaxInfo": {
      "title": "Tax & Payroll Info",
      "description": "Tax withholding and payroll setup (locale-aware).",
      "type": "object",
      "properties": {
        "taxCountry": { "type": "string" },
        "taxFormType": { "type": "string" },
        "taxFormFile": { "type": ["string","null"], "format": "binary" },
        "bankAccount": {
          "type": "object",
          "properties": {
            "accountName": { "type": "string" },
            "iban": { "type": ["string","null"] },
            "swift": { "type": ["string","null"] },
            "accountNumber": { "type": ["string","null"] }
          }
        },
        "paymentMethod": { "type": "string", "enum": ["bank_transfer","cheque","cash"] }
      },
      "required": ["taxCountry","paymentMethod"],
      "sensitivity": "Sensitive",
      "access": { "view": ["HR","Payroll"], "edit": ["Employee","HR"] }
    },

    "ConflictOfInterest": {
      "title": "Conflict of Interest Disclosure",
      "description": "Disclosure of outside affiliations, financial interests, or relationships that may conflict.",
      "type": "object",
      "properties": {
        "hasConflict": { "type": "boolean" },
        "details": { "type": ["string","null"], "maxLength": 2000 },
        "disclosedDate": { "type": "string", "format": "date" }
      },
      "required": ["hasConflict"],
      "ui": { "layout": "single-column", "conditional": { "details": "show if hasConflict == true" } },
      "sensitivity": "Confidential",
      "access": { "view": ["HR","Legal"], "edit": ["Employee","HR"] }
    },

    "NDA_IP_Assignment": {
      "title": "Confidentiality / IP Assignment",
      "type": "object",
      "properties": {
        "signed": { "type": "boolean" },
        "signedAt": { "type": ["string","null"], "format": "date-time" },
        "signatureReference": { "type": ["string","null"] }
      },
      "required": ["signed"],
      "sensitivity": "Legal",
      "access": { "view": ["HR","Legal","Manager"], "edit": ["HR"] }
    },

    "MedicalInfo": {
      "title": "Medical / Emergency Information",
      "description": "Only show where legally appropriate. Keep access restricted to HR + designated medical responders.",
      "type": "object",
      "properties": {
        "hasConditions": { "type": "boolean" },
        "conditions": { "type": ["string","null"] },
        "allergies": { "type": ["string","null"] },
        "medication": { "type": ["string","null"] }
      },
      "required": ["hasConditions"],
      "ui": { "privateNotice": "Show only when allowed; treat as restricted data" },
      "sensitivity": "Restricted",
      "access": { "view": ["HR"], "edit": ["Employee","HR"] }
    },

    "EquipmentReceipt": {
      "title": "Equipment & Asset Receipt",
      "type": "object",
      "properties": {
        "items": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "name": { "type": "string" },
              "serial": { "type": "string" },
              "condition": { "type": "string", "enum": ["new","good","used","damaged"] }
            },
            "required": ["name"]
          }
        },
        "issuedBy": { "type": "string" },
        "issuedAt": { "type": "string", "format": "date-time" }
      },
      "required": ["items","issuedBy"],
      "sensitivity": "Operational",
      "access": { "view": ["HR","IT","Manager"], "edit": ["IT","HR"] }
    },

    "BackgroundCheckConsent": {
      "title": "Background Check Consent",
      "type": "object",
      "properties": {
        "consentGiven": { "type": "boolean" },
        "consentAt": { "type": ["string","null"], "format": "date-time" },
        "notes": { "type": ["string","null"] }
      },
      "required": ["consentGiven"],
      "sensitivity": "Legal",
      "access": { "view": ["HR","Legal"], "edit": ["HR"] }
    },

    "PhotoRelease": {
      "title": "Photo / Publicity Consent",
      "type": "object",
      "properties": {
        "consent": { "type": "boolean" },
        "consentAt": { "type": ["string","null"], "format": "date-time" }
      },
      "required": ["consent"],
      "sensitivity": "Personal",
      "access": { "view": ["HR"], "edit": ["Employee","HR"] }
    }
  },
  "ui-hints": {
    "globalLayout": "two-column with sidebar",
    "stepper": "left",
    "submitBehavior": "next-step saves partial & full validation; final submit triggers e-sign and tasks"
  }
}
```

---

# 5) Sample filled form instance (PersonalDetails example)

```json
{
  "firstName": "Jane",
  "lastName": "Doe",
  "preferredName": "Janey",
  "dateOfBirth": "1993-06-02",
  "nationalId": "01010102302",
  "email": "<EMAIL>",
  "phone": "+263772123456",
  "address": { "street": "12 Baker St", "city": "Harare", "postalCode": "0000", "country": "ZW" },
  "emergencyContact": { "name": "John Doe", "relationship": "Brother", "phone": "+263772654321" }
}
```

---

# 6) Complete Onboarding Workflow (detailed)

This section is an exhaustive workflow spec. Use this to implement the state machine and UI flows. Every step lists triggers, actors, side-effects, and acceptance criteria.

## Workflow summary (states)

1. **Offer Accepted** (trigger: Candidate accepts offer) —> create Employee record (status: `preboarding`) and generate Onboarding package.
2. **Pre-boarding** — document collection, tax form, NDA, equipment request, IT provisioning tasks created.
3. **Day 0 / Day 1 (Joining)** — employee signs into HRIMS, completes personal & emergency details, signs agreements, IT hands over equipment.
4. **Orientation & Training** — mandatory training assigned; progress tracked.
5. **Probation Period** — auto-reminders at mid-probation for manager review, 30/60/90 check-ins.
6. **Confirmation / Transition** — manager completes final review; HR updates employment status.
7. **Active Employee** — all onboarding tasks completed.

### State machine keys
- `employee.onboarding.status` enum: `offer_accepted`, `preboarding`, `joining`, `probation`, `confirmed`, `active`, `onboarding_failed`
- `task` entity with `assignee`, `type`, `status`, `dueDate`, `metadata`


## Triggers & CRUD operations

**1) Offer accepted**
- Trigger: Recruiter toggles `Offer accepted` or candidate clicks accept link.
- Actions:
  - Create `Employee` record with `onboarding.status = preboarding`.
  - Generate onboarding checklist (forms to fill) based on `country`, `role`, `contractType`.
  - Send welcome email with login credentials (or invite to set password) — uses templated email.
  - Create tasks: `IT: provision email & account`, `Facilities: prepare desk`, `Payroll: start payroll setup`.
  - Create `document` entries for NDA, offer letter (attach PDF).

**Acceptance**: Employee record exists; initial tasks created with deadlines.


**2) Pre-boarding**
- Components shown to user (employee): PersonalDetails, TaxInfo, NDA, ConflictOfInterest, PhotoRelease (optional), MedicalInfo (if allowed). Each form uses partial saves & validation.
- Rules:
  - Required forms must be completed within `X days` (configurable per org, default 7 days).
  - Show progress UI (left stepper reflects completed steps).
  - Show warnings for regionally required docs (I-9 etc.).
  - If `country != countryOfCompany`, show `visa` doc step.

**Side-effects**:
  - On bank details completion, create a secure payroll `pending` record for payroll team.
  - On NDA signed: set legal flag `nda_signed=true` and attach signature metadata.

**Acceptance**: All required pre-boarding forms uploaded & signed.


**3) IT provisioning**
- On offer accepted, IT task created.
- HRIMS sends webhook to provisioning system (or creates ticket in Jira/ServiceNow) with payload:

```json
{
  "employeeId": "EMP-1234",
  "requestedAccounts": ["email","slack","jira"],
  "requestedAssets": ["laptop","phone"],
  "dueDate": "2025-09-01T09:00:00Z"
}
```

- Accept/Reject: IT updates task via API; status syncs back to HRIMS.

**Acceptance**: IT task `status = done` and equipment recorded in `EquipmentReceipt`.


**4) Day 1 — joining**
- Employee uses provided credentials to login.
- First-time login flow: mandatory password change, accept device policy, 2FA setup (if required).
- Show stepper with `joining` step active. On successful completion of day-one forms, HR marks the employee as `joining_completed`.

**Acceptance**: Employee able to access internal systems; mandatory documents signed.


**5) Orientation & Training**
- Training modules auto-assigned based on `role`.
- LMS integration: push `assignedModules` via LMS API; track `moduleCompleted` events via webhooks.
- Manager & HR get notifications for incomplete training after `N` days.

**Acceptance**: All mandatory modules complete or manager accepts extension.


**6) Probation tracking**
- Default probation length from `employment.contract.probationDays` (e.g., 90 days)
- Auto-scheduled reviews: midpoint (45 days) reminder & final (90 days) review.
- Manager fills `probationReviewForm` (structured) and selects `confirm|extend|terminate`.
- If `terminate`, HR receives termination workflow.

**Acceptance**: Review submitted and status updated.


**7) Confirmation & transition**
- If `confirm`: HR updates `employee.status = confirmed` and triggers benefits enrollment flows (if applicable).
- If `extend`: new probation period set with tasks & new reminders.

**Acceptance**: Employee moved to `active` and benefits enrolled.


## Notifications & reminders
- Use push/email/SMS based on user's preference.
- Important timed reminders:
  - `T+1 day` after offer: reminder to complete pre-boarding (if incomplete).
  - `T+7 days` escalate to HR.
  - `probation.midpoint` manager reminder.


## Conditional branching examples
- If `employee.contractType == "contractor"` then skip benefits & pension forms.
- If `role.requiresDriving` then include `Driving Authorization` form.
- If `country == US` include `I-9` steps; if `US` include W-4 else include local tax forms.


## Data retention & audit
- Keep audit logs of all signatures, views, and downloads with `timestamp`, `ip`, `userAgent`.
- Retention policy: forms & signed documents kept for a minimum per local law: `7 years` or org default.


# 7) System integration & API/webhook specs

**Core endpoints**

- `POST /api/onboarding/start` — start onboarding (payload: candidate->employee mapping)
- `GET /api/onboarding/:employeeId/status` — get onboarding status & task list
- `POST /api/forms/:formKey/save` — partial save for a form
- `POST /api/forms/:formKey/submit` — final submit (runs validation + triggers downstream tasks)
- `GET /api/forms/:formKey/schema` — return JSON schema for UI rendering
- `POST /api/documents/upload` — store document (returns `documentId`)
- `POST /api/webhooks/integrations` — register webhooks (LMS, IT ticketing, payroll)

**Webhook payload example — form submitted**

```json
{
  "event": "form.submitted",
  "employeeId": "EMP-123",
  "formKey": "PersonalDetails",
  "documentIds": ["DOC-23"],
  "timestamp": "2025-08-29T12:34:56Z"
}
```

**Ticketing / IT provisioning webhook**

```json
{
  "event": "it.provision.request",
  "employeeId": "EMP-123",
  "assets": [ { "name":"Macbook Pro 14", "serial":"ABC123" } ],
  "dueDate": "2025-09-01T09:00:00Z"
}
```

**Security header requirements**
- All requests must include `Authorization: Bearer <token>` (OAuth2 JWT recommended)
- Webhooks signed with `X-HRIMS-Signature` using HMAC-SHA256


# 8) Security, privacy, localization & legal constraints

**Data classification**
- `PII` — names, DOB, national ID, addresses: encrypted at rest, access-limited to HR & Manager.
- `Sensitive` — bank details, tax forms: encrypted, access only to HR & Payroll.
- `Restricted` — medical/health: strong access controls, logged access, separate storage table with stricter retention & delete policies.

**Encryption & storage**
- Use server-side encryption with KMS (AWS KMS/GCP KMS). Use envelope encryption for large documents.
- Store documents in object storage (S3, GCS) with `private` ACL and signed URLs for downloads.

**Regional legal behaviors**
- Do not display medical questions unless permitted by region/policy.
- Provide locale-specific tax forms & right-to-work. Maintain a registry per-country of required forms and forbidden questions.

**E-signatures & audit**
- Capture: `userId`, `timestamp`, `ip`, `userAgent`, `signatureData` (if drawn or typed), and `documentHash`.
- Acceptable methods: typed name + checkbox (low risk), drawn signature capture, or external e-sign provider (DocuSign/HelloSign) for high-assurance docs.

**Access governance**
- RBAC model: `Admin`, `HR`, `Payroll`, `Manager`, `Employee`, `IT`, `Legal`.
- For each form, include `access.view` & `access.edit` arrays (see JSON schemas above).


# 9) QA checklist & implementation notes

**Visual QA**
- [ ] Verify left stepper matches proportions: 280px width vs card width 980px.
- [ ] Check card corner radius = 24px and shadow spread = 0 6px 18px rgba(18,22,26,0.06).
- [ ] Ensure grid gaps: `gap-6` (24px) between fields.
- [ ] Check input focus ring color matches `primaryTeal` at 30% opacity.

**Functional QA**
- [ ] Partial save must persist and restore value on page reload.
- [ ] Submit triggers webhook and advances to next step.
- [ ] Required fields validated both client & server-side.
- [ ] Document upload persists to object store and returns secure `documentId`.
- [ ] E-signature metadata recorded in `Signatures` table.

**Performance**
- Lazy-load heavy assets (images, large dropdown datasets like country lists)
- Debounce validation on inputs
- Use server-side pagination for long lists (e.g., many employees)


---

## Appendix A — Database models (simplified)

**Employee**
```sql
CREATE TABLE employees (
  id uuid PRIMARY KEY,
  first_name text,
  last_name text,
  email text UNIQUE,
  onboarding_status text,
  created_at timestamptz default now()
);
```

**Forms** (schema-driven storage)
```sql
CREATE TABLE form_submissions (
  id uuid PRIMARY KEY,
  employee_id uuid REFERENCES employees(id),
  form_key text,
  data jsonb,
  submitted boolean DEFAULT false,
  created_at timestamptz default now()
);
```

**Tasks**
```sql
CREATE TABLE tasks (
  id uuid PRIMARY KEY,
  employee_id uuid REFERENCES employees(id),
  assignee_role text,
  type text,
  status text,
  metadata jsonb,
  due_date timestamptz
);
```

**Documents**
```sql
CREATE TABLE documents (
  id uuid PRIMARY KEY,
  employee_id uuid,
  name text,
  storage_key text,
  content_type text,
  sensitivity text,
  created_at timestamptz default now()
);
```

---

## Appendix B — Export / developer notes

- Export the `forms-schema.json` definitions to your form renderer. UI-consumption expects `ui.hints.layout` values: `two-column` / `single-column`.
- Provide a per-country `formsRegistry.json` that maps required forms to countries. The onboarding engine should call `GET /api/forms/registry?country=ZW` to build the checklist.
- Add an admin UI to toggle which forms are required for which job-classes.

---

### Final notes

This document is intended to be drop-in ready for your HRIMS. It maps visual design (matching the attached image and your dashboard style tokens) to concrete JSON Schemas and a full workflow. If you want, I can:

- Produce the `forms-schema.json` as a separate downloadable file (JSON) for direct import.
- Produce a React/Tailwind component skeleton for the stepper + form layout following these tokens.
- Generate example Postman collections for the APIs above.

Tell me which follow-up you'd like and I will produce it next.

