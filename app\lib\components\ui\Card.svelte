<script lang="ts">
	// RTG-themed Card component using shadcn-svelte
	import * as ShadcnCard from "./card/index.js";
	import type { HTMLAttributes } from 'svelte/elements';

	// Legacy props interface for backward compatibility
	interface LegacyProps extends HTMLAttributes<HTMLDivElement> {
		variant?: 'default' | 'elevated' | 'outlined' | 'kpi';
		padding?: 'none' | 'sm' | 'md' | 'lg';
		children: import('svelte').Snippet;
		header?: import('svelte').Snippet;
		footer?: import('svelte').Snippet;
	}

	let {
		variant = 'default',
		padding = 'md',
		class: className = '',
		children,
		header,
		footer,
		...restProps
	}: LegacyProps = $props();

	// Map legacy variants to additional classes
	const variantClasses = {
		default: '',
		elevated: 'shadow-lg',
		outlined: 'border-2 shadow-none',
		kpi: 'bg-gradient-to-br from-muted to-card shadow-lg'
	};

	// Map legacy padding to classes
	const paddingClasses = {
		none: '',
		sm: 'p-4',
		md: 'p-6', 
		lg: 'p-8'
	};

	const finalClassName = `${variantClasses[variant]} ${className}`;
	const contentPadding = paddingClasses[padding];
</script>

<ShadcnCard.Root class={finalClassName} {...restProps}>
	{#if header}
		<ShadcnCard.Header class={padding !== 'none' ? paddingClasses[padding] : 'px-6 pt-6'}>
			{@render header()}
		</ShadcnCard.Header>
	{/if}
	
	<ShadcnCard.Content class={contentPadding}>
		{@render children()}
	</ShadcnCard.Content>
	
	{#if footer}
		<ShadcnCard.Footer class={padding !== 'none' ? paddingClasses[padding] : 'px-6 pb-6'}>
			{@render footer()}
		</ShadcnCard.Footer>
	{/if}
</ShadcnCard.Root>
