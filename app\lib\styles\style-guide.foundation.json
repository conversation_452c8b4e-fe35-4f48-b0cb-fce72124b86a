{"meta": {"name": "Foundation / Cross-cutting UI Style Guide", "file": "style-guide.foundation.json", "version": "1.0.0", "description": "Global layout, authentication, app shell, notification, and document-upload UI tokens and component rules. Uses dashboard tokens and RTG palette."}, "tokensReference": {"base": "style-guide.dashboard.json", "notes": "Use the dashboard file as the single source for color, gradient and type tokens."}, "colors": {"brandPrimary": "#F5D6A1", "brandAccent": "#C49A6C", "brandSecondary": "#8C6239", "neutralDark": "#3B2A1A", "neutralLight": "#F9F5F0", "textDefault": "#1C1C1C", "muted": "#8A6A52"}, "gradients": {"primary": "linear-gradient(135deg,#F5D6A1 0%,#C49A6C 100%)", "softPrimary": "linear-gradient(180deg, rgba(245,214,161,0.08), rgba(245,214,161,0.02))"}, "spacing": {"gutter": 24, "cardPadding": 20, "formGap": 12}, "components": {"auth": {"card": {"bg": "#FFFFFF", "radius": 12, "padding": 24, "shadow": "elevation-2"}, "titleStyle": {"fontSize": 22, "weight": 600, "color": "#1C1C1C"}, "oauthButton": {"microsoft": {"bg": "#2B579A", "text": "#FFFFFF", "icon": "#FFFFFF", "hover": "#264F8A"}}, "helperText": {"color": "#8A6A52", "fontSize": 13}}, "fileUpload": {"dropzone": {"height": 140, "bg": "#FCF8F2", "border": "2px dashed #EAD9BF", "radius": 12, "iconSize": 40}, "preview": {"thumbSize": 88, "metaColor": "#8A6A52", "progressFill": "#C49A6C"}}, "toast": {"success": {"bg": "#E8F9EE", "text": "#027A3A", "iconBg": "#10B981"}, "error": {"bg": "#FFF1F0", "text": "#9B2B2B"}}, "modal": {"overlay": "rgba(28,28,28,0.45)", "panel": {"radius": 14, "bg": "#FFFFFF", "padding": 24, "entrance": "slide-up+fade"}}}, "states": {"hoverTransition": "transform 180ms cubic-bezier(.2,.8,.2,1)", "focusRing": "0 0 0 6px rgba(196,154,108,0.06)"}, "accessibility": {"reducedMotion": "Respect prefers-reduced-motion: reduce/disable non-essential animations.", "contrastCheck": "Ensure text on primary (#F5D6A1) uses dark text (#1C1C1C) for minimum AA where possible."}, "tailwindSnippet": {"explain": "Extend theme with foundation tokens.", "code": "theme: { extend: { colors: { 'rtg-primary': '#F5D6A1', 'rtg-accent': '#C49A6C' }, borderRadius: { 'rtg-card': '12px' } } }"}, "notes": {"dev": "All cross-cutting components must reference tokens via CSS vars (see tokensToCssVars in dashboard). For frontend-first, MSW should simulate uploads returning status 'pending_scan' or 'clean' and set document badges accordingly."}}