<script lang="ts">
  import { cn } from '$lib/utils';
  import { setContext } from 'svelte';
  
  interface Props {
    value?: string;
    onValueChange?: (value: string) => void;
    class?: string;
    children?: import('svelte').Snippet;
  }
  
  let { value = $bindable(), onValueChange, class: className, children, ...restProps }: Props = $props();
  
  // Create context for child components
  setContext('tabs', {
    get value() { return value; },
    setValue: (newValue: string) => {
      value = newValue;
      onValueChange?.(newValue);
    }
  });
</script>

<div
  class={cn("w-full", className)}
  {...restProps}
>
  {#if children}
    {@render children()}
  {/if}
</div>
